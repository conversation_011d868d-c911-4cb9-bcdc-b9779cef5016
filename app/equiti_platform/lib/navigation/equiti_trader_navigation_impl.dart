import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:hub/hub.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';

class EquitiTraderNavigationImpl extends EquitiTraderNavigation {
  EquitiTraderNavigationImpl();

  @override
  void navigateToSymbols() {
    diContainer<EquitiNavigatorBase>().set([
      EquitiTraderRouteSchema.navBarRoute.label,
    ]);
  }

  @override
  void navigateToProductDetail({
    required SymbolDetailViewModel symbolDetail,
    required String accountNumber,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.symbolsDetailRoute.label,
      arguments: SymbolDetailArgs(
        symbolDetail: symbolDetail,
        accountNumber: accountNumber,
      ),
    );
  }

  @override
  void navigateToLogin({String? email}) {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      EquitiTraderRouteSchema.loginRoute.label,
      EquitiTraderRouteSchema.loginRoute.label,
    );
  }

  void navigateToTradingPrefrences() {
    print('Navigate to trading preferences');
  }

  @override
  void navigateToPortfolio({TradeConfirmationResult? result}) {
    diContainer<EquitiNavigatorBase>().globalData = {
      EquitiTraderRouteSchema.navBarRoute.label: result,
    };
    diContainer<EquitiNavigatorBase>().popUntil(
      EquitiTraderRouteSchema.navBarRoute.label,
    );
  }

  @override
  void navigateToPerformance({TradeConfirmationResult? result}) {
    print('Navigate to Performance');
  }

  @override
  navigateToSignUpOptions() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.signupOptionsRoute.label,
      OnboardingRouteSchema.signupOptionsRoute.label,
    );
  }

  @override
  void navigateToLoginOptions() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.loginOptionsRoute.label,
      OnboardingRouteSchema.loginOptionsRoute.label,
    );
  }

  @override
  void navigateToHub() {
    diContainer<EquitiNavigatorBase>().set([HubRouteSchema.hubRoute.label]);
  }

  @override
  void navigateToSwitchAccounts() {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      EquitiTraderRouteSchema.switchAccountRoute.label,
    );
  }

  @override
  void navigateToDepositOptions({
    required DepositFlowConfig depositFlowConfig,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositOptionsRoute.label,
      arguments: depositFlowConfig,
    );
  }

  @override
  void navigateToWithdrawOptions() {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawOptionsRoute.label,
    );
  }

  @override
  void goToTransferFundsScreen(String originRoute) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.transferFundsScreen.label,
      arguments: originRoute,
    );
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.createAccountMainRoute.label,
      arguments: CreateAccountMainArgs(createAccountFlow: createAccountFlow),
    );
  }
}
