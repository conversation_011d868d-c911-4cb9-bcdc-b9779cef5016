import 'package:duplo/duplo.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/config/equiti_env.dart';
import 'package:equiti_platform/debug/draggable_fab.dart';
import 'package:equiti_platform/debug/network_logging_bottom_sheet.dart';
import 'package:equiti_platform/deep_links/deep_link_handler.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_platform/di/di_initializer.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_it/get_it.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:custom_action_keyboard/custom_action_keyboard.dart';

Future<void> runMainApp(GetIt diContainer, AppConfig config) async {
  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await configureDependencies(diContainer, env: config.env.name);
  await EquitiLocalizationManager.init();
  EquitiLocalizationManager.update();
  // todo (Abed): Uncomment post firebase notification fix for different envs
  // await FirebaseManagerBase.initialize(config.firebaseEnv);

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((_) {
    diContainer<ReporterBase>().start(
      app: EquitiApp(config: config),
      extraData: {"sentry_dsn_url": config.sentryDSNUrl},
    );
  });
}

class EquitiApp extends StatefulWidget {
  const EquitiApp({super.key, required this.config});

  final AppConfig config;

  @override
  State<EquitiApp> createState() => _EquitiAppState();
}

class _EquitiAppState extends State<EquitiApp> {
  late String nextScreenToShow;

  late final EquitiNavigatorBase _navigator;
  late final RouterDelegate<Object> _routerDelegate;
  late final RouteInformationParser<Object> _routeInformationParser;

  @override
  void initState() {
    super.initState();
    _navigator = diContainer<EquitiNavigatorBase>();
    _routerDelegate = _navigator.routerDelegate;
    _routeInformationParser = _navigator.routeInformationParser;

    // Initialize deep link handler
    _initializeDeepLinks();

    screenDecider();
  }

  @override
  void dispose() {
    DeepLinkHandler.dispose();
    super.dispose();
  }

  /// Initialize deep link handling
  Future<void> _initializeDeepLinks() async {
    try {
      await DeepLinkHandler.initialize();
    } catch (e) {
      debugPrint('Failed to initialize deep links: $e');
    }
  }

  void _init() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navigator.pushReplacement(nextScreenToShow);
      FlutterNativeSplash.remove();

      // Add debug FAB overlay in non-production environments
      if (widget.config.env != EquitiEnv.prod) {
        _addDebugFABOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: diContainer<ThemeManager>(), // Listen to theme changes
      builder: (themeContext, _) {
        return ListenableBuilder(
          listenable: diContainer<LocaleManager>(), // Listen to locale changes
          builder: (localeContext, _) {
            return MaterialApp.router(
              locale: diContainer<LocaleManager>().locale,
              routerDelegate: _routerDelegate,
              routeInformationParser: _routeInformationParser,
              theme: ThemeData(
                useMaterial3: true,
                actionIconTheme: ActionIconThemeData(
                  backButtonIconBuilder: (backButtonBuilderContext) {
                    final theme = backButtonBuilderContext.duploTheme;
                    return Assets.images
                        .arrowLeftDirectional(backButtonBuilderContext)
                        .svg(
                          colorFilter: ColorFilter.mode(
                            theme.foreground.fgPrimary,
                            BlendMode.srcIn,
                          ),
                        );
                  },
                ),
              ),
              supportedLocales: EquitiLocalization.supportedLocales,
              localizationsDelegates: EquitiLocalization.localizationsDelegates,
              builder: (builderContext, child) {
                final duploThemeData =
                    diContainer<ThemeManager>().isDarkMode
                        ? DuploThemeData.dark()
                        : DuploThemeData.light();

                final locale = Localizations.localeOf(builderContext);

                return DuploTheme(
                  data: duploThemeData,
                  child: DuploTextStyles(
                    locale: locale,

                    ///This widget observes keyboard visibility using a stream (keyboardVisibilityStream)
                    ///and updates the UI accordingly. It adjusts padding and displays a
                    /// Done button at the bottom of the screen when the keyboard is visible.
                    child: StreamBuilder(
                      stream:
                          diContainer<CustomActionKeyboard>()
                              .keyboardVisibilityStream,
                      builder: (ctx, value) {
                        // Get the current visibility status, default to false if null
                        final isVisible = value.data ?? false;
                        final theme = DuploTheme.of(ctx);
                        final localization = EquitiLocalization.of(ctx);
                        return Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            // Shifts the main child upward when keyboard is visible
                            AnimatedPadding(
                              padding: EdgeInsets.only(
                                bottom: isVisible ? 50 : 0,
                              ),
                              duration: Duration(milliseconds: 500),
                              child: child!,
                            ),
                            // Custom Done button shown at bottom
                            CustomDoneButtonStripWidget(
                              isKeyboardVisible: isVisible,
                              textColor: theme.text.textPrimary,
                              backgroundColor: theme.background.bgTertiary,
                              text: localization.duplo_done_button,
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  /// Adds debug FAB overlay using Navigator's overlay
  void _addDebugFABOverlay() {
    final navigatorKey = diContainer<GlobalKey<NavigatorState>>();
    final overlay = navigatorKey.currentState?.overlay;

    if (overlay != null) {
      final overlayEntry = OverlayEntry(
        builder:
            (context) => DraggableFAB(
              onFabPressed: () {
                NetworkLoggingSheet.show(context);
              },
            ),
      );

      overlay.insert(overlayEntry);
    }
  }

  void screenDecider() async {
    diContainer<ThemeManager>().loadThemeWithSystemFallback(context);
    final TokenManager manager = diContainer<TokenManager>();
    final tokenState = await manager.getState();
    if (tokenState == TokenManagerState.authenticated) {
      nextScreenToShow = OnboardingRouteSchema.progressTrackerPage.label;
    } else if (tokenState == TokenManagerState.loggedOutUser) {
      nextScreenToShow = OnboardingRouteSchema.welcomeRoute.label;
    } else if (tokenState == TokenManagerState.noUser) {
      nextScreenToShow = OnboardingRouteSchema.welcomeRoute.label;
    }
    diContainer<ThemeManager>().loadThemeWithSystemFallback(context);
    _init();
  }
}
