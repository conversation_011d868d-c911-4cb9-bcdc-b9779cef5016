import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_platform/di/interceptor_wrapper_values.dart';
import 'package:equiti_secure_storage/equiti_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:monitoring/monitoring.dart';
import 'package:network_logging/network_logging.dart';
import 'package:preferences/preferences.dart';
import 'package:socket_client/socket_client.dart';

@module
abstract class ApiModule {
  @Named('SocketBaseUrl')
  @singleton
  String socketBaseUrl(AppConfig appConfig) => appConfig.socketBaseUrl;

  @Named('SocketBaseDemoUrl')
  @singleton
  String demoSocketBaseUrl(AppConfig appConfig) => appConfig.socketBaseDemoUrl;

  @Named('TradeBaseUrl')
  @singleton
  String tradeBaseUrl(AppConfig appConfig) => appConfig.baseUrl;

  @Named('TradeBaseDemoUrl')
  @singleton
  String tradeBaseDemoUrl(AppConfig appConfig) => appConfig.baseDemoUrl;

  @Singleton(as: AuthService)
  OktaAuthService authService(LoggerBase logger, AppConfig appConfig) =>
      OktaAuthService(appConfig.authConfig, logger);

  @Named('mobileBffAuthInterceptor')
  @lazySingleton
  AuthInterceptor mobileBffAuthInterceptor(TokenManager tokenManager) {
    final interceptor = AuthInterceptor(
      dioInstanceName: 'mobileBffApiClient',
      onGetToken: () => tokenManager.getAccessToken(),
      onTokenRefresh: () async {
        await tokenManager.refreshAccessToken();
      },
      onNavigateToLogin: () {
        diContainer<SecureStorage>().clear().then((_) {
          diContainer<EquitiTraderNavigation>().navigateToLogin();
        });
      },
    );
    return interceptor;
  }

  @lazySingleton
  AuthInterceptor authInterceptor(TokenManager tokenManager) {
    final interceptor = AuthInterceptor(
      onGetToken: () => tokenManager.getAccessToken(),
      onTokenRefresh: () async {
        await tokenManager.refreshAccessToken();
      },
      onNavigateToLogin: () {
        diContainer<SecureStorage>().clear().then((_) {
          diContainer<EquitiTraderNavigation>().navigateToLogin();
        });
      },
    );
    return interceptor;
  }

  @lazySingleton
  TokenManager tokenManager(
    SecureStorage secureStorage,
    EquitiPreferences preferences,
    RefreshTokenRepository refreshTokenRepository,
  ) => TokenManager(secureStorage, preferences, refreshTokenRepository);

  @injectable
  RefreshTokenRepository refreshTokenRepository(AuthService authService) =>
      RefreshTokenRepository(authService);

  @lazySingleton
  CurlInterceptor curlInterceptor() => const CurlInterceptor();

  @Named('mobileBffDioBuilder')
  @lazySingleton
  DioBuilder mobileBffDioBuilder(
    PrettyDioLogger dioLogger,
    AppConfig config,
    @Named('mobileBffAuthInterceptor') AuthInterceptor authInterceptor,
    CurlInterceptor curlInterceptor,
    NetworkLoggerInterceptorBase networkLogger,
  ) =>
      DioBuilder()
          .setBaseUrl(config.mobileBffBaseUrl)
          // .addInterceptor(dioLogger)
          .addInterceptor(networkLogger)
          .addInterceptor(authInterceptor)
          .addInterceptor(curlInterceptor)
          .addInterceptor(
            interceptorWrapper(
              InterceptorWrapperValues.instance.cachedEndpoints,
            ),
          )
          .withNativeAdapter()
          .withReporter();

  @Named('mobileBffApiClient')
  @lazySingleton
  ApiClientBase mobileBffApiClient(
    @Named('mobileBffDioBuilder') DioBuilder diobuilder,
  ) => DioApiClient(diobuilder.build());

  @lazySingleton
  DioBuilder dioBuilder(
    PrettyDioLogger dioLogger,
    @Named('TradeBaseUrl') String baseUrl,
    AuthInterceptor authInterceptor,
    CurlInterceptor curlInterceptor,
    NetworkLoggerInterceptorBase networkLogger,
  ) =>
      DioBuilder()
          .setBaseUrl(baseUrl)
          // .addInterceptor(dioLogger)
          .addInterceptor(authInterceptor)
          .addInterceptor(curlInterceptor)
          .addInterceptor(networkLogger)
          .withNativeAdapter()
          .withReporter();

  @lazySingleton
  ApiClientBase apiClient(DioBuilder diobuilder) =>
      DioApiClient(diobuilder.build());

  @singleton
  TokenInterceptor tokenInterceptor(TokenManager tokenManager) =>
      TokenInterceptor(
        tokenProvider: () async => await tokenManager.getAccessToken() ?? "",
      );

  @lazySingleton
  SocketClient socketClient(
    LoggerBase logger,
    @Named('SocketBaseUrl') String socketBaseUrl,
    TokenInterceptor tokenInterceptor,
  ) {
    return SocketClient(
      logger,
      options: SocketConnectionOptions(baseUrl: socketBaseUrl),
      interceptors: [tokenInterceptor],
    );
  }
}
