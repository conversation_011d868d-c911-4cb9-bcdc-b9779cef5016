import 'package:flutter/material.dart';
import 'package:host/src/di/di_container.dart';
import 'package:host/src/presentation/displayable_component.dart';
import 'package:host/src/presentation/screen_detail.dart';
import 'package:theme_manager/theme_manager.dart';

class ScreenListWidget extends StatefulWidget {
  const ScreenListWidget({super.key, required this.displayableComponent});

  final List<DisplayableComponent> displayableComponent;

  @override
  State<ScreenListWidget> createState() => _ScreenListWidgetState();
}

class _ScreenListWidgetState extends State<ScreenListWidget> {
  late TextEditingController _controller;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.addListener(() {
      setState(() {
        _searchQuery = _controller.text;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  List<DisplayableComponent> get _filteredComponents {
    if (_searchQuery.isEmpty) return widget.displayableComponent;
    return widget.displayableComponent
        .where(
          (element) =>
              element.title.toLowerCase().contains(_searchQuery.toLowerCase()),
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(backgroundColor: Colors.grey[200], elevation: 0),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _controller,
              keyboardAppearance:
                  diContainer<ThemeManager>().isDarkMode
                      ? Brightness.dark
                      : Brightness.light,
              decoration: const InputDecoration(
                labelText: 'Search',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          Expanded(
            child: ListView(
              children:
                  _filteredComponents
                      .map(
                        (element) => ListTile(
                          title: Text(element.title),
                          onTap: () {
                            if (element.children != null) {
                              Navigator.of(context).push(
                                MaterialPageRoute<void>(
                                  builder:
                                      (pageRouteContext) =>
                                          innerScreenList(element.children!),
                                  settings: const RouteSettings(
                                    name: 'screen_list',
                                  ),
                                ),
                              );
                            } else if (element.onTap != null) {
                              Navigator.of(context).push(
                                MaterialPageRoute<void>(
                                  builder:
                                      (pageRouteContext) =>
                                          ScreenDetail(child: element.onTap!()),
                                  settings: const RouteSettings(
                                    name: 'screen_detail',
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                      )
                      .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget innerScreenList(List<DisplayableComponent> displayableElements) {
    return ScreenListWidget(displayableComponent: displayableElements);
  }
}
