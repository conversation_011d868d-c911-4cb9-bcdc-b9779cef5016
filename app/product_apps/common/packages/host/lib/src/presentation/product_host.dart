import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:host/src/debug_drawer/debug_drawer.dart';
import 'package:host/src/debug_drawer/debug_drawer_banner.dart';
import 'package:host/src/di/di_container.dart';
import 'package:host/src/navigation/debug_banner_navigator_observer.dart';
import 'package:host/src/presentation/displayable_component.dart';
import 'package:host/src/presentation/screen_list_widget.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:custom_action_keyboard/custom_action_keyboard.dart';

class ProductHost extends StatelessWidget {
  ProductHost({
    super.key,
    required this.supportedLocales,
    required this.localizationsDelegates,
  });

  List<DisplayableComponent> get displayableComponents => [];
  String get appName => 'Example Product Host';
  final List<Locale> supportedLocales;
  final List<LocalizationsDelegate<Object?>> localizationsDelegates;
  final localeModel = diContainer<LocaleManager>();

  @override
  Widget build(BuildContext context) {
    final navigatorKey = diContainer<GlobalKey<NavigatorState>>();
    return ListenableBuilder(
      listenable: localeModel,
      builder: (buildeContext, state) {
        return MaterialApp(
          localizationsDelegates: localizationsDelegates,
          supportedLocales: supportedLocales,
          navigatorObservers: [
            diContainer<DebugBannerNavigatorObserver>(),
            diContainer<PerformanceNavigatorObserver>(),
          ],
          navigatorKey: navigatorKey,
          title: appName,
          locale: localeModel.locale,
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
            useMaterial3: true,
          ),
          home: ScreenListWidget(displayableComponent: displayableComponents),
          debugShowCheckedModeBanner: false,
          builder: (builderContext, child) {
            final duploThemeData =
                diContainer<ThemeManager>().isDarkMode
                    ? DuploThemeData.dark()
                    : DuploThemeData.light();

            // Get the current locale
            final locale = Localizations.localeOf(builderContext);

            return DuploTheme(
              data: duploThemeData,
              child: DuploTextStyles(
                locale: locale,
                child: StreamBuilder(
                  stream:
                      diContainer<CustomActionKeyboard>()
                          .keyboardVisibilityStream,
                  builder: (ctx, value) {
                    final isVisible = value.data ?? false;
                    final theme = DuploTheme.of(ctx);
                    log('keyboard is visible: $isVisible');
                    return Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        AnimatedPadding(
                          padding: EdgeInsets.only(bottom: isVisible ? 50 : 0),
                          duration: Duration(milliseconds: 500),
                          child: child!,
                        ),
                        DebugDrawerBanner(
                          onTap: () {
                            navigatorKey.currentState?.push(
                              MaterialPageRoute<void>(
                                builder:
                                    (pageRouteContext) => const DebugDrawer(),
                                settings: const RouteSettings(
                                  name: 'debug_drawer',
                                ),
                              ),
                            );
                          },
                        ),
                        CustomDoneButtonStripWidget(
                          isKeyboardVisible: isVisible,
                          textColor: theme.text.textPrimary,
                          backgroundColor: theme.background.bgTertiary,
                          text: "Done",
                        ),
                      ],
                    );
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }
}
