import 'package:api_client/api_client.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:injectable/injectable.dart';
import 'package:monitoring/monitoring.dart';

@module
abstract class ApiModule {
  @lazySingleton
  MockApiInterceptor apiInterceptor() => MockApiInterceptor();

  @lazySingleton
  AuthInterceptor authInterceptor(ApiClientBase clientBase) => AuthInterceptor(
    onGetToken: () => "",

    /// Add your access token here
    onTokenRefresh: () {
      print("onTokenRefresh");
    },
    onNavigateToLogin: () {
      print("onNavigateToLogin");
    },
  );

  @lazySingleton
  DioBuilder dioBuilder(
    MockApiInterceptor apiInterceptor,
    PrettyDioLogger dioLogger,
  ) =>
      DioBuilder()
          .setBaseUrl('https://equiti-backend-demo-dev.equiti.me.uk/')
          .addInterceptor(dioLogger)
          .withNativeAdapter()
          .addInterceptor(apiInterceptor)
          .withReporter();

  @lazySingleton
  ApiClientBase apiClient(DioBuilder diobuilder) =>
      DioApiClient(diobuilder.build());
}
