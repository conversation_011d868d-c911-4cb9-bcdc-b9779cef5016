import 'package:equiti_router/src/equiti_navigator_base.dart';
import 'package:equiti_router/src/equiti_page.dart';
import 'package:equiti_router/src/equiti_route.dart';
import 'package:equiti_router/src/equiti_route_location.dart';
import 'package:equiti_router/src/extensions.dart';
import 'package:flutter/widgets.dart';
import 'package:navigation_utils/navigation_utils.dart';

class EquitiNavigator implements EquitiNavigatorBase {
  final NavigationManager _navigationManager;

  EquitiNavigator({
    EquitiPage? initialPage,
    required List<EquitiRouteLocation> locations,
    List<NavigatorObserver> observers = const [],
    bool debugLog = false,
    bool authenticated = true,
    Page<Object?> Function(EquitiRoute route)? onUnknownRoute,
  }) : _navigationManager = NavigationManager.init(
         mainRouterDelegate: DefaultRouterDelegate(
           observers: observers,
           authenticated: authenticated,
           navigationDataRoutes:
               locations
                   .expand((location) => location.pages)
                   .map((page) => page.toNavigationData)
                   .toList(),
           deeplinkDestinations:
               locations
                   .expand((location) => location.deeplinkDestinations)
                   .map(
                     (equitiDeepLink) => equitiDeepLink.toDeeplinkDestination,
                   )
                   .toList(),
           debugLog: debugLog,
           onUnknownRoute:
               onUnknownRoute != null
                   ? (route) => onUnknownRoute(route.equitiRoute)
                   : null,
         ),
         routeInformationParser: DefaultRouteInformationParser(
           debugLog: debugLog,
         ),
       ) {
    if (initialPage != null) {
      _navigationManager.setInitialRoutePathFunction = (Uri uri) {
        return initialPage.url;
      };
    }
  }

  @override
  Future<void> push(
    String name, {
    Object? arguments,
    Map<String, dynamic> data = const {},
    Map<String, String> pathParameters = const {},
  }) => _navigationManager.push(
    name,
    arguments: arguments,
    data: data,
    pathParameters: pathParameters,
  );

  @override
  void pop([Object? result]) => _navigationManager.pop(result);

  @override
  void popUntil(String name, {bool inclusive = false}) =>
      _navigationManager.popUntil(name, inclusive: inclusive);

  @override
  void pauseNavigation({Page<Object?> Function(String name)? pageBuilder}) =>
      _navigationManager.pauseNavigation(pageBuilder: pageBuilder);
  @override
  void resumeNavigation() => _navigationManager.resumeNavigation();

  @override
  RouteInformationParser<Object> get routeInformationParser =>
      _navigationManager.routeInformationParser;

  @override
  RouterDelegate<Object> get routerDelegate =>
      _navigationManager.routerDelegate;

  @override
  void set(List<String> names) => _navigationManager.set(names);

  @override
  void setOverlay(
    Page<Object?> Function(String name) pageBuilder, {
    bool apply = true,
  }) => _navigationManager.setOverlay(pageBuilder, apply: apply);

  @override
  Future<Object?> pushAndRemoveUntil(
    String name,
    String routeUntilName, {
    Map<String, String>? queryParameters,
    Object? arguments,
    Map<String, dynamic> data = const {},
    Map<String, String> pathParameters = const {},
    bool inclusive = false,
  }) => _navigationManager.pushAndRemoveUntil(
    name,
    routeUntilName,
    queryParameters: queryParameters,
    arguments: arguments,
    data: data,
    pathParameters: pathParameters,
    inclusive: inclusive,
  );

  @override
  Future<Object?> pushReplacement(
    String name, {
    Map<String, String>? queryParameters,
    Object? arguments,
    Map<String, dynamic> data = const {},
    Map<String, String> pathParameters = const {},
    Object? result,
  }) => _navigationManager.pushReplacement(
    name,
    queryParameters: queryParameters,
    arguments: arguments,
    data: data,
    pathParameters: pathParameters,
    result: result,
  );

  @override
  void remove(String name) => _navigationManager.remove(name);

  @override
  void removeAbove(String name) => _navigationManager.removeAbove(name);

  @override
  void removeBelow(String name) => _navigationManager.removeBelow(name);

  @override
  void replace(String oldName, {String? newName, Map<String, dynamic>? data}) =>
      _navigationManager.replace(oldName, newName: newName, data: data);

  @override
  set setInitialRoutePathFunction(
    String Function(Uri initialRoute)? setInitialRoutePath,
  ) => _navigationManager.setInitialRoutePathFunction = setInitialRoutePath;

  @override
  set globalData(Map<String, dynamic> data) {
    for (final mapEntry in data.entries) {
      _navigationManager.routerDelegate.globalData[mapEntry.key] =
          mapEntry.value;
    }
  }

  @override
  Map<String, dynamic> get globalData =>
      Map<String, dynamic>.from(_navigationManager.routerDelegate.globalData);

  @override
  GlobalKey<NavigatorState> navigatorKey() =>
      _navigationManager.routerDelegate.navigatorKey;
}
