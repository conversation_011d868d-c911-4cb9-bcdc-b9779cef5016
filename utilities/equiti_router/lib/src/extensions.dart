import 'package:equiti_router/src/equiti_deeplink_destination.dart';
import 'package:equiti_router/src/equiti_page.dart';
import 'package:equiti_router/src/equiti_route.dart';
import 'package:equiti_router/src/route_type.dart';
import 'package:navigation_utils/navigation_utils.dart';

extension Extensions on EquitiDeeplinkDestination {
  DeeplinkDestination get toDeeplinkDestination => DeeplinkDestination(
    deeplinkUrl: deeplinkUrl,
    destinationLabel: destinationLabel,
    backstack: backstack,
    excludeDeeplinkNavigationPages: excludeDeeplinkNavigationPages,
    shouldNavigateDeeplinkFunction: shouldNavigateDeeplink,
    redirectFunction: redirect,
    mapArgumentsFunction: mapArguments,
    mapPathParameterFunction: mapPathParameter,
    mapQueryParameterFunction: mapQueryParameter,
    authenticationRequired: authenticationRequired,
  );
}

extension EquitiRouteDataX on EquitiPage {
  NavigationData get toNavigationData => NavigationData(
    url: url,
    label: label,
    builder:
        (context, routeData, globalData) =>
            builder(context, routeData.equitiRoute, globalData),
    pageType: routeType.toPageType,
    metadata: metadata,
  );
}

extension RouteTypeX on RouteType {
  PageType get toPageType => switch (this) {
    RouteType.material => PageType.material,
    RouteType.transparent => PageType.transparent,
    RouteType.cupertino => PageType.cupertino,
  };
}

extension DefaultRouteX on DefaultRoute {
  EquitiRoute get equitiRoute {
    return EquitiRoute(
      path: path,
      label: label,
      pathParameters: pathParameters,
      metadata: metadata ?? const {},
      arguments: arguments,
    );
  }
}

extension EquitiRouteX on EquitiRoute {
  DefaultRoute get defaultRoute {
    return DefaultRoute(
      path: path,
      label: label,
      pathParameters: pathParameters,
      metadata: metadata,
      arguments: arguments,
    );
  }
}
