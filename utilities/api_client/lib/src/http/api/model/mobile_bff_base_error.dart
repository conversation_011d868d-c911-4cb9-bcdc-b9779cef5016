// To parse this JSON data, do
//
//     final mobileBffBaseError = mobileBffBaseErrorFromJson(jsonString);

import 'dart:convert';

MobileBffBaseError mobileBffBaseErrorFromJson(String str) =>
    MobileBffBaseError.fromJson(json.decode(str) as Map<String, dynamic>);

String mobileBffBaseErrorToJson(MobileBffBaseError data) =>
    json.encode(data.toJson());

class MobileBffBaseError {
  Error? error;

  MobileBffBaseError({this.error});

  MobileBffBaseError copyWith({Error? error}) =>
      MobileBffBaseError(error: error ?? this.error);

  factory MobileBffBaseError.fromJson(Map<String, dynamic> json) =>
      MobileBffBaseError(
        error:
            json["error"] == null
                ? null
                : Error.fromJson(json["error"] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {"error": error?.toJson()};
}

class Error {
  int? errorCode;
  String? description;

  Error({this.errorCode, this.description});

  Error copyWith({int? errorCode, String? description}) => Error(
    errorCode: errorCode ?? this.errorCode,
    description: description ?? this.description,
  );

  factory Error.fromJson(Map<String, dynamic> json) => Error(
    errorCode: json["errorCode"] as int?,
    description: json["description"] as String?,
  );

  Map<String, dynamic> toJson() => {
    "errorCode": errorCode,
    "description": description,
  };
}
