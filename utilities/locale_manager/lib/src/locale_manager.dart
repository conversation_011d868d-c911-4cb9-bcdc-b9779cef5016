import 'dart:io';
import 'package:flutter/material.dart';
import 'package:preferences/preferences.dart';

class LocaleManager extends ChangeNotifier {
  final EquitiPreferences preferences;
  late Locale _locale;

  LocaleManager({required this.preferences}) {
    final languageCode =
        Platform.localeName.split('_').elementAtOrNull(0) == "ar" ? "ar" : "en";

    // Retrieve the saved language preference from local storage (e.g., SharedPreferences).
    // If no language is saved, it defaults to `languageCode`, which is determined from the system locale.
    _locale = Locale(
      preferences.getValue<String>("user_selected_language_code", languageCode),
    );
    // Retrieve the saved language preference from local storage (e.g., SharedPreferences).
    // If no language is saved, it defaults to `languageCode`, which is determined from the system locale.
    _locale = Locale(
      preferences.getValue<String>("user_selected_language_code", languageCode),
    );
  }

  Locale get locale => _locale;

  // Get language code directly from preferences
  String getLanguageCode() {
    return preferences.getValue<String>(
      "user_selected_language_code",
      Platform.localeName.split('_').elementAtOrNull(0) == "ar" ? "ar" : "en",
    );
  }

  String getLanguageLocalCode() {
    final languageCode = preferences.getValue<String>(
      "user_selected_language_code",
      Platform.localeName.split('_').elementAtOrNull(0) ?? 'en',
    );
    return languageCode == "ar" ? "ar-Ae" : "en-us";
  }

  // Set language code directly to preferences
  Future<void> setLanguageCode(String languageCode) async {
    final normalizedCode =
        languageCode.split('_').elementAtOrNull(0) == "ar" ? "ar" : "en";
    await preferences.setValue<String>(
      "user_selected_language_code",
      normalizedCode,
    );
    _locale = Locale(normalizedCode);
    notifyListeners();
  }

  Future<void> setLocale(Locale localeVal) async {
    final languageCode =
        localeVal.languageCode.split('_').elementAtOrNull(0) == "ar"
            ? "ar"
            : "en";
    _locale = localeVal;
    await preferences.setValue<String>(
      "user_selected_language_code",
      languageCode,
    );
    notifyListeners();
  }
}
