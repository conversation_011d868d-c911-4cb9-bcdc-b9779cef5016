// ignore_for_file: prefer-match-file-name

import 'package:decimal/decimal.dart';
import 'package:json_annotation/json_annotation.dart';

/// Converter that processes double values through Decimal for precision handling
///
/// This converter ensures that all double values go through Decimal conversion
/// during JSON serialization/deserialization to maintain consistent precision
/// and avoid floating-point precision issues.
///
/// Usage:
/// ```dart
/// @JsonKey(fromJson: DecimalConverter._fromJson, toJson: DecimalConverter._toJson)
/// required double price;
/// ```
class DecimalConverter implements JsonConverter<double, num> {
  const DecimalConverter();

  @override
  double fromJson(num json) {
    return Decimal.parse(json.toString()).toDouble();
  }

  @override
  double toJson(double object) {
    return Decimal.parse(object.toString()).toDouble();
  }

  /// Static method for use with @<PERSON>son<PERSON>ey annotations
  static double fromJsonStatic(num json) {
    return Decimal.parse(json.toString()).toDouble();
  }

  /// Static method for use with @JsonKey annotations
  static double toJsonStatic(double object) {
    return Decimal.parse(object.toString()).toDouble();
  }
}

/// Converter that processes nullable double values through Decimal for precision handling
///
/// This converter ensures that all nullable double values go through Decimal conversion
/// during JSON serialization/deserialization to maintain consistent precision
/// and avoid floating-point precision issues.
///
/// Usage:
/// ```dart
/// @JsonKey(fromJson: NullableDecimalConverter._fromJson, toJson: NullableDecimalConverter._toJson)
/// double? optionalPrice;
/// ```
class NullableDecimalConverter implements JsonConverter<double?, num?> {
  const NullableDecimalConverter();

  @override
  double? fromJson(num? json) {
    if (json == null) return null;
    return Decimal.parse(json.toString()).toDouble();
  }

  @override
  double? toJson(double? object) {
    if (object == null) return null;
    return Decimal.parse(object.toString()).toDouble();
  }

  /// Static method for use with @JsonKey annotations
  static double? fromJsonStatic(num? json) {
    if (json == null) return null;
    return Decimal.parse(json.toString()).toDouble();
  }

  /// Static method for use with @JsonKey annotations
  static double? toJsonStatic(double? object) {
    if (object == null) return null;
    return Decimal.parse(object.toString()).toDouble();
  }
}
