import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomDoneButtonStripWidget extends StatelessWidget {
  const CustomDoneButtonStripWidget({
    super.key,
    required this.isKeyboardVisible,
    required this.backgroundColor,
    required this.textColor,
    required this.text,
  });
  final bool isKeyboardVisible;
  final Color backgroundColor, textColor;
  final String text;

  @override
  Widget build(BuildContext context) {
    return !isKeyboardVisible
        ? SizedBox()
        : Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.viewInsetsOf(context).bottom,
          ),
          color: backgroundColor,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  SystemChannels.textInput.invokeMethod('TextInput.hide');
                },
                child: Text(
                  text,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
              ),
            ],
          ),
        );
  }
}
