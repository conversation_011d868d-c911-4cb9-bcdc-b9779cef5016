import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';

class AppTestConfigurator {
  const AppTestConfigurator({
    required this.onInit,
    required this.tester,
    required this.app,
    this.supportedLocales,
    this.localizationsDelegates,
    this.isGoldenTest = false,
    this.scenarios = const <VoidCallback>[],
  });

  final WidgetTester tester;
  final Widget app;
  final List<VoidCallback> scenarios;
  final List<Locale>? supportedLocales;
  final List<LocalizationsDelegate<Object?>>? localizationsDelegates;
  final bool isGoldenTest;
  final FutureOr<void> Function()? onInit;

  Future<void> loadCustomFonts() async {
    final interFontLoader = FontLoader('Inter')..addFont(
      Future.value(
        ByteData.sublistView(File('../../fonts/Inter.ttf').readAsBytesSync()),
      ),
    );
    await interFontLoader.load();

    final notoNaskhArabicFontLoader = FontLoader('NotoNaskhArabic')..addFont(
      Future.value(
        ByteData.sublistView(
          File('../../fonts/NotoNaskhArabic.ttf').readAsBytesSync(),
        ),
      ),
    );
    await notoNaskhArabicFontLoader.load();
  }

  Future<void> run() async {
    // await EquitiLocalizationManager.initMock();
    await onInit?.call();
    for (final scenario in scenarios) {
      scenario();
    }

    if (isGoldenTest) {
      await loadCustomFonts();
      await loadAppFonts();
      await tester.pumpWidgetBuilder(app);
    } else {
      await tester.pumpWidget(
        MaterialApp(
          home: app,
          localizationsDelegates: localizationsDelegates,
          supportedLocales: supportedLocales ?? [Locale('en')],
        ),
      );
      await tester.pumpAndSettle();
    }
  }
}
