import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:network_logging/src/data/models/mobile_bff_base_error.dart';

import '../config/network_log_config.dart';
import '../data/models/network_log_entry.dart';
import '../data/storage/network_log_storage.dart';
import 'network_logger_interceptor_base.dart';

class DevelopmentNetworkLoggerInterceptor extends NetworkLoggerInterceptorBase {
  DevelopmentNetworkLoggerInterceptor(
    NetworkLogStorage storage,
    NetworkLogConfig config,
  ) : super(storage, config);

  @override
  Future<void> logRequest(NetworkLogEntry entry) async {
    await storage.store(entry);
  }

  @override
  Future<void> logResponse(Response response, String requestId) async {
    final entries = await storage.getAll();
    final entryIndex = entries.indexWhere((e) => e.id == requestId);

    if (entryIndex == -1) return;

    final originalEntry = entries[entryIndex];
    final responseBody =
        config.logResponseBodies
            ? sanitizer.sanitizeBody(
              response.data?.toString(),
              response.requestOptions.uri.toString(),
            )
            : null;

    final updatedEntry = originalEntry.copyWith(
      responseTimestamp: DateTime.now(),
      statusCode: response.statusCode,
      responseHeaders: sanitizer.sanitizeHeaders(
        Map<String, dynamic>.from(response.headers.map),
      ),
      responseBody: responseBody,
      duration:
          DateTime.now()
              .difference(originalEntry.requestTimestamp)
              .inMilliseconds,
    );

    await storage.store(updatedEntry);
  }

  @override
  Future<void> logError(DioException error, String requestId) async {
    final entries = await storage.getAll();
    final entryIndex = entries.indexWhere((e) => e.id == requestId);

    if (entryIndex == -1) return;

    final originalEntry = entries[entryIndex];
    final errorData = error.response?.data;
    final MobileBffBaseError mobileBffBaseError = MobileBffBaseError.fromJson(
      errorData is String ? {} : errorData,
    );
    final updatedEntry = originalEntry.copyWith(
      responseTimestamp: DateTime.now(),
      statusCode: error.response?.statusCode,
      responseHeaders:
          error.response?.headers.map != null
              ? sanitizer.sanitizeHeaders(
                Map<String, dynamic>.from(error.response!.headers.map),
              )
              : null,
      responseBody: mobileBffBaseError.error?.toJson().toString(),
      errorMessage: mobileBffBaseError.error?.description ?? error.message,
      duration:
          DateTime.now()
              .difference(originalEntry.requestTimestamp)
              .inMilliseconds,
    );

    await storage.store(updatedEntry);
  }
}
