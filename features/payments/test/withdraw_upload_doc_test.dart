// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/withdraw_new_bank_upload_doc/withdraw_new_bank_upload_doc.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Withdraw Add New Account Upload Doc Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Upload Doc Initial Ui''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Upload Doc Initial Ui''');
        await theAppIsRendered(
          tester,
          WithdrawNewBankUploadDoc(operationId: '', tradingAccountId: ''),
        );
        await screenshotVerified(tester, 'withdraw_upload_doc/step_1_loaded');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Upload Doc Initial Ui''', success);
      }
    });
  });
}
