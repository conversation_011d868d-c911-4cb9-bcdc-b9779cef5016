// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/list_of_accounts_success.dart';
import 'scenarios/list_of_accounts_success_empty.dart';
import 'scenarios/list_of_accounts_success_loading.dart';
import 'scenarios/list_of_accounts_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'list_of_accounts_test_data.dart';
import 'tap_bar_style_test_data.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''List of accounts''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''List all accounts failure''', (tester) async {
      var success = true;
      try {
        await beforeEach('''List all accounts failure''');
        await theAppIsRendered(
          tester,
          ListOfAccountsFailureTestData(),
          scenarios: [listOfAccountsFailure],
        );
        await iWait(tester);
        await screenshotVerified(
          tester,
          'account_list_widget/list_of_accounts_failure',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''List all accounts failure''', success);
      }
    });
    testGoldens('''List accounts with empty state''', (tester) async {
      var success = true;
      try {
        await beforeEach('''List accounts with empty state''');
        await theAppIsRendered(
          tester,
          ListOfAccountsEmptyTestData(),
          scenarios: [listOfAccountsSuccessEmpty],
        );
        await iWait(tester);
        await screenshotVerified(
          tester,
          'account_list_widget/list_of_accounts_empty',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''List accounts with empty state''', success);
      }
    });
    testGoldens('''List accounts with selection''', (tester) async {
      var success = true;
      try {
        await beforeEach('''List accounts with selection''');
        await theAppIsRendered(
          tester,
          ListOfAccountsWithSelectionTestData(),
          scenarios: [listOfAccountsSuccess],
        );
        await iWait(tester);
        await screenshotVerified(
          tester,
          'account_list_widget/list_of_accounts_with_selection',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''List accounts with selection''', success);
      }
    });
  });
}
