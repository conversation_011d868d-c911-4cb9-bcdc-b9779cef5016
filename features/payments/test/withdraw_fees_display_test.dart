// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/withdraw_fees_zero_success.dart';
import 'scenarios/withdraw_fees_non_zero_success.dart';
import 'scenarios/withdraw_fees_failure.dart';
import 'scenarios/withdraw_fees_client_profile_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'withdraw_fees_display_test_screen.dart';
import 'package:payment/src/domain/model/transfer_type.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Withdraw Fees Display Widget''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Display zero withdrawal fees''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display zero withdrawal fees''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(),
          scenarios: [withdrawFeesZeroSuccess],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_fees_zero_fees',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display zero withdrawal fees''', success);
      }
    });
    testGoldens('''Display non-zero withdrawal fees''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display non-zero withdrawal fees''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(),
          scenarios: [withdrawFeesNonZeroSuccess],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_fees_non_zero_fees',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display non-zero withdrawal fees''', success);
      }
    });
    testGoldens('''Display loading state while calculating fees''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Display loading state while calculating fees''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(),
          scenarios: [withdrawFeesNonZeroSuccess],
        );
        await screenshotVerifiedWithCustomPump(tester, 'withdraw_fees_loading');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Display loading state while calculating fees''',
          success,
        );
      }
    });
    testGoldens('''Display error when fees calculation fails''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Display error when fees calculation fails''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(),
          scenarios: [withdrawFeesFailure],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_fees_calculation_error',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Display error when fees calculation fails''',
          success,
        );
      }
    });
    testGoldens('''Display error when client profile fails''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display error when client profile fails''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(),
          scenarios: [withdrawFeesClientProfileFailure],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_fees_client_profile_error',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display error when client profile fails''', success);
      }
    });
    testGoldens('''Display idle state when amount is zero''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display idle state when amount is zero''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(amount: 0.0),
          scenarios: [withdrawFeesNonZeroSuccess],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_fees_amount_zero',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display idle state when amount is zero''', success);
      }
    });
    testGoldens('''Display idle state when transfer type is none''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Display idle state when transfer type is none''');
        await theAppIsRendered(
          tester,
          WithdrawFeesDisplayTestScreen(transferType: TransferType.none),
          scenarios: [withdrawFeesNonZeroSuccess],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_fees_transfer_type_none',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Display idle state when transfer type is none''',
          success,
        );
      }
    });
  });
}
