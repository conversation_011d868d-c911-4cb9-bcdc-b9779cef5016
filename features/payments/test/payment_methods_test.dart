// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/payment_methods_success.dart';
import 'scenarios/payment_methods_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/deposit_payment_methods/deposit_payment_options.dart';
import 'package:domain/domain.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Payment Methods Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''List all payment methods''', (tester) async {
      var success = true;
      try {
        await beforeEach('''List all payment methods''');
        await theAppIsRendered(
          tester,
          DepositPaymentOptions(
            depositFlowConfig: DepositFlowConfig(
              origin: "",
              depositType: DepositType.additional,
            ),
          ),
          scenarios: [paymentMethodsSuccess],
        );
        await screenshotVerified(tester, 'payment_methods_success');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''List all payment methods''', success);
      }
    });
    testGoldens('''Failed to load payment methods''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Failed to load payment methods''');
        await theAppIsRendered(
          tester,
          DepositPaymentOptions(
            depositFlowConfig: DepositFlowConfig(
              origin: "",
              depositType: DepositType.additional,
            ),
          ),
          scenarios: [paymentMethodsFailure],
        );
        await screenshotVerified(tester, 'payment_methods_failure');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Failed to load payment methods''', success);
      }
    });
  });
}
