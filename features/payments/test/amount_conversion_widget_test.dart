// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/amount_conversion_success.dart';
import 'scenarios/amount_conversion_failure.dart';
import 'scenarios/amount_conversion_loading.dart';
import 'package:equiti_test/equiti_test.dart';
import 'amount_conversion_widget_test_data.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified_with_custom_pump.dart';
import './step/i_wait_for_seconds.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/i_fill_into_field.dart';
import './step/i_tap_key.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Amount Conversion Widget Component''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Show Loading State''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Loading State''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.loading(),
          scenarios: [getConversionLoading],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'amount_conversion_widget/amount_conversion_loading',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Loading State''', success);
      }
    });
    testGoldens('''Show Error State''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Error State''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.basic(),
          scenarios: [],
        );
        await iWaitForSeconds(tester, 2);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_error',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Error State''', success);
      }
    });
    testGoldens('''Show Without Conversion Same Currency''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Without Conversion Same Currency''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.sameCurrency(),
          scenarios: [],
        );
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_without_conversion',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Without Conversion Same Currency''', success);
      }
    });
    testGoldens('''Show With Conversion Different Currency''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show With Conversion Different Currency''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withConversion(),
          scenarios: [getConversionSuccess],
        );
        await iWaitForSeconds(tester, 2);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_with_conversion',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show With Conversion Different Currency''', success);
      }
    });
    testGoldens('''Show Validation Error Below Minimum''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Validation Error Below Minimum''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.basic(),
          scenarios: [],
        );
        await iWait(tester);
        await iFillIntoField(tester, '0.5', 'transfer_amount_field');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_below_minimum',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Validation Error Below Minimum''', success);
      }
    });
    testGoldens('''Show Validation Error Above Maximum''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Validation Error Above Maximum''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.basic(),
          scenarios: [],
        );
        await iWait(tester);
        await iFillIntoField(tester, '5000', 'transfer_amount_field');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_above_maximum',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Validation Error Above Maximum''', success);
      }
    });
    testGoldens('''Remove validation error when i clear the amount field''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach(
          '''Remove validation error when i clear the amount field''',
        );
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.basic(),
          scenarios: [],
        );
        await iWait(tester);
        await iFillIntoField(tester, '5000', 'transfer_amount_field');
        await iWaitForSeconds(tester, 1);
        await iFillIntoField(tester, '', 'transfer_amount_field');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_remove_amount',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Remove validation error when i clear the amount field''',
          success,
        );
      }
    });
    testGoldens('''Show Suggested Amounts Three Items''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Suggested Amounts Three Items''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withThreeSuggestedAmounts(),
          scenarios: [],
        );
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_three_suggested_amounts',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Suggested Amounts Three Items''', success);
      }
    });
    testGoldens('''Show Suggested Amounts Four Items''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Suggested Amounts Four Items''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withFourSuggestedAmounts(),
          scenarios: [],
        );
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_four_suggested_amounts',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Suggested Amounts Four Items''', success);
      }
    });
    testGoldens('''Show Suggested Amounts Many Items''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Suggested Amounts Many Items''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withManySuggestedAmounts(),
          scenarios: [],
        );
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_many_suggested_amounts',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Suggested Amounts Many Items''', success);
      }
    });
    testGoldens('''Show Currency Dropdown''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Currency Dropdown''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withConversion(),
          scenarios: [getConversionSuccess],
        );
        await iWaitForSeconds(tester, 2);
        await iTapKey(tester, 'converted_currency_selector');
        await iWait(tester);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_currency_dropdown',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Currency Dropdown''', success);
      }
    });
    testGoldens('''Show Without Suggested Amounts''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Without Suggested Amounts''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withoutSuggestedAmounts(),
          scenarios: [],
        );
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_without_suggested_amounts',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Without Suggested Amounts''', success);
      }
    });
    testGoldens('''Show With Conversion Rate Display''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show With Conversion Rate Display''');
        await theAppIsRendered(
          tester,
          AmountConversionWidgetTestData.withConversion(),
          scenarios: [getConversionSuccess],
        );
        await iWaitForSeconds(tester, 2);
        await iFillIntoField(tester, '100', 'transfer_amount_field');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(
          tester,
          'amount_conversion_widget/amount_conversion_with_rate_display',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show With Conversion Rate Display''', success);
      }
    });
  });
}
