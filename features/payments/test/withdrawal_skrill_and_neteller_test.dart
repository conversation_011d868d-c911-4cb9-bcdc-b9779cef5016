// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:payment/payments.dart';
import 'scenarios/check_withdrawal_allowed_failure.dart';
import 'scenarios/check_withdrawal_allowed_success.dart';
import 'scenarios/check_withdrawal_allowed_empty.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Withdrawal By Skrill and Neteller''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Withdrawal Skrill And Neteller View Success''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Withdrawal Skrill And Neteller View Success''');
        await theAppIsRendered(
          tester,
          WithdrawSkrillAndNetellerScreen(
            method: WithdrawalPaymentMethod(
              name: "Skrill",
              currencyAmountDetails: [
                CurrencyAmountDetail(
                  currency: 'AED',
                  suggestedAmounts: [500, 1000, 1500],
                  minAmount: 1,
                  maxAmount: 1000,
                ),
                CurrencyAmountDetail(
                  currency: 'USD',
                  suggestedAmounts: [50, 100, 150],
                  minAmount: 1,
                  maxAmount: 2000,
                ),
              ],
              currencies: ["USD", "AED"],
            ),
          ),
          scenarios: [checkWithdrawalAllowedSuccess],
        );
        await screenshotVerified(
          tester,
          'withdrawal_skrill_and_neteller_success',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Withdrawal Skrill And Neteller View Success''',
          success,
        );
      }
    });
    testGoldens('''Withdrawal Skrill And Neteller View Failure''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Withdrawal Skrill And Neteller View Failure''');
        await theAppIsRendered(
          tester,
          WithdrawSkrillAndNetellerScreen(
            method: WithdrawalPaymentMethod(
              name: "Skrill",
              currencyAmountDetails: [
                CurrencyAmountDetail(
                  currency: 'AED',
                  suggestedAmounts: [500, 1000, 1500],
                  minAmount: 1,
                  maxAmount: 1000,
                ),
                CurrencyAmountDetail(
                  currency: 'USD',
                  suggestedAmounts: [50, 100, 150],
                  minAmount: 1,
                  maxAmount: 2000,
                ),
              ],
              currencies: ["USD", "AED"],
            ),
          ),
          scenarios: [checkWithdrawalAllowedFailure],
        );
        await screenshotVerified(
          tester,
          'withdrawal_skrill_and_neteller_failure',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Withdrawal Skrill And Neteller View Failure''',
          success,
        );
      }
    });
    testGoldens('''Withdrawal Skrill And Neteller View Empty State''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Withdrawal Skrill And Neteller View Empty State''');
        await theAppIsRendered(
          tester,
          WithdrawSkrillAndNetellerScreen(
            method: WithdrawalPaymentMethod(
              name: "Skrill",
              currencyAmountDetails: [
                CurrencyAmountDetail(
                  currency: 'AED',
                  suggestedAmounts: [500, 1000, 1500],
                  minAmount: 1,
                  maxAmount: 1000,
                ),
                CurrencyAmountDetail(
                  currency: 'USD',
                  suggestedAmounts: [50, 100, 150],
                  minAmount: 1,
                  maxAmount: 2000,
                ),
              ],
              currencies: ["USD", "AED"],
            ),
          ),
          scenarios: [checkWithdrawalAllowedEmptyState],
        );
        await screenshotVerified(
          tester,
          'withdrawal_skrill_and_neteller_empty_state',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Withdrawal Skrill And Neteller View Empty State''',
          success,
        );
      }
    });
  });
}
