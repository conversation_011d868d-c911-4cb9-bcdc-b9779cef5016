// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/deposit_amount_success.dart';
import 'scenarios/deposit_amount_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'deposit_amount_test_data.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/i_tap_key.dart';
import 'package:bdd_widget_test/step/i_tap_text.dart';
import './step/i_wait_for_seconds.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Deposit Amount Component''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Show Deposit Amount With Conversion''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Deposit Amount With Conversion''');
        await theAppIsRendered(
          tester,
          DepositAmountTestData(),
          scenarios: [getConversionSuccess],
        );
        await iWait(tester);
        await iTapKey(tester, 'transfer_currency_Inkwell');
        await iWait(tester);
        await iTapText(tester, 'AED');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(tester, 'deposit_amount_with_converison');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Deposit Amount With Conversion''', success);
      }
    });
    testGoldens('''Show Deposit Amount Without Conversion''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Deposit Amount Without Conversion''');
        await theAppIsRendered(tester, DepositAmountTestData(), scenarios: []);
        await screenshotVerified(tester, 'deposit_amount_without_converison');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Deposit Amount Without Conversion''', success);
      }
    });
    testGoldens('''Show Currency List''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Show Currency List''');
        await theAppIsRendered(tester, DepositAmountTestData(), scenarios: []);
        await iWait(tester);
        await iTapKey(tester, 'transfer_currency_Inkwell');
        await iWait(tester);
        await screenshotVerified(tester, 'deposit_amount_currency_list');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Show Currency List''', success);
      }
    });
  });
}
