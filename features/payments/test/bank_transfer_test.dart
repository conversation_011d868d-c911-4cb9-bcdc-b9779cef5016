// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/bank_transfer_accounts_success.dart';
import 'scenarios/bank_transfer_accounts_faliure.dart';
import 'scenarios/withdraw_fees_insufficient_balance.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/withdraw_bank_transfer.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/transfer_type_screen.dart';
import 'package:payment/src/data/bank_accounts_model.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/i_tap_key.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Withdraw Bank Transfer Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''list all  accounts''', (tester) async {
      var success = true;
      try {
        await beforeEach('''list all  accounts''');
        await theAppIsRendered(
          tester,
          WithdrawBankTransfer(
            tradingAccountId: '112344',
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'Usd',
              currency: 'Usd',
              amount: 233,
              convertedAmount: 233,
              conversionRate: 32,
              conversionRateString: '32',
              accountBalance: 1000,
            ),
          ),
          scenarios: [bankTransferAccountListSuccessful],
        );
        await screenshotVerified(tester, 'withdraw_bank_success');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''list all  accounts''', success);
      }
    });
    testGoldens('''Failed to load accounts''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Failed to load accounts''');
        await theAppIsRendered(
          tester,
          WithdrawBankTransfer(
            tradingAccountId: '112344',
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'Usd',
              currency: 'Usd',
              amount: 233,
              convertedAmount: 233,
              conversionRate: 32,
              conversionRateString: '32',
              accountBalance: 1000,
            ),
          ),
          scenarios: [bankTransferAccountListFailure],
        );
        await screenshotVerified(tester, 'withdraw_bank_failure');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Failed to load accounts''', success);
      }
    });
    testGoldens('''enable deletion mode''', (tester) async {
      var success = true;
      try {
        await beforeEach('''enable deletion mode''');
        await theAppIsRendered(
          tester,
          WithdrawBankTransfer(
            tradingAccountId: '112344',
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'Usd',
              currency: 'Usd',
              amount: 233,
              convertedAmount: 233,
              conversionRate: 32,
              conversionRateString: '32',
              accountBalance: 1000,
            ),
          ),
          scenarios: [bankTransferAccountListSuccessful],
        );
        await iWait(tester);
        await iTapKey(tester, 'enable_delete_bank_account_button');
        await screenshotVerified(tester, 'enable_deletion_mode');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''enable deletion mode''', success);
      }
    });
    testGoldens('''tab on delete icon''', (tester) async {
      var success = true;
      try {
        await beforeEach('''tab on delete icon''');
        await theAppIsRendered(
          tester,
          WithdrawBankTransfer(
            tradingAccountId: '112344',
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'Usd',
              currency: 'Usd',
              amount: 233,
              convertedAmount: 233,
              conversionRate: 32,
              conversionRateString: '32',
              accountBalance: 1000,
            ),
          ),
          scenarios: [bankTransferAccountListSuccessful],
        );
        await iWait(tester);
        await iTapKey(tester, 'enable_delete_bank_account_button');
        await iWait(tester);
        await iTapKey(tester, 'delete_bank_account_button_ferf');
        await iWait(tester);
        await screenshotVerified(tester, 'tab_on_delete_icon');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''tab on delete icon''', success);
      }
    });
    testGoldens('''Insufficient balance validation with high fees''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Insufficient balance validation with high fees''');
        await theAppIsRendered(
          tester,
          TransferTypeScreen(
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'USD',
              currency: 'USD',
              amount: 500,
              convertedAmount: 500,
              conversionRate: 1,
              conversionRateString: '1',
              accountBalance: 600,
            ),
            bank: BankAccountData(
              id: 'test-id',
              accountNickname: 'Test Bank',
              bankName: 'Test Bank Name',
              branchName: 'Test Branch',
              accountHolder: 'Test Holder',
              country: 'US',
              iban: 'TEST123',
              swiftBic: 'TESTBIC',
              transferTypes: ['Domestic', 'International'],
              image: null,
              type: 0,
            ),
          ),
          scenarios: [withdrawFeesInsufficientBalance],
        );
        await iWait(tester);
        await iTapKey(tester, 'transfer_type_field');
        await iWait(tester);
        await iTapKey(tester, 'transfer_type_domestic');
        await iWait(tester);
        await screenshotVerified(tester, 'insufficient_balance_validation');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Insufficient balance validation with high fees''',
          success,
        );
      }
    });
  });
}
