// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:payment/payments.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Deposit By Bank Transfer''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Deposit Select Bank Screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Deposit Select Bank Screen''');
        await theAppIsRendered(
          tester,
          DepositSelectBankScreen(
            paymentMethodGroup: DepositPaymentMethodGroup(
              label: 'Bank Transfer',
              bankDetails: [
                BankDetail(
                  currency: 'USA',
                  banks: [
                    Bank(
                      name: 'Capital Bank of Jordan',
                      swiftCode: 'ARABJOAX100',
                      accountName: 'Equiti Group Limited Jordan',
                      accountAddress: 'Bank of America',
                      reference: 'Equiti account number [CRM ID]',
                      purposeOfPayment: 'Financial Services',
                      iban: 'JO89 ARAB 1180 0000 0011 8294 9115 01',
                      accountNumber: '0118-294911-501',
                    ),
                  ],
                ),
              ],
              methods: [
                DepositPaymentMethod(
                  name: "Bank Transfer",
                  currencyAmountDetails: [
                    CurrencyAmountDetail(
                      currency: 'AED',
                      suggestedAmounts: [500, 1000, 1500],
                      minAmount: 1,
                      maxAmount: 1000,
                    ),
                    CurrencyAmountDetail(
                      currency: 'USD',
                      suggestedAmounts: [50, 100, 150],
                      minAmount: 1,
                      maxAmount: 2000,
                    ),
                  ],
                  currencies: ["USD", "AED"],
                ),
              ],
            ),
          ),
        );
        await screenshotVerified(tester, 'deposit_select_bank_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Deposit Select Bank Screen''', success);
      }
    });
    testGoldens('''Deposit Bank Details Screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Deposit Bank Details Screen''');
        await theAppIsRendered(
          tester,
          DepositBankDetailsScreen(
            bank: Bank(
              name: 'Capital Bank of Jordan',
              swiftCode: 'ARABJOAX100',
              accountName: 'Equiti Group Limited Jordan',
              accountAddress: 'Bank of America',
              reference: 'Equiti account number [CRM ID]',
              purposeOfPayment: 'Financial Services',
              iban: 'JO89 ARAB 1180 0000 0011 8294 9115 01',
              accountNumber: '0118-294911-501',
            ),
          ),
        );
        await screenshotVerified(tester, 'deposit_bank__details_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Deposit Bank Details Screen''', success);
      }
    });
  });
}
