// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/widgets/withdraw_status_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Withdraw Status Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Display submitted status''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display submitted status''');
        await theAppIsRendered(tester, WithdrawStatusScreen.submitted());
        await screenshotVerified(
          tester,
          'withdraw_status_screen/submitted_status',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display submitted status''', success);
      }
    });
    testGoldens('''Display successful status''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display successful status''');
        await theAppIsRendered(tester, WithdrawStatusScreen.successful());
        await screenshotVerified(
          tester,
          'withdraw_status_screen/successful_status',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display successful status''', success);
      }
    });
    testGoldens('''Display rejected status''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display rejected status''');
        await theAppIsRendered(tester, WithdrawStatusScreen.rejected());
        await screenshotVerified(
          tester,
          'withdraw_status_screen/rejected_status',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display rejected status''', success);
      }
    });
    testGoldens('''Display error status''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display error status''');
        await theAppIsRendered(tester, WithdrawStatusScreen.error());
        await screenshotVerified(tester, 'withdraw_status_screen/error_status');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display error status''', success);
      }
    });
    testGoldens('''Display successful status with popUntilRoute''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Display successful status with popUntilRoute''');
        await theAppIsRendered(
          tester,
          WithdrawStatusScreen.successful(popUntilRoute: 'withdrawOptions'),
        );
        await screenshotVerified(
          tester,
          'withdraw_status_screen/successful_status_with_pop_until_route',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Display successful status with popUntilRoute''',
          success,
        );
      }
    });
  });
}
