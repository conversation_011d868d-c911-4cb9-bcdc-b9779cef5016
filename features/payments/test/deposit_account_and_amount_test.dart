// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/list_of_accounts_success.dart';
import 'scenarios/list_of_accounts_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/deposit_accounts_and_amount_screen.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Deposit Account and Amount''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Deposit Account and Amount List Of Accounts''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''Deposit Account and Amount List Of Accounts''');
        await theAppIsRendered(
          tester,
          DepositAccountsAndAmountScreen(
            paymentMethod: DepositPaymentMethod(
              name: "Card",
              currencyAmountDetails: [
                CurrencyAmountDetail(
                  currency: 'AED',
                  suggestedAmounts: [500, 1000, 1500],
                  minAmount: 1,
                  maxAmount: 1000,
                ),
                CurrencyAmountDetail(
                  currency: 'USD',
                  suggestedAmounts: [50, 100, 150],
                  minAmount: 1,
                  maxAmount: 2000,
                ),
              ],
              currencies: ["USD", "AED"],
            ),
            depositFlowConfig: DepositFlowConfig(
              origin: '',
              depositType: DepositType.additional,
            ),
          ),
          scenarios: [listOfAccountsSuccess],
        );
        await screenshotVerified(tester, 'deposit_account_and_amount');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Deposit Account and Amount List Of Accounts''',
          success,
        );
      }
    });
  });
}
