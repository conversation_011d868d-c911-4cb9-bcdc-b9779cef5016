// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/transfer_funds/transfer_funds_screen.dart';
import 'scenarios/transfer_funds_success.dart';
import 'transfer_funds_test_data.dart';
import 'scenarios/transfer_funds_failure.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import './step/i_fill_into_field.dart';
import './step/i_tap_duplo_button.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''TransferFunds''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''TransferFunds first Screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''TransferFunds first Screen''');
        await theAppIsRendered(
          tester,
          TransferFundsScreen(originRoute: ''),
          scenarios: [transferFundsSuccess],
        );
        await iWait(tester);
        await screenshotVerified(tester, 'transfer_funds_first_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''TransferFunds first Screen''', success);
      }
    });
    testGoldens('''TransferFunds seconed Screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''TransferFunds seconed Screen''');
        await theAppIsRendered(
          tester,
          TransferFundsTestData(),
          scenarios: [transferFundsSuccess],
        );
        await iWait(tester);
        await screenshotVerified(tester, 'transfer_funds_seconed_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''TransferFunds seconed Screen''', success);
      }
    });
  });
  group('''TransferFunds''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''TransferFunds success Screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''TransferFunds success Screen''');
        await theAppIsRendered(
          tester,
          TransferFundsTestData(),
          scenarios: [transferFundsSuccess],
        );
        await iFillIntoField(tester, '50', 'transfer_amount_field');
        await iWait(tester);
        await iTapDuploButton(tester, "Continue");
        await iWait(tester);
        await screenshotVerified(tester, 'transfer_funds_success_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''TransferFunds success Screen''', success);
      }
    });
  });
  group('''TransferFunds''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''TransferFunds failure Screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''TransferFunds failure Screen''');
        await theAppIsRendered(
          tester,
          TransferFundsTestData(),
          scenarios: [transferFundsfailure],
        );
        await iFillIntoField(tester, '50', 'transfer_amount_field');
        await iWait(tester);
        await iTapDuploButton(tester, "Continue");
        await iWait(tester);
        await screenshotVerified(tester, 'transfer_funds_failure_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''TransferFunds failure Screen''', success);
      }
    });
  });
}
