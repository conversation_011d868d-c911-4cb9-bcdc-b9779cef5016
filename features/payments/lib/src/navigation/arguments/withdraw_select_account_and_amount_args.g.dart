// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_select_account_and_amount_args.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawSelectAccountAndAmountArgs
_$WithdrawSelectAccountAndAmountArgsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_WithdrawSelectAccountAndAmountArgs', json, (
      $checkedConvert,
    ) {
      final val = _WithdrawSelectAccountAndAmountArgs(
        method: $checkedConvert(
          'method',
          (v) => WithdrawalPaymentMethod.fromJson(v as Map<String, dynamic>),
        ),
        account: $checkedConvert('account', (v) => v as String?),
        selectedCard: $checkedConvert(
          'selectedCard',
          (v) =>
              v == null
                  ? null
                  : WithdrawCard.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$WithdrawSelectAccountAndAmountArgsToJson(
  _WithdrawSelectAccountAndAmountArgs instance,
) => <String, dynamic>{
  'method': instance.method.toJson(),
  if (instance.account case final value?) 'account': value,
  if (instance.selectedCard?.toJson() case final value?) 'selectedCard': value,
};
