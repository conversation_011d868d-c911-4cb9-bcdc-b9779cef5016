// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_funds_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TransferFundsResponse _$TransferFundsResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_TransferFundsResponse', json, ($checkedConvert) {
  final val = _TransferFundsResponse(
    success: $checkedConvert('success', (v) => v as bool? ?? false),
    data: $checkedConvert(
      'data',
      (v) =>
          v == null
              ? null
              : TransferFundsData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$TransferFundsResponseToJson(
  _TransferFundsResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  if (instance.data?.toJson() case final value?) 'data': value,
};

_TransferFundsData _$TransferFundsDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_TransferFundsData', json, ($checkedConvert) {
      final val = _TransferFundsData(
        id: $checkedConvert('id', (v) => v as String? ?? ''),
        status: $checkedConvert('status', (v) => v as String? ?? ''),
      );
      return val;
    });

Map<String, dynamic> _$TransferFundsDataToJson(_TransferFundsData instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.status case final value?) 'status': value,
    };
