// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_method_mix_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CurrencyAmountDetail _$CurrencyAmountDetailFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_CurrencyAmountDetail', json, ($checkedConvert) {
  final val = _CurrencyAmountDetail(
    currency: $checkedConvert('currency', (v) => v as String),
    isLimit: $checkedConvert('isLimit', (v) => v as bool?),
    suggestedAmounts: $checkedConvert(
      'suggestedAmounts',
      (v) => (v as List<dynamic>?)?.map((e) => e as num).toList(),
    ),
    minAmount: $checkedConvert('minAmount', (v) => v as num),
    maxAmount: $checkedConvert('maxAmount', (v) => v as num),
  );
  return val;
});

Map<String, dynamic> _$CurrencyAmountDetailToJson(
  _CurrencyAmountDetail instance,
) => <String, dynamic>{
  'currency': instance.currency,
  if (instance.isLimit case final value?) 'isLimit': value,
  if (instance.suggestedAmounts case final value?) 'suggestedAmounts': value,
  'minAmount': instance.minAmount,
  'maxAmount': instance.maxAmount,
};

_CliqDetail _$CliqDetailFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_CliqDetail',
  json,
  ($checkedConvert) {
    final val = _CliqDetail(
      name: $checkedConvert('name', (v) => v as String),
      reference: $checkedConvert('reference', (v) => v as String),
      bankName: $checkedConvert('bankName', (v) => v as String),
      purposeOfPayment: $checkedConvert('purposeOfPayment', (v) => v as String),
    );
    return val;
  },
);

Map<String, dynamic> _$CliqDetailToJson(_CliqDetail instance) =>
    <String, dynamic>{
      'name': instance.name,
      'reference': instance.reference,
      'bankName': instance.bankName,
      'purposeOfPayment': instance.purposeOfPayment,
    };

_BankDetail _$BankDetailFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_BankDetail', json, ($checkedConvert) {
      final val = _BankDetail(
        currency: $checkedConvert('currency', (v) => v as String),
        banks: $checkedConvert(
          'banks',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => Bank.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$BankDetailToJson(_BankDetail instance) =>
    <String, dynamic>{
      'currency': instance.currency,
      'banks': instance.banks.map((e) => e.toJson()).toList(),
    };

_Bank _$BankFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_Bank',
  json,
  ($checkedConvert) {
    final val = _Bank(
      name: $checkedConvert('name', (v) => v as String),
      swiftCode: $checkedConvert('swiftCode', (v) => v as String),
      accountName: $checkedConvert('accountName', (v) => v as String),
      accountAddress: $checkedConvert('accountAddress', (v) => v as String),
      reference: $checkedConvert('reference', (v) => v as String),
      purposeOfPayment: $checkedConvert('purposeOfPayment', (v) => v as String),
      iban: $checkedConvert('iban', (v) => v as String),
      accountNumber: $checkedConvert('accountNumber', (v) => v as String),
      imageUrl: $checkedConvert('imageUrl', (v) => v as String?),
    );
    return val;
  },
);

Map<String, dynamic> _$BankToJson(_Bank instance) => <String, dynamic>{
  'name': instance.name,
  'swiftCode': instance.swiftCode,
  'accountName': instance.accountName,
  'accountAddress': instance.accountAddress,
  'reference': instance.reference,
  'purposeOfPayment': instance.purposeOfPayment,
  'iban': instance.iban,
  'accountNumber': instance.accountNumber,
  if (instance.imageUrl case final value?) 'imageUrl': value,
};
