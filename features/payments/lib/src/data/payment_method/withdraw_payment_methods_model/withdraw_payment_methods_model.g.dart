// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_payment_methods_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawPaymentMethodsModel _$WithdrawPaymentMethodsModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_WithdrawPaymentMethodsModel', json, ($checkedConvert) {
  final val = _WithdrawPaymentMethodsModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => WithdrawalPaymentMethodsData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$WithdrawPaymentMethodsModelToJson(
  _WithdrawPaymentMethodsModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

_WithdrawalPaymentMethodsData _$WithdrawalPaymentMethodsDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_WithdrawalPaymentMethodsData', json, ($checkedConvert) {
  final val = _WithdrawalPaymentMethodsData(
    paymentsMethods: $checkedConvert(
      'paymentsMethods',
      (v) =>
          (v as List<dynamic>)
              .map(
                (e) => WithdrawalPaymentMethodGroup.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList(),
    ),
    noCentsCurrencies: $checkedConvert(
      'noCentsCurrencies',
      (v) => (v as List<dynamic>).map((e) => e as String).toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$WithdrawalPaymentMethodsDataToJson(
  _WithdrawalPaymentMethodsData instance,
) => <String, dynamic>{
  'paymentsMethods': instance.paymentsMethods.map((e) => e.toJson()).toList(),
  'noCentsCurrencies': instance.noCentsCurrencies,
};

_WithdrawalPaymentMethodGroup _$WithdrawalPaymentMethodGroupFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_WithdrawalPaymentMethodGroup', json, ($checkedConvert) {
  final val = _WithdrawalPaymentMethodGroup(
    label: $checkedConvert('label', (v) => v as String),
    methods: $checkedConvert(
      'methods',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) =>
                    WithdrawalPaymentMethod.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    ),
    bankDetails: $checkedConvert(
      'bankDetails',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => BankDetail.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$WithdrawalPaymentMethodGroupToJson(
  _WithdrawalPaymentMethodGroup instance,
) => <String, dynamic>{
  'label': instance.label,
  if (instance.methods?.map((e) => e.toJson()).toList() case final value?)
    'methods': value,
  if (instance.bankDetails?.map((e) => e.toJson()).toList() case final value?)
    'bankDetails': value,
};

_WithdrawalPaymentMethod _$WithdrawalPaymentMethodFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_WithdrawalPaymentMethod', json, ($checkedConvert) {
  final val = _WithdrawalPaymentMethod(
    name: $checkedConvert('name', (v) => v as String),
    currencies: $checkedConvert(
      'currencies',
      (v) => (v as List<dynamic>?)?.map((e) => e as String).toList(),
    ),
    tag: $checkedConvert(
      'tag',
      (v) => (v as List<dynamic>?)?.map((e) => e as String).toList(),
    ),
    time: $checkedConvert('time', (v) => v as String?),
    imageUrl: $checkedConvert('imageUrl', (v) => v as String?),
    mop: $checkedConvert(
      'mop',
      (v) => v == null ? null : WithdrawalMopTypes.fromJson(v as String),
    ),
    fee: $checkedConvert('fee', (v) => v as String?),
    additionalData: $checkedConvert(
      'additionalData',
      (v) => v as Map<String, dynamic>?,
    ),
    enabled: $checkedConvert('enabled', (v) => v as bool?),
    currencyAmountDetails: $checkedConvert(
      'currencyAmountDetails',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) => CurrencyAmountDetail.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    ),
    cliq: $checkedConvert(
      'cliq',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => CliqDetail.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$WithdrawalPaymentMethodToJson(
  _WithdrawalPaymentMethod instance,
) => <String, dynamic>{
  'name': instance.name,
  if (instance.currencies case final value?) 'currencies': value,
  if (instance.tag case final value?) 'tag': value,
  if (instance.time case final value?) 'time': value,
  if (instance.imageUrl case final value?) 'imageUrl': value,
  if (instance.mop?.toJson() case final value?) 'mop': value,
  if (instance.fee case final value?) 'fee': value,
  if (instance.additionalData case final value?) 'additionalData': value,
  if (instance.enabled case final value?) 'enabled': value,
  if (instance.currencyAmountDetails?.map((e) => e.toJson()).toList()
      case final value?)
    'currencyAmountDetails': value,
  if (instance.cliq?.map((e) => e.toJson()).toList() case final value?)
    'cliq': value,
};
