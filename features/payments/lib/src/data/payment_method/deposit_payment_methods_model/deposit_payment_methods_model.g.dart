// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'deposit_payment_methods_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DepositPaymentMethodsModel _$DepositPaymentMethodsModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_DepositPaymentMethodsModel', json, ($checkedConvert) {
  final val = _DepositPaymentMethodsModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) => DepositPaymentMethodsData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$DepositPaymentMethodsModelToJson(
  _DepositPaymentMethodsModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

_DepositPaymentMethodsData _$DepositPaymentMethodsDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_DepositPaymentMethodsData', json, ($checkedConvert) {
  final val = _DepositPaymentMethodsData(
    paymentsMethods: $checkedConvert(
      'paymentsMethods',
      (v) =>
          (v as List<dynamic>)
              .map(
                (e) => DepositPaymentMethodGroup.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList(),
    ),
    noCentsCurrencies: $checkedConvert(
      'noCentsCurrencies',
      (v) => (v as List<dynamic>).map((e) => e as String).toList(),
    ),
    maxPollingTime: $checkedConvert('maxPollingTime', (v) => v as num?),
    pollingFrequency: $checkedConvert('pollingFrequency', (v) => v as num?),
  );
  return val;
});

Map<String, dynamic> _$DepositPaymentMethodsDataToJson(
  _DepositPaymentMethodsData instance,
) => <String, dynamic>{
  'paymentsMethods': instance.paymentsMethods.map((e) => e.toJson()).toList(),
  'noCentsCurrencies': instance.noCentsCurrencies,
  if (instance.maxPollingTime case final value?) 'maxPollingTime': value,
  if (instance.pollingFrequency case final value?) 'pollingFrequency': value,
};

_DepositPaymentMethodGroup _$DepositPaymentMethodGroupFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_DepositPaymentMethodGroup', json, ($checkedConvert) {
  final val = _DepositPaymentMethodGroup(
    label: $checkedConvert('label', (v) => v as String),
    methods: $checkedConvert(
      'methods',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) => DepositPaymentMethod.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    ),
    bankDetails: $checkedConvert(
      'bankDetails',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => BankDetail.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$DepositPaymentMethodGroupToJson(
  _DepositPaymentMethodGroup instance,
) => <String, dynamic>{
  'label': instance.label,
  if (instance.methods?.map((e) => e.toJson()).toList() case final value?)
    'methods': value,
  if (instance.bankDetails?.map((e) => e.toJson()).toList() case final value?)
    'bankDetails': value,
};

_DepositPaymentMethod _$DepositPaymentMethodFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_DepositPaymentMethod', json, ($checkedConvert) {
  final val = _DepositPaymentMethod(
    name: $checkedConvert('name', (v) => v as String),
    currencies: $checkedConvert(
      'currencies',
      (v) => (v as List<dynamic>?)?.map((e) => e as String).toList(),
    ),
    tag: $checkedConvert(
      'tag',
      (v) => (v as List<dynamic>?)?.map((e) => e as String).toList(),
    ),
    time: $checkedConvert('time', (v) => v as String?),
    imageUrl: $checkedConvert('imageUrl', (v) => v as String?),
    mop: $checkedConvert(
      'mop',
      (v) => $enumDecodeNullable(_$DepositMopEnumMap, v),
    ),
    fee: $checkedConvert('fee', (v) => v as String?),
    additionalData: $checkedConvert(
      'additionalData',
      (v) => v as Map<String, dynamic>?,
    ),
    enabled: $checkedConvert('enabled', (v) => v as bool?),
    currencyAmountDetails: $checkedConvert(
      'currencyAmountDetails',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) => CurrencyAmountDetail.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    ),
    cliq: $checkedConvert(
      'cliq',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => CliqDetail.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$DepositPaymentMethodToJson(
  _DepositPaymentMethod instance,
) => <String, dynamic>{
  'name': instance.name,
  if (instance.currencies case final value?) 'currencies': value,
  if (instance.tag case final value?) 'tag': value,
  if (instance.time case final value?) 'time': value,
  if (instance.imageUrl case final value?) 'imageUrl': value,
  if (_$DepositMopEnumMap[instance.mop] case final value?) 'mop': value,
  if (instance.fee case final value?) 'fee': value,
  if (instance.additionalData case final value?) 'additionalData': value,
  if (instance.enabled case final value?) 'enabled': value,
  if (instance.currencyAmountDetails?.map((e) => e.toJson()).toList()
      case final value?)
    'currencyAmountDetails': value,
  if (instance.cliq?.map((e) => e.toJson()).toList() case final value?)
    'cliq': value,
};

const _$DepositMopEnumMap = {
  DepositMop.card: 'card',
  DepositMop.apple_pay: 'apple_pay',
  DepositMop.google_pay: 'google_pay',
  DepositMop.bridgerpay: 'bridgerpay',
  DepositMop.bank: 'bank',
};
