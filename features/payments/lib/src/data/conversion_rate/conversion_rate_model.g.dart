// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversion_rate_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ConversionRateModel _$ConversionRateModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ConversionRateModel', json, ($checkedConvert) {
      final val = _ConversionRateModel(
        success: $checkedConvert('success', (v) => v as bool),
        data: $checkedConvert(
          'data',
          (v) => ConversionRateData.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ConversionRateModelToJson(
  _ConversionRateModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.data.toJson(),
};

_ConversionRateData _$ConversionRateDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ConversionRateData', json, ($checkedConvert) {
      final val = _ConversionRateData(
        rates: $checkedConvert(
          'rates',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => RatesModel.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ConversionRateDataToJson(_ConversionRateData instance) =>
    <String, dynamic>{'rates': instance.rates.map((e) => e.toJson()).toList()};

_RatesModel _$RatesModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_RatesModel', json, ($checkedConvert) {
      final val = _RatesModel(
        fromCurrency: $checkedConvert('fromCurrency', (v) => v as String),
        toCurrency: $checkedConvert('toCurrency', (v) => v as String),
        rate: $checkedConvert('rate', (v) => (v as num).toDouble()),
      );
      return val;
    });

Map<String, dynamic> _$RatesModelToJson(_RatesModel instance) =>
    <String, dynamic>{
      'fromCurrency': instance.fromCurrency,
      'toCurrency': instance.toCurrency,
      'rate': instance.rate,
    };
