// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_fees_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawFeesResponseModel _$WithdrawFeesResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_WithdrawFeesResponseModel', json, ($checkedConvert) {
  final val = _WithdrawFeesResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) =>
          v == null
              ? null
              : WithdrawFeesData.fromJson(v as Map<String, dynamic>),
    ),
    error: $checkedConvert(
      'error',
      (v) => v == null ? null : ErrorData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

_ErrorData _$ErrorDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ErrorData', json, ($checkedConvert) {
      final val = _ErrorData(
        errorCode: $checkedConvert('errorCode', (v) => (v as num).toInt()),
        description: $checkedConvert('description', (v) => v as String),
        fieldErrors: $checkedConvert(
          'fieldErrors',
          (v) => (v as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
              k,
              (e as List<dynamic>).map((e) => e as String).toList(),
            ),
          ),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ErrorDataToJson(_ErrorData instance) =>
    <String, dynamic>{
      'errorCode': instance.errorCode,
      'description': instance.description,
      if (instance.fieldErrors case final value?) 'fieldErrors': value,
    };

_WithdrawFeesData _$WithdrawFeesDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_WithdrawFeesData', json, ($checkedConvert) {
      final val = _WithdrawFeesData(
        fee: $checkedConvert('fee', (v) => v as num),
        currency: $checkedConvert('currency', (v) => v as String),
      );
      return val;
    });

Map<String, dynamic> _$WithdrawFeesDataToJson(_WithdrawFeesData instance) =>
    <String, dynamic>{'fee': instance.fee, 'currency': instance.currency};
