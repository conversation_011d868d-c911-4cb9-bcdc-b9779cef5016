// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AccountModel _$AccountModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_AccountModel', json, ($checkedConvert) {
      final val = _AccountModel(
        success: $checkedConvert('success', (v) => v as bool),
        data: $checkedConvert(
          'data',
          (v) => AccountData.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$AccountModelToJson(_AccountModel instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data.toJson(),
    };

_AccountData _$AccountDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_AccountData', json, ($checkedConvert) {
      final val = _AccountData(
        accounts: $checkedConvert(
          'accounts',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => Account.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$AccountDataToJson(_AccountData instance) =>
    <String, dynamic>{
      'accounts': instance.accounts.map((e) => e.toJson()).toList(),
    };

_Account _$AccountFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Account', json, ($checkedConvert) {
      final val = _Account(
        accountId: $checkedConvert('accountId', (v) => v as String),
        accountStatus: $checkedConvert('accountStatus', (v) => v as String),
        accountType: $checkedConvert('accountType', (v) => v as String),
        accountCurrency: $checkedConvert('accountCurrency', (v) => v as String),
        platformAccountNumber: $checkedConvert(
          'platformAccountNumber',
          (v) => v as String,
        ),
        clientId: $checkedConvert('clientId', (v) => v as String),
        accountGroup: $checkedConvert('accountGroup', (v) => v as String),
        brokerId: $checkedConvert('brokerId', (v) => v as String),
        platformAccountType: $checkedConvert(
          'platformAccountType',
          (v) => v as String,
        ),
        name: $checkedConvert('name', (v) => v as String),
        nickName: $checkedConvert('nickName', (v) => v as String),
        primaryEmail: $checkedConvert('primaryEmail', (v) => v as String),
        leverage: $checkedConvert('leverage', (v) => (v as num).toInt()),
        serverCode: $checkedConvert('serverCode', (v) => v as String),
        platformType: $checkedConvert('platformType', (v) => v as String),
        leadSource: $checkedConvert('leadSource', (v) => v as String),
        balance: $checkedConvert('balance', (v) => v as num),
        margin: $checkedConvert('margin', (v) => v as num),
        equity: $checkedConvert('equity', (v) => v as num),
        profit: $checkedConvert('profit', (v) => v as num),
        grossProfit: $checkedConvert('grossProfit', (v) => v as num),
        dateCreated: $checkedConvert(
          'dateCreated',
          (v) => DateTime.parse(v as String),
        ),
        isDemo: $checkedConvert('isDemo', (v) => v as bool),
        classification: $checkedConvert('classification', (v) => v as String),
        accountIdLong: $checkedConvert(
          'accountIdLong',
          (v) => (v as num).toInt(),
        ),
        credit: $checkedConvert('credit', (v) => v as num),
        accountCurrencyUsdPair: $checkedConvert(
          'accountCurrencyUsdPair',
          (v) => v as String,
        ),
      );
      return val;
    });

Map<String, dynamic> _$AccountToJson(_Account instance) => <String, dynamic>{
  'accountId': instance.accountId,
  'accountStatus': instance.accountStatus,
  'accountType': instance.accountType,
  'accountCurrency': instance.accountCurrency,
  'platformAccountNumber': instance.platformAccountNumber,
  'clientId': instance.clientId,
  'accountGroup': instance.accountGroup,
  'brokerId': instance.brokerId,
  'platformAccountType': instance.platformAccountType,
  'name': instance.name,
  'nickName': instance.nickName,
  'primaryEmail': instance.primaryEmail,
  'leverage': instance.leverage,
  'serverCode': instance.serverCode,
  'platformType': instance.platformType,
  'leadSource': instance.leadSource,
  'balance': instance.balance,
  'margin': instance.margin,
  'equity': instance.equity,
  'profit': instance.profit,
  'grossProfit': instance.grossProfit,
  'dateCreated': instance.dateCreated.toIso8601String(),
  'isDemo': instance.isDemo,
  'classification': instance.classification,
  'accountIdLong': instance.accountIdLong,
  'credit': instance.credit,
  'accountCurrencyUsdPair': instance.accountCurrencyUsdPair,
};
