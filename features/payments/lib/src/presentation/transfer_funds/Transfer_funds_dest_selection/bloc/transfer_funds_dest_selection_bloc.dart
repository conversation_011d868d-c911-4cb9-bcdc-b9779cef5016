import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';

import 'package:payment/src/data/transfer_funds/transfer_funds_request_model.dart';
import 'package:payment/src/domain/usecase/submit_transfer_use_case.dart';

part 'transfer_funds_dest_selection_event.dart';
part 'transfer_funds_dest_selection_state.dart';
part 'transfer_funds_dest_selection_bloc.freezed.dart';

class TransferFundsDestSelectionBloc
    extends
        Bloc<TransferFundsDestSelectionEvent, TransferFundsDestSelectionState> {
  final SubmitTransferUseCase _submitTransferUseCase;
  TransferFundsDestSelectionBloc({
    required SubmitTransferUseCase submitTransferUseCase,
  }) : _submitTransferUseCase = submitTransferUseCase,
       super(TransferFundsDestSelectionState()) {
    on<_SelectDestinationAccount>(_selectDestinationAccount);
    on<_TransferFunds>(_transferFunds);
    on<_ChangeButtonStateAndUpdateAmount>(_changeButtonStateAndUpdateAmount);
    on<_UpdateEmptyState>(_updateEmptyState);
  }

  void _selectDestinationAccount(
    _SelectDestinationAccount event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) {
    emit(state.copyWith(destinationAccount: event.account));
  }

  FutureOr<void> _transferFunds(
    _TransferFunds event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) async {
    emit(
      state.copyWith(
        processState: TransferFundsDestSelectionProcessState.loadingTransfer(),
      ),
    );
    const DEFAULT_CONVERSION_RATE = 1.0;
    final amount = double.parse(state.amount!);
    final convertedAmount = double.parse(
      state.convertedAmount ?? state.amount!,
    );
    final bool isConversionEnabled =
        event.sourceAccount.accountCurrency !=
        event.destinationAccount.accountCurrency;
    final result =
        await _submitTransferUseCase(
          model: TransferFundsRequestModel(
            accountInfo: AccountInfo(
              sourceTradingAccountId: event.sourceAccount.accountId,
              sourceAccountCurrency: event.sourceAccount.accountCurrency,
              destinationTradingAccountId: event.destinationAccount.accountId,
              destinationAccountCurrency:
                  event.destinationAccount.accountCurrency,
            ),
            amount: AmountInfo(
              conversionRateString:
                  isConversionEnabled
                      ? _getRateFromListOfConversionModel(
                        state.conversionRateModel!.data.rates,
                        event.sourceAccount.accountCurrency,
                        event.destinationAccount.accountCurrency,
                      ).toString()
                      : DEFAULT_CONVERSION_RATE.toString(),
              amount: amount,
              convertedAmount: isConversionEnabled ? convertedAmount : amount,
              conversionRate:
                  isConversionEnabled
                      ? _getRateFromListOfConversionModel(
                        state.conversionRateModel!.data.rates,
                        event.sourceAccount.accountCurrency,
                        event.destinationAccount.accountCurrency,
                      )
                      : DEFAULT_CONVERSION_RATE,
            ),
          ),
        ).run();

    result.fold(
      (l) {
        if (!isClosed) {
          emit(
            state.copyWith(
              processState:
                  TransferFundsDestSelectionProcessState.errorTransfer(),
            ),
          );
        }
      },
      (r) {
        if (r.success) {
          if (!isClosed) {
            emit(
              state.copyWith(
                processState:
                    TransferFundsDestSelectionProcessState.successTransfer(
                      event.sourceAccount,
                      event.destinationAccount,
                      isConversionEnabled
                          ? state.convertedAmount!
                          : state.amount!,
                    ),
              ),
            );
          }
        } else {
          if (!isClosed) {
            emit(
              state.copyWith(
                processState:
                    TransferFundsDestSelectionProcessState.errorTransfer(),
              ),
            );
          }
        }
      },
    );
  }

  FutureOr<void> _changeButtonStateAndUpdateAmount(
    _ChangeButtonStateAndUpdateAmount event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) {
    emit(
      state.copyWith(
        amount: event.amount,
        convertedAmount: event.convertedAmount,
        isButtonDisabled: event.isDisabled,
        conversionRateModel: event.conversionRateModel,
      ),
    );
  }

  FutureOr<void> _updateEmptyState(
    _UpdateEmptyState event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) {
    emit(
      state.copyWith(
        hasAccounts: event.hasAccounts,
        hasWallets: event.hasWallets,
        isCurrentTabEmpty: event.isCurrentTabEmpty,
        currentTabIndex: event.currentTabIndex,
      ),
    );
  }

  double _getRateFromListOfConversionModel(
    List<RatesModel> list,
    String sourceCurrency,
    String targetCurrency,
  ) {
    final rate =
        list
            .firstWhere(
              (element) => element.fromCurrency == sourceCurrency,
              orElse:
                  () =>
                      throw Exception(
                        'No conversion rate found for $sourceCurrency to $targetCurrency',
                      ),
            )
            .rate;
    return rate;
  }
}
