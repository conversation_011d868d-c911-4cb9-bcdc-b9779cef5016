import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/bloc/transfer_funds_dest_selection_bloc.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/error_transfer_screen.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/source_account_card.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/success_transfer_screen.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/amount_conversion_widget.dart';

class TransferFundsDestSelectionScreen extends StatelessWidget {
  const TransferFundsDestSelectionScreen({
    super.key,
    required this.account,
    required this.originRoute,
  });
  final Account account;
  final String originRoute;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    return BlocProvider(
      create:
          (blocProviderContext) =>
              diContainer<TransferFundsDestSelectionBloc>(),
      child: BlocBuilder<
        TransferFundsDestSelectionBloc,
        TransferFundsDestSelectionState
      >(
        buildWhen:
            (previous, current) =>
                (previous.processState != current.processState ||
                    previous.destinationAccount != current.destinationAccount ||
                    previous.isCurrentTabEmpty != current.isCurrentTabEmpty ||
                    previous.hasAccounts != current.hasAccounts ||
                    previous.hasWallets != current.hasWallets),
        builder: (blocBuilderContext, state) {
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            appBar: switch (state.processState) {
              ErrorTransfer() || SuccessTransfer() => null,
              (_) => DuploAppBar(title: "Transfer"),
            },
            body: switch (state.processState) {
              ErrorTransfer() => ErrorTransferScreen(),
              LoadingTransfer() => LoadingView(),
              SuccessTransfer(
                :final sourceAccount,
                :final destinationAccount,
                :final amount,
              ) =>
                SuccessTransferScreen(
                  sourceAccount: sourceAccount,
                  destinationAccount: destinationAccount,
                  amount: amount,
                  originRoute: originRoute,
                ),
              StartedScreen() => SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const SizedBox(height: 8),
                      Align(
                        alignment: AlignmentDirectional.centerStart,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DuploText(
                              text: "Source account or wallet",
                              style: style.textLg,
                              color: theme.text.textPrimary,
                              fontWeight: DuploFontWeight.semiBold,
                            ),
                            const SizedBox(height: 8),
                            SourceAccountCard(account: account),
                            const SizedBox(height: 23),
                            DuploText(
                              text: "Destination account or wallet",
                              style: style.textLg,
                              color: theme.text.textPrimary,
                              fontWeight: DuploFontWeight.semiBold,
                            ),
                            const SizedBox(height: 8),
                            DuploText(
                              text:
                                  "Select the account where you'd like to receive the funds.",
                              style: style.textSm,
                              color: theme.text.textSecondary,
                            ),
                            const SizedBox(height: 23),
                          ],
                        ),
                      ),
                      AccountListWidget(
                        args: (
                          selectByHighestBalance: false,
                          excludeAccountNumber: account.platformAccountNumber,
                          onEmptyStateChanged: ({
                            required bool hasAccounts,
                            required bool hasWallets,
                            required bool isCurrentTabEmpty,
                            required int currentTabIndex,
                          }) {
                            blocBuilderContext
                                .read<TransferFundsDestSelectionBloc>()
                                .add(
                                  TransferFundsDestSelectionEvent.updateEmptyState(
                                    hasAccounts: hasAccounts,
                                    hasWallets: hasWallets,
                                    isCurrentTabEmpty: isCurrentTabEmpty,
                                    currentTabIndex: currentTabIndex,
                                  ),
                                );
                          },
                        ),
                        onAccountSelected: (Account destinationAccount) {
                          blocBuilderContext
                              .read<TransferFundsDestSelectionBloc>()
                            ..add(
                              TransferFundsDestSelectionEvent.selectDestinationAccount(
                                destinationAccount,
                              ),
                            );
                        },
                      ),
                      // Only show DepositAmount if current tab is not empty AND destination account is selected
                      if (!state.isCurrentTabEmpty &&
                          state.destinationAccount != null)
                        AmountConversionWidget(
                          key: ValueKey(
                            '${account.accountCurrency}_${state.destinationAccount!.accountCurrency}',
                          ),
                          args: (
                            transferCurrency: account.accountCurrency,
                            transferCurrencyImage: '',
                            currencyAmountDetails:
                                (account.accountCurrency !=
                                        state
                                            .destinationAccount
                                            ?.accountCurrency)
                                    ? [
                                      CurrencyAmountDetail(
                                        currency:
                                            state
                                                .destinationAccount!
                                                .accountCurrency,
                                        maxAmount: account.balance,
                                        minAmount: 1,
                                      ),
                                      CurrencyAmountDetail(
                                        currency: account.accountCurrency,
                                        maxAmount: account.balance,
                                        minAmount: 1,
                                      ),
                                    ]
                                    : [
                                      CurrencyAmountDetail(
                                        currency: account.accountCurrency,
                                        maxAmount: account.balance,
                                        minAmount: 1,
                                      ),
                                    ],
                            currencies: [
                              state.destinationAccount?.accountCurrency ??
                                  account.accountCurrency,
                            ],
                            showSuggestedAmounts: false,
                            isStartWithConversionRate:
                                (account.accountCurrency !=
                                    state.destinationAccount?.accountCurrency),
                            targetCurrency:
                                state.destinationAccount!.accountCurrency,
                            externalErrorMessage: null,
                            paymentType: PaymentType.transfer,
                            conversionType:
                                ConversionType.accountToTargetCurrency,
                          ),
                          onAmountChange: ({
                            required String amount,
                            required bool isAmountValid,
                            required String convertedAmount,
                            required RatesModel? ratesModel,
                            required ConversionRateModel? conversionRateData,
                            String? conversionRateString,
                            String? targetCurrency,
                          }) {
                            blocBuilderContext
                                .read<TransferFundsDestSelectionBloc>()
                                .add(
                                  TransferFundsDestSelectionEvent.changeButtonStateAndUpdateAmount(
                                    amount: amount,
                                    isDisabled: !isAmountValid,
                                    convertedAmount: amount,
                                    conversionRateModel: conversionRateData,
                                  ),
                                );
                          },
                        ),
                      // Show message when current tab is empty
                      if (state.isCurrentTabEmpty) const SizedBox(),
                      if (!state.isCurrentTabEmpty &&
                          state.destinationAccount != null)
                        BlocBuilder<
                          TransferFundsDestSelectionBloc,
                          TransferFundsDestSelectionState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  (previous.isButtonDisabled !=
                                          current.isButtonDisabled ||
                                      previous.amount != current.amount),
                          builder: (buttonContext, buttonState) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 24.0,
                              ),
                              child: DuploButton.defaultPrimary(
                                isDisabled: buttonState.isButtonDisabled,
                                title: "Continue",
                                useFullWidth: true,
                                trailingIcon:
                                    Assets.images
                                        .chevronRightDirectional(buttonContext)
                                        .keyName,
                                onTap: () {
                                  buttonContext
                                      .read<TransferFundsDestSelectionBloc>()
                                    ..add(
                                      TransferFundsDestSelectionEvent.transferFunds(
                                        sourceAccount: account,
                                        destinationAccount:
                                            state.destinationAccount!,
                                      ),
                                    );
                                },
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ),
            },
          );
        },
      ),
    );
  }
}
