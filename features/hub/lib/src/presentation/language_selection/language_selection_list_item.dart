import 'package:duplo/duplo.dart';
import 'package:duplo/src/models/language_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/presentation/language_selection/bloc/language_selection_bloc.dart';

class LanguageSelectionListItem extends StatelessWidget {
  const LanguageSelectionListItem();

  @override
  Widget build(BuildContext context) {
    final loc = EquitiLocalization.of(context);

    return BlocProvider(
      create:
          (_) =>
              diContainer<LanguageSelectionBloc>()
                ..add(LanguageSelectionEvent.fetchLanguageSelection()),
      child: BlocBuilder<LanguageSelectionBloc, LanguageSelectionState>(
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          return switch (state) {
            LanguageSelectionInitial() => Container(),
            LanguageSelectionSuccess(:final language) => Semantics(
              identifier: 'language_selection_option',
              button: true,
              child: GestureDetector(
                onTap: () {
                  final allOptions = <SelectionOptionModel>[];
                  SelectionOptionModel selected = SelectionOptionModel(
                    displayText: language.name,
                    identifier: language.displayCode,
                  );
                  LanguageModelOptions.supportedLanguages.forEach((element) {
                    final model = SelectionOptionModel(
                      displayText: element.name,
                      identifier: element.displayCode,
                    );
                    if (model.identifier == language.displayCode) {
                      selected = model;
                    }
                    allOptions.add(model);
                  });

                  final selection = TextSelectionComponentScreen(
                    pageTitle: loc.hub_language,
                    buttonTitle: loc.hub_select,
                    options: allOptions,
                    selected: selected,
                    onSelection: (option) {
                      final newSelected = option.identifier.toLanguageModel();
                      if (newSelected.displayCode != language.displayCode) {
                        blocBuilderContext.read<LanguageSelectionBloc>().add(
                          LanguageSelectionEvent.changeLanguageSelection(
                            newSelected,
                          ),
                        );
                      }

                      Navigator.pop(blocBuilderContext);
                    },
                  );

                  DuploSheet.showNonScrollableModalSheet<void>(
                    context: context,
                    title: loc.hub_language,
                    content: (ctx) => selection,
                  );
                },
                child: DuploTap(
                  child: DuploLabelInfoChevronWidget(
                    title: loc.hub_language,
                    valueText: language.name,
                  ),
                ),
              ),
            ),
          };
        },
      ),
    );
  }
}
