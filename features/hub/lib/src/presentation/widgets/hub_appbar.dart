import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:hub/src/assets/assets.gen.dart' as hub;

class HubAppBar extends StatelessWidget implements PreferredSizeWidget {
  const HubAppBar({super.key, this.onMenuPressed, this.actions});

  final VoidCallback? onMenuPressed;
  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return AppBar(
      backgroundColor: theme.background.bgPrimary,
      elevation: 0,
      leading:
          onMenuPressed != null
              ? IconButton(
                icon: Assets.images.dotGrid.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgPrimary,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: onMenuPressed,
              )
              : null,
      actions: actions,
      centerTitle: true,
      title: hub.Assets.images.logo.svg(),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
