import 'package:customer_support_chat/customer_support_chat.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/navigation/hub_navigation.dart';
import 'package:hub/src/presentation/product_hub_settings/bloc/product_hub_settings_bloc.dart';
import 'package:hub/src/presentation/product_hub_settings/widgets/hub_profile_header_widget.dart';
import 'package:hub/src/presentation/product_hub_settings/widgets/hub_settings_section_item_widget.dart';
import 'package:hub/src/presentation/product_hub_settings/widgets/hub_settings_section_widget.dart';
import 'package:hub/src/presentation/widgets/hub_appbar.dart';
import 'package:monitoring/monitoring.dart';
import 'package:url_launcher/url_launcher.dart';

class HubSettingsScreen extends StatelessWidget {
  const HubSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: HubAppBar(),
      body: BlocProvider(
        create: (_) => diContainer<ProductHubSettingsBloc>(),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            BlocBuilder<ProductHubSettingsBloc, ProductHubSettingsState>(
              buildWhen:
                  (previous, current) =>
                      previous.processState != current.processState,
              builder:
                  (blocBuilderContext, state) => switch (state.processState) {
                    ProductHubSettingsLoadingProcessState() => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    ProductHubSettingsSuccessProcessState() =>
                      HubProfileHeaderWidget(
                        firstName: state.clientProfile?.firstName ?? '',
                        lastName: state.clientProfile?.lastName ?? '',
                        email: state.clientProfile?.email ?? '',
                        onTap: () {
                          diContainer<HubNavigation>().gotoProfile(
                            state.clientProfile!,
                          );
                        },
                      ),
                    ProductHubSettingsErrorProcessState() => Center(
                      child: Text("Profile Error"),
                    ),
                  },
            ),
            _HubBody(),
          ],
        ),
      ),
    );
  }
}

class _HubBody extends StatelessWidget {
  const _HubBody();

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        const SizedBox(height: 32),
        HubSettingsSectionWidget(
          title: localization.hub_services,
          items: [
            HubSettingsSectionItemWidget(
              icon: Assets.images.hub.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_hub,
              onTap: () => Navigator.of(context).pop(),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.trading.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_trading,
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToTrading(),
                  ),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.gold.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_gold,
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToGold(),
                  ),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.wealth.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_wealth,
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToWealth(),
                  ),
            ),
          ],
        ),
        const SizedBox(height: 32),
        HubSettingsSectionWidget(
          title: localization.hub_payments,
          items: [
            HubSettingsSectionItemWidget(
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToDepositPaymentOptions(),
                  ),
              icon: Assets.images.deposit.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_deposit,
            ),
            HubSettingsSectionItemWidget(
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToWithdrawPaymentOptions(),
                  ),
              icon: Assets.images.withdraw.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_withdraw,
            ),
            HubSettingsSectionItemWidget(
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToTransferOptions(),
                  ),
              icon: Assets.images.transfer.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_transfer,
            ),
          ],
        ),
        const SizedBox(height: 32),
        HubSettingsSectionWidget(
          title: localization.hub_accountsAndWallets,
          items: [
            HubSettingsSectionItemWidget(
              icon: Assets.images.performance.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_performance,
              onTap:
                  () =>
                      diContainer<HubNavigation>().gotoHistoricalPerformance(),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.accounts.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_accounts,
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToAccounts(),
                  ),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.wallet.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_wallets,
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToWallets(),
                  ),
            ),
          ],
        ),
        const SizedBox(height: 32),
        HubSettingsSectionWidget(
          title: localization.hub_settings,
          items: [
            HubSettingsSectionItemWidget(
              icon: Assets.images.profile.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_profile,
              onTap:
                  () => context.read<ProductHubSettingsBloc>().add(
                    const ProductHubSettingsEvent.goToProfile(),
                  ),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.security.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_security,
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.settings.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_settings,
            ),
          ],
        ),
        const SizedBox(height: 32),
        HubSettingsSectionWidget(
          title: localization.hub_more,
          items: [
            HubSettingsSectionItemWidget(
              icon: Assets.images.learn.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_learn,
              onTap: () => _launchLearnUrl(localization),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.support.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_support,
              onTap: () {
                try {
                  diContainer<CustomerSupportChat>().openChat();
                } on PlatformException catch (e) {
                  diContainer<LoggerBase>().logError(e.toString());
                }
              },
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.legal.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_legal,
              onTap: () => _launchLegalDocumentsUrl(localization),
            ),
            HubSettingsSectionItemWidget(
              icon: Assets.images.logout.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: localization.hub_signOut,
              onTap: () {
                _showLogoutConfirmationDialog(context);
              },
            ),
          ],
        ),
      ],
    );
  }

  void _launchLegalDocumentsUrl(EquitiLocalization localization) {
    try {
      launchUrl(
        Uri.parse(localization.hub_legalDocumentsUrl),
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      debugPrint('Error launching legal documents URL: $e');
    }
  }

  void _launchLearnUrl(EquitiLocalization localization) {
    try {
      launchUrl(
        Uri.parse(localization.hub_educationUrl),
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      debugPrint('Error launching education URL: $e');
    }
  }

  void _showLogoutConfirmationDialog(BuildContext context) {
    DuploLogoutBottomSheet.show(
      context: context,
      onLogout: () {
        diContainer<HubNavigation>().logout();
      },
    );
  }
}
