import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:hub/src/presentation/product_hub_settings/widgets/hub_settings_section_item_widget.dart';
import 'package:prelude/prelude.dart';

class HubSettingsSectionWidget extends StatelessWidget {
  const HubSettingsSectionWidget({
    super.key,
    required this.title,
    required this.items,
    this.minItemCount = 4,
  });

  final String title;
  final int minItemCount;
  final List<HubSettingsSectionItemWidget> items;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsetsDirectional.only(start: 16.0),
          child: DuploText(
            text: title,
            style: textStyles.textSm,
            fontWeight: DuploFontWeight.semiBold,
            color: theme.text.textPrimary,
            textAlign: TextAlign.start,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            ...List<Widget>.generate(
              items.length > minItemCount ? items.length : minItemCount,
              (index) =>
                  index < items.length
                      ? Expanded(child: items.elementAtOrNull(index)!)
                      : Expanded(child: const SizedBox(width: 80)),
            ),
          ],
        ),
      ],
    );
  }
}
