import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/presentation/theme_selection/bloc/theme_selection_bloc.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:hub/src/assets/assets.gen.dart' as hub;

class ThemeSelectionListItem extends StatelessWidget {
  const ThemeSelectionListItem();

  @override
  Widget build(BuildContext context) {
    final textStyle = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create:
          (_) =>
              diContainer<ThemeSelectionBloc>()
                ..add(ThemeSelectionEvent.fetchThemeSelection()),
      child: <PERSON><PERSON>uilder<ThemeSelectionBloc, ThemeSelectionState>(
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          return switch (state) {
            ThemeSelectionInitial() => Container(),
            ThemeSelectionSuccess(:final mode) => Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 16),
                  DuploText(
                    text: localization.hub_chooseTheme,
                    style: textStyle.textSm,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textSecondary,
                  ),
                  SizedBox(height: 16),
                  Row(
                    children: [
                      Spacer(),
                      _ThemeOptionTile(
                        imagePath: hub.Assets.images.systemThemeBack.keyName,
                        label: localization.hub_system,
                        isSelected: mode == AppThemeMode.system,
                        semanticLabel: 'theme_option_system',
                        onTap: () {
                          blocBuilderContext.read<ThemeSelectionBloc>().add(
                            ThemeSelectionEvent.changeThemeSelection(
                              AppThemeMode.system,
                            ),
                          );
                        },
                      ),
                      Spacer(),
                      _ThemeOptionTile(
                        imagePath: hub.Assets.images.lightThemeBack.keyName,
                        label: localization.hub_light,
                        isSelected: mode == AppThemeMode.light,
                        semanticLabel: 'theme_option_light',
                        onTap: () {
                          blocBuilderContext.read<ThemeSelectionBloc>().add(
                            ThemeSelectionEvent.changeThemeSelection(
                              AppThemeMode.light,
                            ),
                          );
                        },
                      ),
                      Spacer(),
                      _ThemeOptionTile(
                        imagePath: hub.Assets.images.darkThemeBack.keyName,
                        label: localization.hub_dark,
                        isSelected: mode == AppThemeMode.dark,
                        semanticLabel: 'theme_option_dark',
                        onTap: () {
                          blocBuilderContext.read<ThemeSelectionBloc>().add(
                            ThemeSelectionEvent.changeThemeSelection(
                              AppThemeMode.dark,
                            ),
                          );
                        },
                      ),
                      Spacer(),
                    ],
                  ),
                ],
              ),
            ),
          };
        },
      ),
    );
  }
}

class _ThemeOptionTile extends StatelessWidget {
  final String imagePath;
  final bool isSelected;
  final String label;
  final VoidCallback onTap;
  final String? semanticLabel;

  const _ThemeOptionTile({
    Key? key,
    required this.imagePath,
    required this.isSelected,
    required this.label,
    required this.onTap,
    this.semanticLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;

    final child = DuploTap(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              SvgPicture.asset(imagePath),
              Positioned(
                bottom: 6,
                left: 6,
                child: CircleAvatar(
                  radius: 12,
                  backgroundColor: isSelected ? Colors.teal : Colors.white,
                  child:
                      isSelected
                          ? Icon(Icons.check, size: 14, color: Colors.white)
                          : null,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          DuploText(
            text: label,
            style: style.textSm,
            color: theme.text.textPrimary,
            fontWeight: DuploFontWeight.semiBold,
          ),
        ],
      ),
    );

    // Wrap with Semantics if semantic label is provided
    if (semanticLabel != null) {
      return Semantics(identifier: semanticLabel!, button: true, child: child);
    }

    return child;
  }
}
