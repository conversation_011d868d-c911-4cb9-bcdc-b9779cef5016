part of '../hub_screen.dart';

class _SectionItemWidget extends StatelessWidget {
  const _SectionItemWidget({
    required this.icon,
    required this.title,
    required this.backgroundColor,
    required this.onTap,
    this.isVertical = true,
  });
  final Widget icon;
  final String title;
  final bool isVertical;
  final Color backgroundColor;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;

    return DuploTap(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child:
            isVertical
                ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: icon),
                    DuploText(
                      text: title,
                      style: textStyles.textXxs,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                )
                : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Align(
                        alignment: Alignment.bottomLeft,
                        child: DuploText(
                          text: title,
                          style: textStyles.textXxs,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    Expanded(child: icon),
                  ],
                ),
      ),
    );
  }
}
