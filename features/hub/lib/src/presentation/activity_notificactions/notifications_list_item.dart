import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:hub/src/data/api/account_activity_model.dart';
import 'package:prelude/prelude.dart';

class NotificationsListItem extends StatelessWidget {
  const NotificationsListItem({super.key, required this.notification});
  final ActivityItem notification;
  @override
  Widget build(BuildContext context) {
    final TextDirection currentDirection = Directionality.of(context);
    final bool isRTL = currentDirection == TextDirection.rtl;

    return Padding(
      padding: EdgeInsets.fromLTRB(isRTL ? 16.0 : 8, 8, isRTL ? 8.0 : 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DuploText(
            text: notification.activityText,
            style: context.duploTextStyles.textSm,
            color: context.duploTheme.text.textSecondary,
          ),
          SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              DuploTagContainer.md(
                text: notification.activityDetail?.statusName ?? '',
                type:
                    notification.activityDetail?.status == "Accepted"
                        ? DuploTagType.success
                        : notification.activityDetail?.status == "Rejected"
                        ? DuploTagType.error
                        : DuploTagType.warning,
              ),
              DuploText(
                text: EquitiFormatter.formatDayMonthYearTime(
                  notification.dateTime,
                ),
                style: context.duploTextStyles.textXs,
                color: context.duploTheme.text.textQuaternary,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
