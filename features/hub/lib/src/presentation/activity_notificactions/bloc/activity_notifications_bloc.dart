import 'package:clock/clock.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hub/src/data/api/account_activity_model.dart';
import 'package:hub/src/data/api/account_activity_request_model.dart';
import 'package:hub/src/domain/usecase/get_account_activity_use_case.dart';

part 'activity_notifications_event.dart';
part 'activity_notifications_state.dart';
part 'activity_notifications_bloc.freezed.dart';

class ActivityNotificationsBloc
    extends Bloc<ActivityNotificationsEvent, ActivityNotificationsState> {
  final GetAccountActivityUseCase _getAccountActivityUseCase;
  final Clock _clock;
  ActivityNotificationsBloc(this._getAccountActivityUseCase, this._clock)
    : super(ActivityNotificationsState()) {
    on<_FetchActivityNotifications>(_fetchAccountActivity);
  }

  Future<void> _fetchAccountActivity(
    _FetchActivityNotifications event,
    Emitter<ActivityNotificationsState> emit,
  ) async {
    final String fromDateStr =
        DateTime.parse('2020-03-01T09:12:26.293Z').toIso8601String();
    final String toDateStr = _clock.now().toIso8601String();

    final body = AccountActivityRequestModel(
      isFunding: false,
      fromDate: fromDateStr,
      toDate: toDateStr,
      pageNumber: state.currentPage,
      pageSize: 500,
      sortingOrder: "Descending",
    );
    final result = await _getAccountActivityUseCase(body).run();
    if (isClosed) return;

    result.fold(
      (exception) => emit(
        state.copyWith(
          processState: NotificationsProcessState.error(exception),
        ),
      ),
      (fundingResponse) {
        final List<ActivityItem> newFundingItems = List<ActivityItem>.of(
          state.activityItems,
        );

        if (fundingResponse.list != null) {
          newFundingItems.addAll(fundingResponse.list!);
        }

        final bool emptyResults =
            newFundingItems.isEmpty && state.currentPage == 1;

        final processState =
            emptyResults
                ? NotificationsProcessState.empty()
                : NotificationsProcessState.success();

        emit(
          state.copyWith(
            processState: processState,
            activityItems: newFundingItems,
            currentPage: state.currentPage + 1,
            creationDate: state.creationDate,
            count: fundingResponse.totalCount,

            status: state.status,
          ),
        );
      },
    );
  }
}
