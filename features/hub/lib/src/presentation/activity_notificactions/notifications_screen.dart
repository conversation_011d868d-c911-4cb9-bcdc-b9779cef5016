import 'package:duplo/duplo.dart';
import 'package:hub/src/assets/assets.gen.dart' as trader;
import 'package:hub/src/di/di_container.dart';
import 'bloc/activity_notifications_bloc.dart';
import 'notifications_list_item.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

void showNotificationsScreen({required BuildContext parentContext}) {
  DuploSheet.showModalSheet<void>(
    context: parentContext,
    routeSettings: const RouteSettings(name: 'activity_notifications'),
    title: EquitiLocalization.of(parentContext).trader_notifications,
    hideTitle: true,
    navBarHeight: 90,
    hasTopBarLayer: false,
    topBarTitle: DuploText(
      text: EquitiLocalization.of(parentContext).trader_notifications,
      textAlign: TextAlign.center,
      fontWeight: DuploFontWeight.semiBold,
      style: parentContext.duploTextStyles.textSm,

      color: parentContext.duploTheme.text.textPrimary,
    ),
    hideCloseButton: true,
    leadingWidget: Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              Navigator.pop(parentContext);
            },
            icon: Icon(
              Icons.arrow_back,
              size: 24,
              color: parentContext.duploTheme.foreground.fgSecondary,
            ),
          ),
          Spacer(),
          DuploText(
            text: EquitiLocalization.of(parentContext).trader_notifications,
            textAlign: TextAlign.start,
            fontWeight: DuploFontWeight.semiBold,
            style: parentContext.duploTextStyles.textSm,

            color: parentContext.duploTheme.text.textPrimary,
          ),
          Spacer(),
          Container(width: 40),
        ],
      ),
    ),
    content:
        (context) => BlocProvider(
          create:
              (_) =>
                  diContainer<ActivityNotificationsBloc>()
                    ..add(ActivityNotificationsEvent.fetchNotifications()),
          child: NotificationsScreen(),
        ),
  );
}

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (_) =>
              diContainer<ActivityNotificationsBloc>()
                ..add(ActivityNotificationsEvent.fetchNotifications()),
      child: Container(
        padding: EdgeInsets.all(16),
        height: MediaQuery.sizeOf(context).height * .8,
        child: BlocBuilder<
          ActivityNotificationsBloc,
          ActivityNotificationsState
        >(
          buildWhen:
              (previous, current) =>
                  current.processState != previous.processState,
          builder: (ctx, state) {
            return switch (state.processState) {
              NotificationsSuccessState() => PagedView.list(
                physics: ClampingScrollPhysics(),
                itemCount: state.activityItems.length,
                onFetchData: () {
                  ctx.read<ActivityNotificationsBloc>().add(
                    ActivityNotificationsEvent.fetchNotifications(),
                  );
                },
                separatorBuilder:
                    (p0, p1) => Padding(
                      padding: const EdgeInsets.only(top: 16.0, bottom: 16),
                      child: Divider(
                        color: context.duploTheme.border.borderSecondary,
                      ),
                    ),
                itemBuilder:
                    (builderCtx, index) => NotificationsListItem(
                      notification: state.activityItems[index],
                    ),
              ),
              NotificationsLoadingState() => DuploShimmerList(
                hasLeading: false,
                hasTrailing: false,
                height: 80,
              ),
              NotificationsEmptyState() => EmptyOrErrorStateComponent.empty(
                svgImage: trader.Assets.images.notifications.svg(),
                title: EquitiLocalization.of(context).trader_noNotifications,
                description:
                    EquitiLocalization.of(context).trader_noNotificationsDesc,
              ),
              NotificationsErrorState() =>
                EmptyOrErrorStateComponent.defaultError(
                  ctx,
                  () => ctx.read<ActivityNotificationsBloc>().add(
                    ActivityNotificationsEvent.fetchNotifications(),
                  ),
                ),
            };
          },
        ),
      ),
    );
  }
}
