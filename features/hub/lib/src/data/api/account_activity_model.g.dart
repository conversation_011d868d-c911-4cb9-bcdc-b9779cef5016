// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_activity_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AccountActivityModel _$AccountActivityModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_AccountActivityModel', json, ($checkedConvert) {
  final val = _AccountActivityModel(
    list: $checkedConvert(
      'list',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => ActivityItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    totalCount: $checkedConvert('totalCount', (v) => (v as num?)?.toInt() ?? 0),
  );
  return val;
});

Map<String, dynamic> _$AccountActivityModelToJson(
  _AccountActivityModel instance,
) => <String, dynamic>{
  if (instance.list?.map((e) => e.toJson()).toList() case final value?)
    'list': value,
  'totalCount': instance.totalCount,
};

_ActivityItem _$ActivityItemFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_ActivityItem', json, ($checkedConvert) {
  final val = _ActivityItem(
    id: $checkedConvert('id', (v) => (v as num?)?.toInt()),
    activityType: $checkedConvert('activityType', (v) => v as String?),
    activityTypeName: $checkedConvert('activityTypeName', (v) => v as String?),
    dateTime: $checkedConvert(
      'dateTime',
      (v) => v == null ? null : DateTime.parse(v as String),
    ),
    activityDetail: $checkedConvert(
      'activityDetail',
      (v) =>
          v == null ? null : ActivityDetail.fromJson(v as Map<String, dynamic>),
    ),
    operationId: $checkedConvert('operationId', (v) => v as String?),
    activityText: $checkedConvert('activityText', (v) => v as String?),
  );
  return val;
});

Map<String, dynamic> _$ActivityItemToJson(
  _ActivityItem instance,
) => <String, dynamic>{
  if (instance.id case final value?) 'id': value,
  if (instance.activityType case final value?) 'activityType': value,
  if (instance.activityTypeName case final value?) 'activityTypeName': value,
  if (instance.dateTime?.toIso8601String() case final value?) 'dateTime': value,
  if (instance.activityDetail?.toJson() case final value?)
    'activityDetail': value,
  if (instance.operationId case final value?) 'operationId': value,
  if (instance.activityText case final value?) 'activityText': value,
};

_ActivityDetail _$ActivityDetailFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_ActivityDetail', json, ($checkedConvert) {
  final val = _ActivityDetail(
    currency: $checkedConvert('currency', (v) => v as String?),
    amount: $checkedConvert('amount', (v) => (v as num?)?.toDouble()),
    accountNumber: $checkedConvert('accountNumber', (v) => v as String?),
    status: $checkedConvert('status', (v) => v as String?),
    statusName: $checkedConvert('statusName', (v) => v as String?),
    paymentMethod: $checkedConvert('paymentMethod', (v) => v as String?),
    destinationAccount: $checkedConvert(
      'destinationAccount',
      (v) => v as String?,
    ),
    comment: $checkedConvert('comment', (v) => v as String?),
    accountType: $checkedConvert('accountType', (v) => v as String?),
    sourceAccount: $checkedConvert('sourceAccount', (v) => v as String?),
    campaign: $checkedConvert('campaign', (v) => v as String?),
    oldLeverage: $checkedConvert('oldLeverage', (v) => (v as num?)?.toInt()),
    newLeverage: $checkedConvert('newLeverage', (v) => (v as num?)?.toInt()),
    sourceClientName: $checkedConvert('sourceClientName', (v) => v as String?),
    destinationClientName: $checkedConvert(
      'destinationClientName',
      (v) => v as String?,
    ),
  );
  return val;
});

Map<String, dynamic> _$ActivityDetailToJson(
  _ActivityDetail instance,
) => <String, dynamic>{
  if (instance.currency case final value?) 'currency': value,
  if (instance.amount case final value?) 'amount': value,
  if (instance.accountNumber case final value?) 'accountNumber': value,
  if (instance.status case final value?) 'status': value,
  if (instance.statusName case final value?) 'statusName': value,
  if (instance.paymentMethod case final value?) 'paymentMethod': value,
  if (instance.destinationAccount case final value?)
    'destinationAccount': value,
  if (instance.comment case final value?) 'comment': value,
  if (instance.accountType case final value?) 'accountType': value,
  if (instance.sourceAccount case final value?) 'sourceAccount': value,
  if (instance.campaign case final value?) 'campaign': value,
  if (instance.oldLeverage case final value?) 'oldLeverage': value,
  if (instance.newLeverage case final value?) 'newLeverage': value,
  if (instance.sourceClientName case final value?) 'sourceClientName': value,
  if (instance.destinationClientName case final value?)
    'destinationClientName': value,
};
