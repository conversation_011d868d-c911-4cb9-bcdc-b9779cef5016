import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toastification/toastification.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// Usage: The {HubScreen()} app is rendered {scenarios:[hubSuccessScenario]}
Future<void> theAppIsRendered(
  WidgetTester tester,
  Widget app, {
  List<VoidCallback> scenarios = const <VoidCallback>[],
}) async {
  final locale =
      Platform.environment['APP_LOCALE'] == 'ar'
          ? const Locale('ar')
          : const Locale('en');
  final theme =
      Platform.environment['APP_THEME'] == "dark"
          ? DuploThemeData.dark()
          : DuploThemeData.light();
  return await AppTestConfigurator(
    tester: tester,
    app: DuploTheme(
      child: DuploTextStyles(
        locale: locale,
        child: ToastificationWrapper(
          child: MaterialApp(
            home: app,
            localizationsDelegates: EquitiLocalization.localizationsDelegates,
            supportedLocales: EquitiLocalization.supportedLocales,
            locale: locale,
          ),
        ),
      ),
      data: theme,
    ),
    isGoldenTest: true,
    scenarios: scenarios,
    onInit: () async {
      VisibilityDetectorController.instance.updateInterval = Duration.zero;
      await EquitiLocalizationManager.initMock();
    },
  ).run();
}
