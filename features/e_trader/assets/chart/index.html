<!DOCTYPE html>
<html>

<head>
  <title>TradingView Chart</title>
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0">

  <!-- Charting Library -->
  <script src="./charting_library/charting_library.js" defer></script>

  <!-- Custom Chart Logic -->
  <script src="./src/main.js" defer></script>
</head>

<body style="margin:0px;">
  <div id="tv_chart_container"></div>

  <script type="module">
    import('./src/main.js').then((chartModule) => {
      window.flutter_inappwebview.callHandler('init')
        .then((config) => {
          // Set background color based on theme (using Duplo theme colors)
          const isDarkTheme = config.theme === 'Dark';
          const backgroundColor = isDarkTheme ? '#1E1E1E' : '#FFFFFF'; // grayDark750 : baseWhite
          const textColor = isDarkTheme ? '#ffffff' : '#000000';

          // Apply theme to body and container
          document.body.style.backgroundColor = backgroundColor;
          document.body.style.color = textColor;

          const container = document.getElementById('tv_chart_container');
          if (container) {
            container.style.backgroundColor = backgroundColor;
          }

          console.log(`🎨 Applied ${config.theme} theme with background: ${backgroundColor}`);

          chartModule.startChart(
            config.identifier,
            config.symbol,
            config.defaultInterval,
            config.disabledFeatures,
            config.theme,
            config.timezone,
            config.chartType,
            config.locale,
          ).then((widget) => {
              window.tvWidget = widget;
              console.log("✅ tvWidget is now available");
          });
        })
        .catch((error) => {
          console.error('Failed to initialize chart:', error);
        });
    });
  </script>
</body>
</html>
