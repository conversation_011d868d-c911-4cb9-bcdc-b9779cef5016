import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard_input_control.dart';
import 'package:flutter/material.dart';
import 'package:theme_manager/theme_manager.dart';

class TradingKeyboardTestData extends StatefulWidget {
  const TradingKeyboardTestData({super.key});

  @override
  MyStatefulWidgetState createState() => MyStatefulWidgetState();
}

class MyStatefulWidgetState extends State<TradingKeyboardTestData>
    with TickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final TextEditingController _controller2 = TextEditingController();
  final TextEditingController _controller3 = TextEditingController();

  final TradingKeyboardInputControl _inputControl =
      TradingKeyboardInputControl();

  @override
  void initState() {
    super.initState();

    _inputControl.register();
  }

  @override
  void dispose() {
    _controller.dispose();
    _controller2.dispose();
    _controller3.dispose();

    _inputControl.unregister();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Container(
              width: 300,
              child: Column(
                children: [
                  TextField(
                    key: const Key('TextField_1'),
                    controller: _controller,
                    keyboardAppearance:
                        diContainer<ThemeManager>().isDarkMode
                            ? Brightness.dark
                            : Brightness.light,
                    onTap: () => _inputControl.show(),
                    onChanged: (value) => print("Cont 1 $value"),
                    decoration: InputDecoration(
                      suffix: IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          _inputControl.hide();
                        },
                      ),
                    ),
                  ),
                  TextField(
                    keyboardAppearance:
                        diContainer<ThemeManager>().isDarkMode
                            ? Brightness.dark
                            : Brightness.light,
                    key: const Key('TextField_2'),
                    controller: _controller2,
                    onTap: () => _inputControl.show(),
                    onChanged: (value) => print("Cont 2 $value"),
                    decoration: InputDecoration(
                      suffix: IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller2.clear();
                          _inputControl.hide();
                        },
                      ),
                    ),
                  ),
                  TextField(
                    keyboardAppearance:
                        diContainer<ThemeManager>().isDarkMode
                            ? Brightness.dark
                            : Brightness.light,
                    key: const Key('TextField_3'),
                    controller: _controller3,
                    onTap: () => _inputControl.show(),
                    onChanged: (value) => print("Cont 3 $value"),
                    decoration: InputDecoration(
                      suffix: IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller3.clear();
                          _inputControl.hide();
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          ValueListenableBuilder<bool>(
            valueListenable: _inputControl.visible,
            builder: (_, bool visible, __) {
              return AnimatedPositioned(
                duration: Duration(milliseconds: 250),
                curve: Curves.easeIn,
                left: 0,
                right: 0,
                bottom: visible ? 35 : -400,
                child: TradingKeyboard(
                  inputControl: _inputControl,
                  onDone: () {
                    _inputControl.hide();
                    FocusScope.of(context).unfocus();
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
