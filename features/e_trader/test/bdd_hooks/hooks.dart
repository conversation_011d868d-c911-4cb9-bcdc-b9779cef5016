import 'dart:async';

import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/symbols/services/symbol_subscription_batch_service.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';

import '../di/di_initializer.dart';
import '../mocks/equiti_trader_mocks.dart';

abstract class Hooks {
  const Hooks._();

  static FutureOr<void> beforeEach(String title, [List<String>? tags]) async {}

  static FutureOr<void> beforeAll() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    await setupDi();
    setupEquitiTraderMocks();
  }

  static FutureOr<void> afterEach(
    String title,
    bool success, [
    List<String>? tags,
  ]) async {
    // Clean up the SymbolSubscriptionBatchService to prevent pending timers
    try {
      final batchService = diContainer<SymbolSubscriptionBatchService>();
      batchService.dispose();
    } catch (e) {
      // Service might not be registered yet, ignore
    }
  }

  static FutureOr<void> afterAll() {
    diContainer.reset();
    WidgetsBinding.instance.resetEpoch();
  }
}
