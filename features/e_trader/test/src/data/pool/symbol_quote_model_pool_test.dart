import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/pool/symbol_quote_model_pool.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SymbolQuoteModelPool', () {
    late SymbolQuoteModelPool pool;

    setUp(() {
      pool = SymbolQuoteModelPool();
    });

    tearDown(() {
      pool.clear();
    });

    // Helper method to create sample JSON data
    Map<String, dynamic> createSampleJson({
      String symbol = 'EURUSD',
      double ask = 1.1234,
      double bid = 1.1230,
      String? date = '2024-01-01T10:00:00Z',
      int digits = 5,
      double spread = 0.0004,
      double dailyRateChange = 0.0012,
      String direction = 'up',
      double midPrice = 1.1232,
      double? previousDayPrice = 1.1220,
    }) {
      return {
        'symbol': symbol,
        'ask': ask,
        'bid': bid,
        'date': date,
        'digits': digits,
        'spread': spread,
        'dailyRateChange': dailyRateChange,
        'direction': direction,
        'midPrice': midPrice,
        'previousDayPrice': previousDayPrice,
      };
    }

    group('Object Creation and Pooling', () {
      test('should create new model for new symbol', () {
        // Arrange
        final json = createSampleJson();

        // Act
        final model = pool.getOrCreate('EURUSD', json);

        // Assert
        expect(model.symbol, equals('EURUSD'));
        expect(model.ask, equals(1.1234));
        expect(model.bid, equals(1.1230));
        expect(pool.hasSymbol('EURUSD'), isTrue);
        expect(pool.size, equals(1));
      });

      test(
        'should return same instance for existing symbol with no changes',
        () {
          // Arrange
          final json = createSampleJson();
          final firstModel = pool.getOrCreate('EURUSD', json);

          // Act
          final secondModel = pool.getOrCreate('EURUSD', json);

          // Assert
          expect(identical(firstModel, secondModel), isTrue);
          expect(pool.size, equals(1));
        },
      );

      test('should create separate instances for different symbols', () {
        // Arrange
        final eurUsdJson = createSampleJson(symbol: 'EURUSD');
        final gbpUsdJson = createSampleJson(
          symbol: 'GBPUSD',
          ask: 1.2500,
          bid: 1.2496,
        );

        // Act
        final eurUsdModel = pool.getOrCreate('EURUSD', eurUsdJson);
        final gbpUsdModel = pool.getOrCreate('GBPUSD', gbpUsdJson);

        // Assert
        expect(identical(eurUsdModel, gbpUsdModel), isFalse);
        expect(eurUsdModel.symbol, equals('EURUSD'));
        expect(gbpUsdModel.symbol, equals('GBPUSD'));
        expect(pool.size, equals(2));
      });
    });

    group('In-Place Updates', () {
      test('should update existing model in place when data changes', () {
        // Arrange
        final initialJson = createSampleJson(ask: 1.1234, bid: 1.1230);
        final originalModel = pool.getOrCreate('EURUSD', initialJson);
        final originalAsk = originalModel.ask;

        // Act
        final updatedJson = createSampleJson(ask: 1.1240, bid: 1.1236);
        final updatedModel = pool.getOrCreate('EURUSD', updatedJson);

        // Assert
        expect(identical(originalModel, updatedModel), isTrue);
        expect(updatedModel.ask, equals(1.1240));
        expect(updatedModel.bid, equals(1.1236));
        expect(originalAsk, equals(1.1234)); // Original value was different
        expect(pool.size, equals(1));
      });

      test('should update all changeable properties in place', () {
        // Arrange
        final initialJson = createSampleJson();
        final model = pool.getOrCreate('EURUSD', initialJson);

        // Act
        final updatedJson = createSampleJson(
          ask: 1.1250,
          bid: 1.1246,
          spread: 0.0005,
          dailyRateChange: 0.0020,
          direction: 'down',
          midPrice: 1.1248,
          previousDayPrice: 1.1225,
          digits: 4,
          date: '2024-01-02T10:00:00Z',
        );
        final updatedModel = pool.getOrCreate('EURUSD', updatedJson);

        // Assert
        expect(identical(model, updatedModel), isTrue);
        expect(updatedModel.ask, equals(1.1250));
        expect(updatedModel.bid, equals(1.1246));
        expect(updatedModel.spread, equals(0.0005));
        expect(updatedModel.dailyRateChange, equals(0.0020));
        expect(updatedModel.direction, equals('down'));
        expect(updatedModel.midPrice, equals(1.1248));
        expect(updatedModel.previousDayPrice, equals(1.1225));
        expect(updatedModel.digits, equals(4));
        expect(updatedModel.date, equals('2024-01-02T10:00:00Z'));
      });
    });

    group('Change Detection', () {
      test('should detect changes in ask price', () {
        // Arrange
        final initialJson = createSampleJson(ask: 1.1234);
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(ask: 1.1235);
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.ask, equals(1.1235));
      });

      test('should detect changes in bid price', () {
        // Arrange
        final initialJson = createSampleJson(bid: 1.1230);
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(bid: 1.1231);
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.bid, equals(1.1231));
      });

      test('should detect changes in spread', () {
        // Arrange
        final initialJson = createSampleJson(spread: 0.0004);
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(spread: 0.0005);
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.spread, equals(0.0005));
      });

      test('should detect changes in dailyRateChange', () {
        // Arrange
        final initialJson = createSampleJson(dailyRateChange: 0.0012);
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(dailyRateChange: 0.0015);
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.dailyRateChange, equals(0.0015));
      });

      test('should detect changes in direction', () {
        // Arrange
        final initialJson = createSampleJson(direction: 'up');
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(direction: 'down');
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.direction, equals('down'));
      });

      test('should detect changes in midPrice', () {
        // Arrange
        final initialJson = createSampleJson(midPrice: 1.1232);
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(midPrice: 1.1235);
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.midPrice, equals(1.1235));
      });

      test('should detect changes in previousDayPrice', () {
        // Arrange
        final initialJson = createSampleJson(previousDayPrice: 1.1220);
        pool.getOrCreate('EURUSD', initialJson);

        // Act & Assert
        final changedJson = createSampleJson(previousDayPrice: 1.1225);
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);
        expect(updatedModel.previousDayPrice, equals(1.1225));
      });

      test('should not update when non-important fields change', () {
        // Arrange
        final initialJson = createSampleJson(date: '2024-01-01T10:00:00Z');
        final originalModel = pool.getOrCreate('EURUSD', initialJson);
        final originalAsk = originalModel.ask;

        // Act - change only date (not in important fields list)
        final changedJson = createSampleJson(date: '2024-01-02T10:00:00Z');
        final updatedModel = pool.getOrCreate('EURUSD', changedJson);

        // Assert - should be same instance and date should NOT be updated
        // because date is not in the important fields list
        expect(identical(originalModel, updatedModel), isTrue);
        expect(updatedModel.ask, equals(originalAsk)); // Ask unchanged
        expect(
          updatedModel.date,
          equals('2024-01-01T10:00:00Z'),
        ); // Date unchanged
      });
    });

    group('Pool Management', () {
      test('hasSymbol should return correct status', () {
        // Arrange & Act
        expect(pool.hasSymbol('EURUSD'), isFalse);

        pool.getOrCreate('EURUSD', createSampleJson());
        expect(pool.hasSymbol('EURUSD'), isTrue);
        expect(pool.hasSymbol('GBPUSD'), isFalse);
      });

      test('getExisting should return correct model or null', () {
        // Arrange
        expect(pool.getExisting('EURUSD'), isNull);

        final model = pool.getOrCreate('EURUSD', createSampleJson());

        // Act & Assert
        final existing = pool.getExisting('EURUSD');
        expect(identical(model, existing), isTrue);
        expect(pool.getExisting('GBPUSD'), isNull);
      });

      test('removeSymbol should remove symbol from pool', () {
        // Arrange
        pool.getOrCreate('EURUSD', createSampleJson());
        pool.getOrCreate('GBPUSD', createSampleJson(symbol: 'GBPUSD'));
        expect(pool.size, equals(2));

        // Act
        pool.removeSymbol('EURUSD');

        // Assert
        expect(pool.hasSymbol('EURUSD'), isFalse);
        expect(pool.hasSymbol('GBPUSD'), isTrue);
        expect(pool.size, equals(1));
      });

      test('clear should remove all symbols from pool', () {
        // Arrange
        pool.getOrCreate('EURUSD', createSampleJson());
        pool.getOrCreate('GBPUSD', createSampleJson(symbol: 'GBPUSD'));
        expect(pool.size, equals(2));

        // Act
        pool.clear();

        // Assert
        expect(pool.size, equals(0));
        expect(pool.hasSymbol('EURUSD'), isFalse);
        expect(pool.hasSymbol('GBPUSD'), isFalse);
      });

      test('symbols should return all symbol keys', () {
        // Arrange
        pool.getOrCreate('EURUSD', createSampleJson());
        pool.getOrCreate('GBPUSD', createSampleJson(symbol: 'GBPUSD'));
        pool.getOrCreate('USDJPY', createSampleJson(symbol: 'USDJPY'));

        // Act
        final symbols = pool.symbols;

        // Assert
        expect(symbols.length, equals(3));
        expect(symbols, contains('EURUSD'));
        expect(symbols, contains('GBPUSD'));
        expect(symbols, contains('USDJPY'));
      });
    });

    group('Edge Cases and Null Handling', () {
      test('should handle null values in JSON gracefully', () {
        // Arrange - only nullable fields can be null
        final jsonWithNulls = createSampleJson();
        jsonWithNulls['previousDayPrice'] = null; // This is nullable
        jsonWithNulls['date'] = null; // This is nullable

        // Act & Assert - should not throw for nullable fields
        expect(
          () => pool.getOrCreate('EURUSD', jsonWithNulls),
          returnsNormally,
        );
        expect(pool.hasSymbol('EURUSD'), isTrue);

        final model = pool.getExisting('EURUSD')!;
        expect(model.previousDayPrice, isNull);
        expect(model.date, isNull);
      });

      test('should handle updates with null values for nullable fields', () {
        // Arrange
        final initialJson = createSampleJson(
          previousDayPrice: 1.1220,
          date: '2024-01-01T10:00:00Z',
        );
        final model = pool.getOrCreate('EURUSD', initialJson);
        final originalPreviousDayPrice = model.previousDayPrice;

        // Act - update with null values for nullable fields only
        final updateJson = createSampleJson(
          ask: 1.1235, // Change this to trigger update
        );
        updateJson['previousDayPrice'] = null; // Set to null after creation
        updateJson['date'] = null; // Set to null after creation
        final updatedModel = pool.getOrCreate('EURUSD', updateJson);

        // Assert - null values should NOT overwrite existing values
        // The pool only updates when non-null values are provided
        expect(identical(model, updatedModel), isTrue);
        expect(updatedModel.ask, equals(1.1235)); // This was updated
        expect(
          updatedModel.previousDayPrice,
          equals(originalPreviousDayPrice),
        ); // Should remain unchanged when null provided
        expect(
          updatedModel.date,
          equals('2024-01-01T10:00:00Z'),
        ); // Should remain unchanged when null provided
      });

      test('should handle missing fields in JSON', () {
        // Arrange
        final incompleteJson = {
          'symbol': 'EURUSD',
          'ask': 1.1234,
          'bid': 1.1230,
          'digits': 5,
          'spread': 0.0004,
          'dailyRateChange': 0.0012,
          'direction': 'up',
          'midPrice': 1.1232,
          // Missing previousDayPrice and date
        };

        // Act & Assert - should not throw
        expect(
          () => pool.getOrCreate('EURUSD', incompleteJson),
          returnsNormally,
        );
        expect(pool.hasSymbol('EURUSD'), isTrue);
      });

      test('should handle empty symbol string', () {
        // Arrange
        final json = createSampleJson();

        // Act & Assert
        final model = pool.getOrCreate('', json);
        expect(pool.hasSymbol(''), isTrue);
        expect(pool.getExisting(''), isNotNull);
        expect(identical(model, pool.getExisting('')), isTrue);
      });

      test('should handle removeSymbol for non-existent symbol', () {
        // Act & Assert - should not throw
        expect(() => pool.removeSymbol('NONEXISTENT'), returnsNormally);
        expect(pool.size, equals(0));
      });

      test('should handle multiple updates with same data', () {
        // Arrange
        final json = createSampleJson();
        final firstModel = pool.getOrCreate('EURUSD', json);

        // Act - multiple calls with same data
        final secondModel = pool.getOrCreate('EURUSD', json);
        final thirdModel = pool.getOrCreate('EURUSD', json);

        // Assert - should always return same instance
        expect(identical(firstModel, secondModel), isTrue);
        expect(identical(secondModel, thirdModel), isTrue);
        expect(pool.size, equals(1));
      });
    });

    group('Memory Efficiency', () {
      test('should reuse objects efficiently across multiple updates', () {
        // Arrange
        final initialJson = createSampleJson(ask: 1.1234);
        final originalModel = pool.getOrCreate('EURUSD', initialJson);

        // Act - perform multiple updates
        final models = <SymbolQuoteModel>[];
        for (int i = 0; i < 10; i++) {
          final updatedJson = createSampleJson(ask: 1.1234 + (i * 0.0001));
          models.add(pool.getOrCreate('EURUSD', updatedJson));
        }

        // Assert - all should be the same instance
        for (final model in models) {
          expect(identical(originalModel, model), isTrue);
        }
        expect(pool.size, equals(1));
      });

      test('should maintain separate instances for different symbols', () {
        // Arrange
        final symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD'];
        final models = <String, SymbolQuoteModel>{};

        // Act
        for (final symbol in symbols) {
          models[symbol] = pool.getOrCreate(
            symbol,
            createSampleJson(symbol: symbol),
          );
        }

        // Assert - all should be different instances
        final modelList = models.values.toList();
        for (int i = 0; i < modelList.length; i++) {
          for (int j = i + 1; j < modelList.length; j++) {
            expect(identical(modelList[i], modelList[j]), isFalse);
          }
        }
        expect(pool.size, equals(symbols.length));
      });

      test('should handle large number of symbols efficiently', () {
        // Arrange & Act
        final symbolCount = 100;
        for (int i = 0; i < symbolCount; i++) {
          final symbol = 'SYMBOL$i';
          pool.getOrCreate(symbol, createSampleJson(symbol: symbol));
        }

        // Assert
        expect(pool.size, equals(symbolCount));
        expect(pool.symbols.length, equals(symbolCount));

        // Verify all symbols are accessible
        for (int i = 0; i < symbolCount; i++) {
          final symbol = 'SYMBOL$i';
          expect(pool.hasSymbol(symbol), isTrue);
          expect(pool.getExisting(symbol), isNotNull);
        }
      });
    });
  });
}
