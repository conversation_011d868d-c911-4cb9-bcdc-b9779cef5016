import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Usage: The {WithdrawScreen(walletId: '123', isFromAccountScreen: false)} app runs with configuration {scenarios:[withdrawFailureScenario]}
Future<void> theAppRunsWithConfiguration(
  WidgetTester tester,
  Widget app, {
  VoidCallback? postDi,
  List<VoidCallback> scenarios = const <VoidCallback>[],
}) async {
  tester.view.devicePixelRatio = 1.0;
  tester.view.physicalSize = Size(600, 800);
  final locale =
      Platform.environment['APP_LOCALE'] == 'ar'
          ? const Locale('ar')
          : const Locale('en');
  final theme =
      Platform.environment['APP_THEME'] == "dark"
          ? DuploThemeData.dark()
          : DuploThemeData.light();

  return await AppTestConfigurator(
    tester: tester,
    app: DuploTheme(
      child: DuploTextStyles(
        locale: locale,
        child: MaterialApp(
          home: app,
          localizationsDelegates: EquitiLocalization.localizationsDelegates,
          supportedLocales: EquitiLocalization.supportedLocales,
          locale: locale,
        ),
      ),
      data: theme,
    ),
    scenarios: scenarios,
    onInit: () => EquitiLocalizationManager.initMock(),
  ).run();
}
