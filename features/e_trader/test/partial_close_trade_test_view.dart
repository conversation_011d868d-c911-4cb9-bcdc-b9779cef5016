import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/partial_close/partial_close.dart';
import 'package:e_trader/src/presentation/partial_close/partial_close_toast.dart';
import 'package:flutter/material.dart';

class PartialCloseTradeTestView extends StatelessWidget {
  const PartialCloseTradeTestView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: InkWell(
          onTap: () {
            showPartialCloseSheet(
              context: context,
              positionId: 0,
              onSuccess: ({
                required double lots,
                required String productIconUrl,
                required String productName,
                required double profit,
                required TradeType? tradeType,
                String? titleMessage,
              }) {
                showCloseTradeToast(
                  toast: DuploToast(),
                  context: context,
                  lots: lots,
                  productIconUrl: productIconUrl,
                  productName: productName,
                  profit: profit,
                  tradeType: tradeType,
                );
              },
            );
          },
          child: Semantics(
            identifier: 'open_partial_close_trade_sheet',
            child: Text('Open partial close trade sheet'),
          ),
        ),
      ),
    );
  }
}
