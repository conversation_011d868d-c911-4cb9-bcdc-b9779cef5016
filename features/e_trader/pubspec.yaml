name: e_trader
description: Equiti Trader
version: 1.0.0+1
publish_to: none

environment:
  sdk: 3.8.1

dependencies:
  flutter:
    sdk: flutter
  intl: 0.20.2
  api_client:
    path: ../../utilities/api_client
  socket_client:
    path: ../../utilities/socket_client
  validator:
    path: ../../core/validator
  flutter_bloc: 9.1.1
  freezed_annotation: 3.0.0
  get_it: 8.0.3
  injectable: 2.5.0
  json_annotation: 4.9.0
  flutter_svg: 2.0.10+1
  clock: 1.1.2
  equiti_router:
    path: ../../utilities/equiti_router
  monitoring:
    path: ../../utilities/monitoring
  login:
    path: ../../core/login
  prelude:
    path: ../../utilities/prelude
  duplo:
    path: ../../core/duplo
  preferences:
    path: ../../utilities/preferences
  equiti_localization:
    path: ../../core/equiti_localization
  equiti_auth:
    path: ../../core/equiti_auth
  user_account:
    path: ../../core/user_account
  domain:
    path: ../../core/domain

  bloc_concurrency: 0.3.0
  flutter_timezone: 3.0.1
  cached_network_image: 3.4.1
  rxdart: 0.28.0
  fl_chart: 1.0.0
  flutter_inappwebview: 6.1.5
  locale_manager: 0.0.1
  theme_manager: 0.0.1
  syncfusion_flutter_pdfviewer: 29.2.7
  device_calendar: 4.3.3
  dio: 5.8.0+1
  url_launcher: 6.3.1
  flutter_widget_from_html: 0.16.0
  smooth_sheets: ^0.14.0
  lazy_load_indexed_stack: ^1.2.1
  visibility_detector: ^0.4.0+2
  decimal: ^3.2.4

dev_dependencies:
  build_runner: 2.5.4
  dependency_validator: 5.0.2
  equiti_lint:
    path: ../../utilities/equiti_lint
  flutter_gen_runner: 5.10.0
  freezed: 3.0.6
  injectable_generator: 2.7.0
  json_serializable: 6.9.5
  bdd_widget_test: 1.8.1
  mocktail: 1.0.4
  bdd_steps:
    path: ../../utilities/bdd_steps
  equiti_test:
    path: ../../utilities/equiti_test
  dart_code_metrics_presets: 2.22.0

  flutter_test:
    sdk: flutter

  toastification: 2.3.0
  build_verify: 3.1.1

dependency_overrides:
  analyzer: ^6.3.0
  dart_style: ^3.0.1

flutter:
  uses-material-design: true
  generate: true
  fonts:
    - family: Helvetica
      fonts:
        - asset: assets/fonts/helvetica/Helvetica.ttf
          weight: 600
    - family: Gilroy
      fonts:
        - asset: assets/fonts/gilroy/Gilroy-Medium.ttf
          weight: 500
        - asset: assets/fonts/gilroy/Gilroy-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/gilroy/Gilroy-Bold.ttf
          weight: 700
  assets:
    - assets/images/
    - assets/chart/
    - assets/chart/src/
    - assets/chart/src/layout_adabter.js
    - assets/chart/src/datafeed.js
    - assets/chart/src/main.js
    - assets/chart/charting_library/
    - assets/chart/charting_library/bundles/
    - resources/mocks/reset_balance/
    - resources/mocks/legal_documents/
    - resources/mocks/withdraw/
    - resources/mocks/get_leverage/
    - resources/mocks/change_leverage/
    - resources/mocks/transfer_funds/
    - resources/mocks/transfer_funds/conversion_rate/
    - resources/mocks/transfer_funds/get_transfer_account/
    - resources/mocks/transfer_funds/transfer_funds/
    - resources/mocks/categories/
    - resources/mocks/symbols/
    - resources/mocks/holidays/
    - resources/mocks/sessions/
    - resources/mocks/switch_account/get_live_accounts/
    - resources/mocks/switch_account/get_demo_accounts/
    - resources/mocks/get_chart/
    - resources/mocks/product_detail_info/
    - resources/mocks/close_position/
    - resources/mocks/get_countries/
    - resources/mocks/statements/
    - resources/mocks/funding_tab/
    - resources/mocks/news/
    - resources/mocks/events/
    - resources/mocks/wallet_details/
    - resources/mocks/watchlist/
    - resources/mocks/trading_tab/
    - resources/mocks/change_account_password/


flutter_gen:
  output: lib/src/assets/
  assets:
    exclude:
      - resources/mocks/**/*
    outputs:
      package_parameter_enabled: true
  integrations:
    flutter_svg: true
