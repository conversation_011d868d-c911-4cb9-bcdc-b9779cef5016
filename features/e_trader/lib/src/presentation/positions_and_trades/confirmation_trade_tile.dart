import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/widgets/buy_sell_lots.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;
import 'package:prelude/prelude.dart';

class ConfirmationTradeTile extends StatelessWidget {
  const ConfirmationTradeTile({
    super.key,
    required this.lots,
    required this.tradeType,
    this.orderPrice,
    required this.digits,
  });
  final double lots;
  final TradeType tradeType;
  final double? orderPrice;
  final int digits;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final locale = Localizations.localeOf(context).toString();

    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: theme.shadow.shadowMainCentreMd.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(-1, 4),
          ),
          BoxShadow(
            color: theme.shadow.shadowMainCentreMd.withValues(alpha: 0.06),
            blurRadius: 4,
            offset: const Offset(-2, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.all(
          Radius.circular(DuploRadius.radius_xl_12),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.background.bgPrimary,
            border: Border(
              bottom: BorderSide(color: theme.border.borderSecondary, width: 1),
              top: BorderSide(color: theme.border.borderSecondary, width: 1),
              left:
                  intl.Bidi.isRtlLanguage(
                        Localizations.localeOf(context).languageCode,
                      )
                      ? BorderSide.none
                      : BorderSide(
                        color:
                            tradeType == TradeType.buy
                                ? theme.foreground.fgSuccessPrimary
                                : theme.foreground.fgErrorPrimary,
                        width: 4,
                      ),
              right:
                  intl.Bidi.isRtlLanguage(
                        Localizations.localeOf(context).languageCode,
                      )
                      ? BorderSide(
                        color:
                            tradeType == TradeType.buy
                                ? theme.foreground.fgSuccessPrimary
                                : theme.foreground.fgErrorPrimary,
                        width: 4,
                      )
                      : BorderSide.none,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              BuySellLots(lots: lots, tradeType: tradeType),
              DuploText(
                text: EquitiFormatter.decimalPatternDigits(
                  value: orderPrice!,
                  digits: digits,
                  locale: locale,
                ),
                style: DuploTextStyles.of(context).textSm,
                color: theme.text.textSecondary,
                fontWeight: DuploFontWeight.bold,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
