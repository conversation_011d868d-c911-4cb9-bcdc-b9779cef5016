import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/widgets/buy_sell_lots.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class TradeTile extends StatelessWidget {
  const TradeTile({
    super.key,
    required this.lots,
    required this.tradeType,
    required this.profit,
    required this.priceChange,
    required this.currentPrice,
    required this.tpValue,
    required this.slValue,
    required this.productName,
    required this.productIcon,
    required this.digits,
    this.currency,
  });

  final double tpValue;
  final double slValue;
  final String? productName;
  final String? productIcon;
  final double lots;
  final TradeType tradeType;
  final double profit;
  final double priceChange;
  final double currentPrice;
  final int digits;
  final String? currency;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final priceChangeColor =
        profit >= 0
            ? theme.text.textSuccessPrimary
            : theme.text.textErrorPrimary;
    final arrowIcon =
        profit >= 0
            ? trader.Assets.images.trendingUpIc.svg()
            : trader.Assets.images.trendingDownIc.svg();
    final backgroundColor = theme.background.bgPrimary;
    final String profitWhole =
        profit.toStringAsFixed(2).split('.').firstOrNull ?? "";
    final String profitFraction =
        profit.toStringAsFixed(2).split('.').elementAtOrNull(1) ?? "";

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.border.borderSecondary, width: 1),
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            color:
                tradeType == TradeType.buy
                    ? theme.text.textSuccessPrimary
                    : theme.text.textErrorPrimary,
            height: 80,
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const SizedBox(width: 4),
                    DuploCachedNetworkImage(
                      imageUrl: productIcon ?? "",
                      imageHeight: 40,
                      imageWidth: 40,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DuploText(
                          text: productName ?? "",
                          style: context.duploTextStyles.textSm,
                          fontWeight: DuploFontWeight.medium,
                          color: theme.text.textPrimary,
                        ),
                        SizedBox(height: 2),
                        Row(
                          children: [
                            BuySellLots(lots: lots, tradeType: tradeType),
                            SizedBox(width: 10),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      DuploText.rich(
                        spans: [
                          DuploTextSpan(
                            text: "${profit > 0 ? "+" : ""}${profitWhole}",
                            style: context.duploTextStyles.textSm,
                            color: priceChangeColor,
                            fontWeight: DuploFontWeight.bold,
                          ),
                          DuploTextSpan(
                            text: "." + profitFraction,
                            style: context.duploTextStyles.textXs,
                            color: priceChangeColor,
                            fontWeight: DuploFontWeight.bold,
                          ),
                          DuploTextSpan(
                            text: currency != null ? " ${currency!}" : " USD",
                            style: context.duploTextStyles.textXs,
                            color: priceChangeColor,
                            fontWeight: DuploFontWeight.regular,
                          ),
                        ],
                      ),
                      SizedBox(height: 2),

                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          DuploText(
                            text: "${priceChange.toStringAsFixed(2)}%",
                            style: context.duploTextStyles.textXs,
                            color: priceChangeColor,
                            fontWeight: DuploFontWeight.semiBold,
                          ),
                          SizedBox(width: 2),
                          arrowIcon,
                          SizedBox(width: 8),
                          DuploText(
                            text: currentPrice.toStringAsFixed(digits),
                            style: context.duploTextStyles.textXs,
                            color: theme.text.textPrimary,
                            fontWeight: DuploFontWeight.semiBold,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
