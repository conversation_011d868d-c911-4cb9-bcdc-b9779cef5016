import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/entry_order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;
import 'package:prelude/prelude.dart';

class OrderListTile extends StatelessWidget {
  const OrderListTile({
    super.key,
    required this.productIconURL,
    required this.productName,
    required this.lots,
    required this.tradeType,
    required this.currentPrice,
    required this.orderPrice,
    required this.digits,
    required this.tpValue,
    required this.slValue,
    required this.entryOrderType,
    required this.priceChange,
    this.onTap,
  });
  final String productIconURL;
  final String productName;
  final double lots;
  final TradeType tradeType;
  final double currentPrice;
  final double orderPrice;
  final int digits;
  final double tpValue;
  final double slValue;
  final EntryOrderType entryOrderType;
  final double priceChange;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final locale = Localizations.localeOf(context);

    return DuploTap(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsetsDirectional.only(
          start: 8,
          end: 16,
          top: 16,
          bottom: 16,
        ),
        decoration: BoxDecoration(
          color: context.duploTheme.background.bgPrimary,
          border: Border(
            bottom: BorderSide(color: theme.border.borderSecondary, width: 1),
            top: BorderSide(color: theme.border.borderSecondary, width: 1),
            left:
                intl.Bidi.isRtlLanguage(locale.languageCode)
                    ? BorderSide.none
                    : BorderSide(
                      color:
                          tradeType == TradeType.buy
                              ? theme.foreground.fgSuccessPrimary
                              : theme.foreground.fgErrorPrimary,
                      width: 4,
                    ),
            right:
                intl.Bidi.isRtlLanguage(locale.languageCode)
                    ? BorderSide(
                      color:
                          tradeType == TradeType.buy
                              ? theme.foreground.fgSuccessPrimary
                              : theme.foreground.fgErrorPrimary,
                      width: 4,
                    )
                    : BorderSide.none,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                DuploCachedNetworkImage(
                  imageUrl: productIconURL,
                  imageHeight: 40,
                  imageWidth: 40,
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DuploText(
                      text: productName,
                      style: context.duploTextStyles.textSm,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.text.textPrimary,
                    ),
                    SizedBox(height: 2),
                    DuploTagContainer.sm(
                      text: _getEntryOrderTypeText(
                        EquitiLocalization.of(context),
                        locale.toString(),
                      ),
                      type:
                          entryOrderType == EntryOrderType.buyLimit ||
                                  entryOrderType == EntryOrderType.buyStop
                              ? DuploTagType.success
                              : DuploTagType.error,
                    ),
                  ],
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                DuploText(
                  text: EquitiFormatter.formatDynamicDigits(
                    value: orderPrice,
                    digits: digits,
                    locale: locale.toString(),
                  ),
                  style: context.duploTextStyles.textSm,
                  color: theme.text.textSecondary,
                  fontWeight: DuploFontWeight.bold,
                ),
                SizedBox(height: 6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,

                  children: [
                    SizedBox(width: 4),
                    DuploText(
                      text: EquitiFormatter.formatDynamicDigits(
                        value: currentPrice,
                        digits: digits,
                        locale: locale.toString(),
                      ),
                      style: context.duploTextStyles.textXs,
                      color: theme.text.textSecondary,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      fontWeight: DuploFontWeight.medium,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getEntryOrderTypeText(
    EquitiLocalization localization,
    String locale,
  ) {
    switch (entryOrderType) {
      case EntryOrderType.buyLimit:
        return "BUY LIMIT ${EquitiFormatter.formatNumber(value: lots, locale: locale)} ${localization.trader_lots}";
      case EntryOrderType.buyStop:
        return "BUY STOP ${EquitiFormatter.formatNumber(value: lots, locale: locale)} ${localization.trader_lots}";
      case EntryOrderType.sellLimit:
        return "SELL LIMIT ${EquitiFormatter.formatNumber(value: lots, locale: locale)} ${localization.trader_lots}";
      case EntryOrderType.sellStop:
        return "SELL STOP ${EquitiFormatter.formatNumber(value: lots, locale: locale)} ${localization.trader_lots}";
    }
  }
}
