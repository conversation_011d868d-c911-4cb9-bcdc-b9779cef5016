import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class BuySellLots extends StatelessWidget {
  const BuySellLots({super.key, required this.lots, required this.tradeType});
  final double lots;
  final TradeType tradeType;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      child: DuploText(
        text:
            "${tradeType == TradeType.buy ? localization.trader_buy.toUpperCase() : localization.trader_sell.toUpperCase()} ${EquitiFormatter.formatNumber(value: lots, locale: Localizations.localeOf(context).toString())} ${EquitiLocalization.of(context).trader_lots}",
        style: context.duploTextStyles.textXs,
        color:
            tradeType == TradeType.buy
                ? theme.utility.utilitySuccess700
                : theme.utility.utilityError700,
        fontWeight: DuploFontWeight.medium,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          width: 2,
          color:
              tradeType == TradeType.buy
                  ? theme.utility.utilitySuccess100
                  : theme.utility.utilityError100,
        ),
        color:
            tradeType == TradeType.buy
                ? theme.utility.utilitySuccess50
                : theme.utility.utilityError50,
      ),
    );
  }
}
