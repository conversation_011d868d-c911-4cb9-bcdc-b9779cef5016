import 'package:duplo/duplo.dart';
import 'package:flutter/widgets.dart';

class ProfitWidget extends StatelessWidget {
  const ProfitWidget({super.key, required this.profit, this.currency});
  final double profit;
  final String? currency;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final profitColor =
        profit >= 0
            ? theme.text.textSuccessPrimary
            : theme.text.textErrorPrimary;
    return DuploText.rich(
      spans: [
        DuploTextSpan(
          text: " ${profit > 0 ? "+" : ""} ${profit.toStringAsFixed(2)}",
          style: context.duploTextStyles.textSm,
          color: profitColor,
          fontWeight: DuploFontWeight.bold,
        ),
        DuploTextSpan(
          text: currency != null ? " $currency" : " USD",
          style: context.duploTextStyles.textXs,
          color: profitColor,
          fontWeight: DuploFontWeight.regular,
        ),
      ],
    );
  }
}
