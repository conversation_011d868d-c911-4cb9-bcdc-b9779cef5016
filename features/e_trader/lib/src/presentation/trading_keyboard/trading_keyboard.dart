import 'package:e_trader/src/presentation/duplo/primary_button.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard_input_control.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

class TradingKeyboard extends StatefulWidget {
  final TradingKeyboardInputControl _inputControl;
  final VoidCallback? onDone;

  const TradingKeyboard({
    super.key,
    required TradingKeyboardInputControl inputControl,
    this.onDone,
  }) : _inputControl = inputControl;

  @override
  _TradingKeyboardState createState() => _TradingKeyboardState();
}

class _TradingKeyboardState extends State<TradingKeyboard> {
  @override
  void initState() {
    super.initState();
    widget._inputControl.register();
  }

  @override
  void dispose() {
    widget._inputControl.unregister();
    super.dispose();
  }

  void _handleKeyPress(String key) {
    widget._inputControl.processUserInput(key);
  }

  final List<List<String>> keys = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['.', '0', 'delete'],
  ];

  @override
  Widget build(BuildContext context) {
    final loc = EquitiLocalization.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children:
                keys.map((row) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children:
                        row.map((key) {
                          return Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: SizedBox(
                                height: 60,
                                child: ElevatedButton(
                                  key: Key('trading_keyboard_key_$key'),
                                  onPressed: () {
                                    _handleKeyPress(key);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    foregroundColor: Colors.black,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child:
                                      key == 'delete'
                                          ? trader
                                              .Assets
                                              .images
                                              .deleteKeyboardKey
                                              .svg()
                                          : Text(
                                            key,
                                            style: TextStyle(
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                  );
                }).toList(),
          ),
          SizedBox(height: 70),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  height: 50,
                  child: PrimaryButton(
                    key: Key('trading_keyboard_done_button'),
                    loading: false,
                    title: loc.trader_done,
                    onTap: () {
                      if (widget.onDone != null) {
                        widget.onDone!();
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
