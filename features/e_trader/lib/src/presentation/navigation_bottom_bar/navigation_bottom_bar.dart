import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/account/account_screen.dart';
import 'package:e_trader/src/presentation/discover/discover_screen.dart';
import 'package:e_trader/src/presentation/duplo/company_logo_widget.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/account_balance/bloc/account_balance_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/bloc/navigation_bottom_bar_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/performance_screen.dart';
import 'package:e_trader/src/presentation/portfolio/positions/portfolio_position_screen.dart';
import 'package:e_trader/src/presentation/symbols/symbols_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

part 'widgets/account_summary_content_widget.dart';

class NavigationBottomBar extends StatelessWidget {
  const NavigationBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => diContainer<NavigationBottomBarBloc>()),
        BlocProvider(create: (_) => diContainer<AccountBalanceBloc>()),
      ],
      child: _NavigationBarContent(),
    );
  }
}

class _NavigationBarContent extends StatefulWidget {
  const _NavigationBarContent();

  @override
  State<_NavigationBarContent> createState() => _NavigationBarContentState();
}

class _NavigationBarContentState extends State<_NavigationBarContent>
    with PerformanceObserverMixin {
  int tabIndex = 0;
  int portfolioTab = 0;
  int accountSettingsTab = 0;

  late final SheetController _draggableController;

  @override
  void initState() {
    super.initState();
    _draggableController = SheetController();
  }

  @override
  void dispose() {
    _draggableController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return BlocBuilder<NavigationBottomBarBloc, NavigationBottomBarState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return Scaffold(
          backgroundColor: theme.background.bgPrimary,
          bottomNavigationBar: DuploBottomNavbar(
            onNavBarCallBack: (index) {
              setState(() {
                tabIndex = index;
              });
            },
            selectedIndex: tabIndex,
            navItems: [
              DubloBottomNavBarItems(
                title: EquitiLocalization.of(context).trader_discover,
                selectedIcon: trader.Assets.images.discoverIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgWhite,
                    BlendMode.srcIn,
                  ),
                ),
                unselectedIcon: trader.Assets.images.discoverIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgDisabled,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              DubloBottomNavBarItems(
                selectedIcon: trader.Assets.images.marketsIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgWhite,
                    BlendMode.srcIn,
                  ),
                ),
                unselectedIcon: trader.Assets.images.marketsIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgDisabled,
                    BlendMode.srcIn,
                  ),
                ),
                title: EquitiLocalization.of(context).trader_markets,
              ),
              DubloBottomNavBarItems(
                selectedIcon: trader.Assets.images.portfolioIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgWhite,
                    BlendMode.srcIn,
                  ),
                ),
                unselectedIcon: trader.Assets.images.portfolioIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgDisabled,
                    BlendMode.srcIn,
                  ),
                ),
                title: EquitiLocalization.of(context).trader_portfolio,
              ),
              DubloBottomNavBarItems(
                selectedIcon: trader.Assets.images.performanceIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgWhite,
                    BlendMode.srcIn,
                  ),
                ),
                unselectedIcon: trader.Assets.images.performanceIcon.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgDisabled,
                    BlendMode.srcIn,
                  ),
                ),
                title: EquitiLocalization.of(context).trader_performance,
              ),
              DubloBottomNavBarItems(
                selectedIcon: trader.Assets.images.account.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgWhite,
                    BlendMode.srcIn,
                  ),
                ),
                unselectedIcon: trader.Assets.images.account.svg(
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgDisabled,
                    BlendMode.srcIn,
                  ),
                ),
                title: EquitiLocalization.of(context).trader_account,
              ),
            ],
          ),
          body: SafeArea(
            top: true,
            bottom: false,
            child: LayoutBuilder(
              builder: (layoutBuilderContext, constraints) {
                final _accountHeaderHeight =
                    kToolbarHeight +
                    MediaQuery.viewPaddingOf(layoutBuilderContext).top;
                final marginLevelProgressBar = DuploMarginProgressBar(
                  height: 4,
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  marginLevel: state.tradingAccountModel.marginLevel ?? 0,
                );
                final marginProgressBarHeight =
                    marginLevelProgressBar.getHeight();
                final _accountSummaryContentHeight =
                    _accountHeaderHeight +
                    marginProgressBarHeight +
                    8 +
                    measureText(
                      context: layoutBuilderContext,
                      text: '',
                      textStyle: context.duploTextStyles.textXs.toTextStyle(),
                    ).height +
                    measureText(
                      context: layoutBuilderContext,
                      text: '',
                      textStyle:
                          context.duploTextStyles.displaySm.toTextStyle(),
                    ).height +
                    measureText(
                      context: layoutBuilderContext,
                      text: '',
                      textStyle: context.duploTextStyles.textSm.toTextStyle(),
                    ).height;
                final _actionsContentHeight =
                    72.0 +
                    measureText(
                      context: layoutBuilderContext,
                      text: '',
                      textStyle: context.duploTextStyles.textXs.toTextStyle(),
                    ).height;
                final _maxChildSize = ((constraints.maxHeight -
                            _accountHeaderHeight -
                            8 -
                            marginProgressBarHeight) /
                        (constraints.maxHeight - _accountHeaderHeight))
                    .clamp(0.0, 1.0);
                double _totalContentAboveSheet =
                    state.tradingAccountModel.isDemo
                        ? _accountSummaryContentHeight
                        : _accountSummaryContentHeight +
                            _actionsContentHeight +
                            16;
                double _initialChildSize = ((constraints.maxHeight -
                            _totalContentAboveSheet) /
                        constraints.maxHeight)
                    .clamp(0.4, 0.9);

                return ColoredBox(
                  color: theme.background.bgPrimary,
                  child: Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: _AccountHeaderWidget(
                          sheetController: _draggableController,
                        ),
                      ),
                      Positioned(
                        top: 8 + _accountHeaderHeight,
                        left: 0,
                        right: 0,
                        child: marginLevelProgressBar,
                      ),
                      Positioned(
                        top: 8 + _accountHeaderHeight + marginProgressBarHeight,
                        right: 0,
                        left: 0,
                        child: SizedBox(
                          height: _accountSummaryContentHeight,
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                              16,
                              16,
                              16,
                              40,
                            ),
                            child: _AccountSummaryContentWidget(),
                          ),
                        ),
                      ),
                      if (!state.tradingAccountModel.isDemo)
                        Positioned(
                          top: _accountSummaryContentHeight + 40,
                          left: 0,
                          right: 0,
                          child: SizedBox(
                            height: _actionsContentHeight,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                              ),
                              child: DuploFundingButtons(
                                onDepositPressed: () {
                                  builderContext
                                      .read<NavigationBottomBarBloc>()
                                      .add(
                                        NavigationBottomBarEvent.navigateToDepositPaymentOptions(),
                                      );
                                },
                                onTransferPressed: () {
                                  builderContext
                                      .read<NavigationBottomBarBloc>()
                                      .add(
                                        NavigationBottomBarEvent.navigateToTransferFunds(),
                                      );
                                },
                                onWithdrawPressed: () {
                                  builderContext
                                      .read<NavigationBottomBarBloc>()
                                      .add(
                                        NavigationBottomBarEvent.navigateToWithdrawPaymentOptions(),
                                      );
                                },
                              ),
                            ),
                          ),
                        ),
                      Positioned.fill(
                        top: _accountHeaderHeight,
                        child: SheetViewport(
                          child: Sheet(
                            controller: _draggableController,
                            scrollConfiguration:
                                const SheetScrollConfiguration(),
                            decoration: MaterialSheetDecoration(
                              size: SheetSize.stretch,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                              color: theme.background.bgSecondary,
                            ),
                            initialOffset: SheetOffset(_initialChildSize),
                            physics: BouncingSheetPhysics(),
                            snapGrid: SheetSnapGrid(
                              snaps: [
                                SheetOffset(_initialChildSize),
                                SheetOffset(_maxChildSize),
                              ],
                            ),
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(24),
                                ),
                                color: theme.background.bgSecondary,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.08),
                                    blurRadius: 4,
                                    offset: Offset(0, -1),
                                  ),
                                ],
                              ),
                              child: SafeArea(
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    const SizedBox(height: 8),
                                    DraggableHandle(),
                                    _TabContentWidget(
                                      tabIndex: tabIndex,
                                      portfolioTab: portfolioTab,
                                      accountSettingsTab: accountSettingsTab,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _handleTradeConfirmationNavigation(route);
  }

  void _handleTradeConfirmationNavigation(Route<Object?> route) {
    if (route.settings.name == 'confirmation_sheet') {
      switch (diContainer<EquitiNavigatorBase>()
          .globalData[EquitiTraderRouteSchema.navBarRoute.label]) {
        case TradeConfirmationResult.viewTrades:
          setState(() {
            tabIndex = 2;
            portfolioTab = 2;
          });

        case TradeConfirmationResult.viewOrders:
          setState(() {
            tabIndex = 2;
            portfolioTab = 3;
          });

        default:
          setState(() {
            tabIndex = 1;
          });
      }
      diContainer<EquitiNavigatorBase>().globalData[EquitiTraderRouteSchema
              .navBarRoute
              .label] =
          <String, dynamic>{};
      print("=== DEBUG: ${diContainer<EquitiNavigatorBase>().globalData}");
    }
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    debugPrint("Route pushed: ${route.settings.name ?? 'null'}");
  }
}

class _TabContentWidget extends StatelessWidget {
  const _TabContentWidget({
    required this.tabIndex,
    required this.portfolioTab,
    required this.accountSettingsTab,
  });

  final int tabIndex;
  final int portfolioTab;
  final int accountSettingsTab;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: LazyLoadIndexedStack(
        index: tabIndex,
        children: [
          DiscoverScreen(),
          SymbolsScreen(),
          PortfolioPositionScreen(tabIndex: portfolioTab),
          PerformanceScreen(),
          AccountScreen(tabIndex: accountSettingsTab),
        ],
      ),
    );
  }
}

class _AccountHeaderWidget extends StatelessWidget
    implements PreferredSizeWidget {
  const _AccountHeaderWidget({required this.sheetController});

  final SheetController sheetController;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    // Create OffsetDrivenAnimation to monitor sheet position in real-time
    final sheetAnimation = SheetOffsetDrivenAnimation(
      controller: sheetController,
      initialValue: 0.0, // Start with account name (not at max)
      // We'll use the default startOffset (min) and endOffset (max)
    );

    return BlocSelector<
      NavigationBottomBarBloc,
      NavigationBottomBarState,
      TradingAccountModel
    >(
      selector: (state) {
        return state.tradingAccountModel;
      },
      builder: (blocSelectorContext, accountModel) {
        return AppBar(
          backgroundColor: theme.background.bgPrimary,
          centerTitle: true,
          automaticallyImplyLeading: false,
          leading: IconButton(
            onPressed:
                () => diContainer<EquitiTraderNavigation>().navigateToHub(),
            icon: Assets.images.dotGrid.svg(
              colorFilter: ColorFilter.mode(
                theme.foreground.fgPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
          title: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: theme.background.bgSecondary,
              borderRadius: const BorderRadius.all(
                Radius.circular(DuploRadius.radius_sm_6),
              ),
              border: Border.all(color: theme.border.borderSecondary),
            ),
            child: DuploTap(
              onTap:
                  () =>
                      diContainer<EquitiTraderNavigation>()
                          .navigateToSwitchAccounts(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.background.bgQuaternary,
                      borderRadius: const BorderRadius.all(
                        Radius.circular(DuploRadius.radius_xs_4),
                      ),
                    ),
                    child: CompanyLogoWidget(
                      platformType: accountModel.platformType,
                      useMono: true,
                    ),
                  ),
                  const SizedBox(width: 2),
                  Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        DuploText.rich(
                          spans: [
                            DuploTextSpan(
                              text:
                                  accountModel.platformAccountType.displayName,
                              style: textStyles.textXxs,
                              color: theme.text.textSecondary,
                            ),
                            DuploTextSpan(
                              text: '.',
                              style: textStyles.textXxs,
                              color: theme.text.textSecondary,
                            ),
                            DuploTextSpan(
                              text:
                                  '${accountModel.homeCurrency}-${accountModel.accountNumber}',
                              style: textStyles.textXxs,
                              color: theme.text.textSecondary,
                            ),
                          ],
                        ),
                        AnimatedBuilder(
                          animation: sheetAnimation,
                          builder: (animationContext, child) {
                            // Determine if we should show equity based on animation value
                            // Animation value goes from 0 (min) to 1 (max)
                            // We'll show equity when animation value >= 0.92 (92% of max)
                            final shouldShowEquity =
                                sheetAnimation.value >= 0.92;

                            if (shouldShowEquity)
                              return BlocSelector<
                                AccountBalanceBloc,
                                AccountBalanceState,
                                TradingAccountModel
                              >(
                                selector: (state) => state.tradingAccountModel,
                                builder: (equityContext, accountBalanceModel) {
                                  return Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      DuploText(
                                        text:
                                            '${EquitiFormatter.decimalPatternDigits(value: accountBalanceModel.equity ?? 0, digits: 2, locale: Localizations.localeOf(equityContext).toString())}',
                                        style: textStyles.textXs,
                                        color: theme.text.textPrimary,
                                        fontWeight: DuploFontWeight.semiBold,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(width: 4),
                                      DuploText(
                                        text: accountBalanceModel.homeCurrency,
                                        style: textStyles.textXxs,
                                        color: theme.text.textPrimary,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  );
                                },
                              );
                            else if (accountModel.name case final name?)
                              return DuploText(
                                text: name,
                                style: textStyles.textXs,
                                color: theme.text.textPrimary,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              );
                            else
                              return const SizedBox.shrink();
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Assets.images.chevronSelectorVertical.svg(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(56);
}
