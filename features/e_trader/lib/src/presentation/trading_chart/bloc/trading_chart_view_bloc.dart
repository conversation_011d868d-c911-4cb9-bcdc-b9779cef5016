import 'dart:async';

import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_chart_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/presentation/model/chart_timing_schema.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:preferences/preferences.dart';
import 'package:prelude/prelude.dart';

part 'trading_chart_view_bloc.freezed.dart';
part 'trading_chart_view_event.dart';
part 'trading_chart_view_state.dart';

class TradingChartViewBloc
    extends Bloc<TradingChartViewEvent, TradingChartViewState>
    with DisposableMixin {
  final LoggerBase _logger;
  final EquitiPreferences _preferences;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final GetTradingChartUseCase _getTradingChartUseCase;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;

  TradingChartViewBloc({
    required LoggerBase logger,
    required EquitiPreferences preferences,
    required GetAccountNumberUseCase getAccountNumberUseCase,
    required GetTradingChartUseCase getTradingChartUseCase,
    required SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
  }) : _logger = logger,
       _preferences = preferences,
       _getAccountNumberUseCase = getAccountNumberUseCase,
       _getTradingChartUseCase = getTradingChartUseCase,
       _subscribeToSymbolQuotesUseCase = subscribeToSymbolQuotesUseCase,
       super(const TradingChartViewState()) {
    on<_ChartLoadingState>(_chartLoadingState);
    on<_OnGetInitialSetting>(_onGetInitialSetting);
  }

  FutureOr<void> _onGetInitialSetting(
    _OnGetInitialSetting event,
    Emitter<TradingChartViewState> emit,
  ) async {
    final String chart_timing = _preferences.getValue<String>(
      "chart_timing",
      "",
    );

    final ChartTimingSchema? chart_timing_schema =
        ChartTimingSchema.values
            .where((e) => e.shortcut == chart_timing)
            .firstOrNull;

    if (chart_timing_schema != null) {
      emit(state.copyWith(chartTiming: chart_timing_schema));
    }

    await _getAccountNumberUseCase().toTaskEither().chainFirst((accountNumber) {
      if (!isClosed) emit(state.copyWith(accountNumber: accountNumber));
      return TaskEither.of(accountNumber);
    }).run();
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }

  FutureOr<void> _chartLoadingState(
    _ChartLoadingState event,
    Emitter<TradingChartViewState> emit,
  ) {
    emit(state.copyWith(processState: event.state));
  }

  // ignore: avoid-bloc-public-methods
  Future<List<Map<String, Object?>>> onGetChart({
    required String symbol,
    required String fromDate,
    required String toDate,
    required String timeframMinutes,
  }) async {
    final result =
        await _getAccountNumberUseCase().toTaskEither().flatMap((
          accountNumber,
        ) {
          return _getTradingChartUseCase(
            accountNumber: accountNumber,
            symbolCode: symbol,
            fromDate: fromDate,
            toDate: toDate,
            timeframMinutes: timeframMinutes,
          );
        }).run();

    final List<Map<String, Object?>> listOfResult = result.fold(
      (exception) => [],
      (listOfCandles) {
        return listOfCandles.map((e) {
          return {
            "time":
                DateTime.parse("${e.dateTime}Z").millisecondsSinceEpoch.abs(),
            "low": e.low,
            "high": e.high,
            "close": e.close,
            "open": e.open,
          };
        }).toList();
      },
    );

    /// Sort listOfResult by time
    // listOfResult.sort((a, b) => (a["time"] as int).compareTo(b["time"] as int));
    return listOfResult;
  }

  // ignore: avoid-bloc-public-methods
  TaskEither<Exception, Stream<SymbolQuoteModel>> getSymbolQuotesStream({
    required String symbol,
  }) => _subscribeToSymbolQuotesUseCase(
    symbol: symbol,
    subscriberId: '${TradingChartViewBloc}_$hashCode',
  );
}
