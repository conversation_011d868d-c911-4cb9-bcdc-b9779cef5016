import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/trading_chart/bloc/trading_chart_view_bloc.dart';
import 'package:e_trader/src/presentation/trading_chart/full_screen_portal.dart';
import 'package:e_trader/src/presentation/trading_chart/widgets/chart_state/chart_error_view.dart';
import 'package:e_trader/src/presentation/trading_chart/widgets/web_view/chart_webview.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AdvanceTradingView extends StatefulWidget {
  final String symbol;
  final int digit;
  final SheetController? sheetController;
  final bool interactionsEnabled;

  const AdvanceTradingView({
    super.key,
    required this.symbol,
    required this.digit,
    this.sheetController,
    required this.interactionsEnabled,
  });

  @override
  State<AdvanceTradingView> createState() => _AdvanceTradingViewState();
}

class _AdvanceTradingViewState extends State<AdvanceTradingView>
    with WidgetsBindingObserver, PerformanceObserverMixin {
  final GlobalKey<ChartWebviewState> chartKey = GlobalKey();

  Widget? _dynamicWidget;
  final portalController = FullscreenPortalController();
  bool fullScreenMode = false;
  bool _chartIsVisible = true;

  Orientation? lastOrientation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setupSheetListener();
  }

  void _setupSheetListener() {
    if (widget.sheetController != null) {
      widget.sheetController!.addListener(_onSheetChanged);
    }
  }

  void _onSheetChanged() {
    final isSheetExpanded = _isSheetFullyExpanded();
    final shouldAllowFullscreen = !isSheetExpanded;

    if (_chartIsVisible != shouldAllowFullscreen) {
      _chartIsVisible = shouldAllowFullscreen;
      changeFullScreenMode(shouldAllowFullscreen);
    }
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    changeFullScreenMode(true);
    super.onRoutePopped(route);
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    changeFullScreenMode(false);
    super.onRoutePushed(route);
  }

  void changeFullScreenMode(bool newFullScreenMode) {
    if (fullScreenMode != newFullScreenMode) {
      fullScreenMode = newFullScreenMode;
      if (fullScreenMode) {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeRight,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.portraitUp,
        ]);
      } else {
        SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      }
    }
  }

  bool _isSheetFullyExpanded() {
    if (widget.sheetController == null) return false;

    final metrics = widget.sheetController!.metrics;
    if (metrics == null) return false;

    final currentOffset = metrics.offset;
    final maxOffset = metrics.maxOffset;

    // Consider the sheet fully expanded if it's at 90% or more of max offset
    // This provides a small buffer for floating point precision
    return currentOffset >= (maxOffset * 0.9);
  }

  @override
  void dispose() {
    widget.sheetController?.removeListener(_onSheetChanged);
    changeFullScreenMode(false);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final orientation = MediaQuery.orientationOf(context);
    if (lastOrientation != orientation) {
      switch (orientation) {
        case Orientation.landscape:
          portalController.enter(context);
        case Orientation.portrait:
          portalController.exit();
      }
      lastOrientation = orientation;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocProvider(
      create:
          (createContext) =>
              diContainer<TradingChartViewBloc>()
                ..add(TradingChartViewEvent.onGetInitialSetting()),
      child: BlocBuilder<TradingChartViewBloc, TradingChartViewState>(
        buildWhen:
            (previous, current) =>
                previous.processState != current.processState ||
                previous.chartType != current.chartType ||
                previous.chartTiming != current.chartTiming ||
                previous.accountNumber != current.accountNumber,
        builder: (builderContext, state) {
          if (state.processState == ChartViewProcessState.error()) {
            return ChartErrorView(
              onReload:
                  () => context.read<TradingChartViewBloc>().add(
                    TradingChartViewEvent.onGetInitialSetting(),
                  ),
            );
          }
          if (_dynamicWidget == null) {
            if (Platform.environment.containsKey('FLUTTER_TEST')) {
              _dynamicWidget = Container(color: Colors.yellow);
            } else {
              _dynamicWidget = ChartWebview(
                key: chartKey,
                accountNumber: state.accountNumber,
                symbol: widget.symbol,
                digits: widget.digit,
                chartType: state.chartType,
                timeFrameInMinutes: state.chartTiming.timeFrameInMinutes,
              );
            }
          } else {
            chartKey.currentState?.updateConfig(
              chartType: state.chartType,
              timeFrameInMinutes: state.chartTiming.timeFrameInMinutes,
              interactionsEnabled: widget.interactionsEnabled,
            );
          }

          return VisibilityDetector(
            key: const Key('advance_trading_view'),
            onVisibilityChanged: (info) {
              // Only update fullscreen mode based on actual chart visibility
              // Sheet state is handled separately by the sheet listener
              if (info.visibleFraction < 1) {
                changeFullScreenMode(false);
              } else if (_chartIsVisible) {
                // Only allow fullscreen if chart is visible and sheet allows it
                changeFullScreenMode(true);
              }
            },
            child: FullscreenPortal(
              controller: portalController,
              child: Container(child: _dynamicWidget),
              fullscreenBuilder:
                  (exitFullscreen) => Scaffold(
                    backgroundColor: theme.background.bgPrimary,
                    body: Container(
                      color: theme.background.bgPrimary,
                      child: SafeArea(child: _dynamicWidget!),
                    ),
                  ),
            ),
          );
        },
      ),
    );
  }
}
