import 'dart:async';
import 'package:e_trader/src/data/api/change_account_password_request_model.dart';
import 'package:e_trader/src/domain/usecase/change_account_password_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
part 'change_account_password_event.dart';
part 'change_account_password_state.dart';
part 'change_account_password_bloc.freezed.dart';

class ChangeAccountPasswordBloc
    extends Bloc<ChangeAccountPasswordEvent, ChangeAccountPasswordState> {
  final ChangeAccountPasswordUseCase _changeAccountPasswordUseCase;
  final LoggerBase _logger;
  ChangeAccountPasswordBloc({
    required ChangeAccountPasswordUseCase changeAccountPasswordUseCase,
    required LoggerBase logger,
  }) : _logger = logger,
       _changeAccountPasswordUseCase = changeAccountPasswordUseCase,

       super(ChangeAccountPasswordState()) {
    on<_OnChangePassword>(_onChangePassword);
    on<_UpdateContinueButtonFlag>(_updateContinueButtonFlag);
  }

  FutureOr<void> _onChangePassword(
    _OnChangePassword event,
    Emitter<ChangeAccountPasswordState> emit,
  ) async {
    ChangeAccountPasswordRequestModel requestModel =
        ChangeAccountPasswordRequestModel(
          password: event.password,
          newPassword: event.newPassword,
          accountNumber: event.accountNumber,
        );
    final result =
        await _changeAccountPasswordUseCase(
          requestModel,
          event.accountNumber,
        ).run();
    result.fold(
      (exception) {
        if (!isClosed)
          emit(
            state.copyWith(
              processState: ChangeAccountPasswordProcessState.error(),
            ),
          );
        addError(exception);
      },
      (success) {
        if (!isClosed)
          emit(
            state.copyWith(
              processState: ChangeAccountPasswordProcessState.success(),
            ),
          );
      },
    );
  }

  void _updateContinueButtonFlag(
    _UpdateContinueButtonFlag event,
    Emitter<ChangeAccountPasswordState> emit,
  ) {
    emit(state.copyWith(isContinueButtonDisabled: event.isValid));
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }
}
