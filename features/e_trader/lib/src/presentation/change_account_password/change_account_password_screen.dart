import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as assetsTrader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/change_account_password/bloc/change_account_password_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChangeAccountPasswordScreen extends StatefulWidget {
  const ChangeAccountPasswordScreen({super.key, required this.accountNumber});
  final String accountNumber;

  @override
  State<ChangeAccountPasswordScreen> createState() =>
      _ChangeAccountPasswordScreenState();
}

class _ChangeAccountPasswordScreenState
    extends State<ChangeAccountPasswordScreen> {
  late final TextEditingController _currentPasswordController =
      TextEditingController();
  late final TextEditingController _newPasswordController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create: (createContext) => diContainer<ChangeAccountPasswordBloc>(),
      child: BlocBuilder<ChangeAccountPasswordBloc, ChangeAccountPasswordState>(
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          switch (state.processState) {
            case ChangeAccountPasswordProcessStateInitial():
              return Container(
                decoration: BoxDecoration(color: theme.background.bgPrimary),
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.all(16),
                        children: [
                          DuploText(
                            text:
                                localization
                                    .trader_changeAccountPasswordDescription,
                            style: context.duploTextStyles.textSm,
                            color: theme.text.textSecondary,
                            textAlign: TextAlign.start,
                            fontWeight: DuploFontWeight.regular,
                          ),
                          SizedBox(height: 16),
                          PasswordFieldValidator(
                            semanticsIdentifier: 'current_password',
                            controller: _currentPasswordController,
                            label: localization.trader_currentPassword,
                            hint: localization.trader_currentPassword,
                            backgroundColor: theme.background.bgSecondary,
                            onPasswordValidation: (
                              String password,
                              bool isValid,
                            ) {
                              debugPrint('');
                            },
                          ),
                          SizedBox(height: DuploSpacing.spacing_3xl_24),
                          DuploText(
                            text: localization.trader_setNewPassword,
                            style: context.duploTextStyles.textXl,
                            color: theme.text.textPrimary,
                            textAlign: TextAlign.start,
                            fontWeight: DuploFontWeight.semiBold,
                          ),
                          SizedBox(height: DuploSpacing.spacing_lg_12),
                          PasswordFieldValidator(
                            semanticsIdentifier: 'new_password',
                            controller: _newPasswordController,
                            needValidationComponent: true,
                            backgroundColor: theme.background.bgSecondary,
                            onPasswordValidation: (
                              String password,
                              bool isValid,
                            ) {
                              builderContext.read<ChangeAccountPasswordBloc>().add(
                                ChangeAccountPasswordEvent.updateContinueButtonFlag(
                                  !isValid,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 16.0,
                        right: 16.0,
                        bottom: 16.0,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 16),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 4.0),
                                child:
                                    assetsTrader.Assets.images.alertTriangle
                                        .svg(),
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: DuploText(
                                  text:
                                      localization
                                          .trader_changeAccountPasswordWarning,
                                  style: context.duploTextStyles.textSm,
                                  color: theme.text.textSecondary,
                                  textAlign: TextAlign.start,
                                  maxLines: 3,
                                  fontWeight: DuploFontWeight.regular,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: DuploSpacing.spacing_xl_16),
                          DuploButton.defaultPrimary(
                            key: const Key('change_password_continue_button'),
                            isDisabled: state.isContinueButtonDisabled,
                            title: localization.trader_continue,
                            useFullWidth: true,
                            trailingIcon:
                                Assets.images
                                    .chevronRightDirectional(builderContext)
                                    .keyName,
                            onTap:
                                () => builderContext
                                    .read<ChangeAccountPasswordBloc>()
                                    .add(
                                      ChangeAccountPasswordEvent.onChangePassword(
                                        widget.accountNumber,
                                        _currentPasswordController.text,
                                        _newPasswordController.text,
                                      ),
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            case ChangeAccountPasswordProcessStateLoading():
              return Center(child: CircularProgressIndicator());
            case ChangeAccountPasswordProcessStateSuccess():
              return Center(
                child: DuploText(
                  text: localization.trader_passwordChangedSuccessfully,
                  style: duploTextStyles.textMd,
                  color: theme.text.textPrimary,
                ),
              );
            case ChangeAccountPasswordProcessStateError():
              return Center(
                child: DuploText(
                  text: localization.trader_somethingWentWrong,
                  style: duploTextStyles.textMd,
                  color: theme.text.textPrimary,
                ),
              );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    super.dispose();
  }
}
