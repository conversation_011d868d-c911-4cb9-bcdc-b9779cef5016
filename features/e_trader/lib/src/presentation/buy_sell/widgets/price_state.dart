import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/model/parsed_price_text.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class PriceState extends StatelessWidget {
  const PriceState({
    super.key,
    required this.textColor,
    required this.price,
    required this.isBuy,
    required this.digits,
  });
  final Color textColor;
  final double price;
  final int digits;
  final bool isBuy;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = context.duploTextStyles;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment:
          isBuy ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        DuploText(
          text:
              isBuy
                  ? EquitiLocalization.of(context).trader_buy
                  : EquitiLocalization.of(context).trader_sell,
          style: duploTextStyles.textSm,
          color: textColor,
          fontWeight: DuploFontWeight.medium,
        ),
        ParsedPriceText(color: textColor, value: price, digits: digits),
      ],
    );
  }
}
