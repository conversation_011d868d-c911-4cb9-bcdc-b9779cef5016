import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/buy_sell/widgets/loading_state.dart';
import 'package:e_trader/src/presentation/buy_sell/widgets/placed_state.dart';
import 'package:e_trader/src/presentation/buy_sell/widgets/price_state.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:flutter/material.dart';

class TradeButton extends StatelessWidget {
  const TradeButton({
    super.key,
    required this.onTap,
    required this.tradeType,
    required this.buttonState,
    required this.digits,
  });

  final void Function() onTap;
  final TradeType tradeType;
  final BuySellButtonState buttonState;
  final int digits;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final isBuy = tradeType == TradeType.buy;
    Color backgroundColor;
    Color textColor;
    Widget? child;

    switch (buttonState) {
      case BuySellButtonLoadingState():
        textColor = theme.text.textPrimaryOnBrand;
        backgroundColor =
            isBuy
                ? theme.foreground.fgSuccessPrimary
                : theme.foreground.fgErrorPrimary;
        child = LoadingState();
      case BuySellButtonSuccessState():
        textColor = theme.text.textPrimaryOnBrand;
        backgroundColor = theme.utility.utilityBrand300Alt;
        child = PlacedState();
      case BuySellButtonDisabledState(:final price):
        textColor = theme.foreground.fgDisabled;
        backgroundColor = theme.border.borderDisabledSubtle;
        child = PriceState(
          isBuy: isBuy,
          textColor: textColor,
          price: price,
          digits: digits,
        );
      case BuySellButtonSelectedState(:final price):
        textColor = theme.text.textPrimaryOnBrand;
        backgroundColor =
            isBuy
                ? theme.foreground.fgSuccessPrimary
                : theme.foreground.fgErrorPrimary;
        child = PriceState(
          isBuy: isBuy,
          textColor: textColor,
          price: price,
          digits: digits,
        );
      case BuySellButtonActiveState(:final price):
        textColor =
            isBuy ? theme.text.textSuccessPrimary : theme.text.textErrorPrimary;
        backgroundColor =
            isBuy
                ? theme.utility.utilitySuccess100
                : theme.utility.utilityError100;
        child = PriceState(
          isBuy: isBuy,
          textColor: textColor,
          price: price,
          digits: digits,
        );
      case BuySellButtonErrorState():

        /// TODO add error state
        backgroundColor = theme.text.textPlaceholder;
        child = Container();
    }

    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: isBuy ? Radius.zero : Radius.circular(12),
            bottomLeft: isBuy ? Radius.zero : Radius.circular(12),
            topRight: isBuy ? Radius.circular(12) : Radius.zero,
            bottomRight: isBuy ? Radius.circular(12) : Radius.zero,
          ),
        ),
        child: DuploTap(
          useMaterial: true,
          splashColor: Colors.white.withAlpha(77),
          highlightColor: Colors.white.withAlpha(51),
          onTap: switch (buttonState) {
            BuySellButtonActiveState() || BuySellButtonSelectedState() => onTap,
            _ => null,
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: child,
          ),
        ),
      ),
    );
  }
}
