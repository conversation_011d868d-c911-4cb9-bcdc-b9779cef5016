import 'package:clock/clock.dart';
import 'package:e_trader/src/data/api/funding_activity_response.dart';
import 'package:e_trader/src/data/api/funding_request_body.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/usecase/get_funding_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'funding_bloc.freezed.dart';
part 'funding_event.dart';
part 'funding_state.dart';

class FundingBloc extends Bloc<FundingEvent, FundingState> {
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final GetFundingUseCase _getFundingUseCase;
  final Clock _clock;

  FundingBloc(
    this._getSelectedAccountUseCase,
    this._getFundingUseCase,
    this._clock,
  ) : super(FundingState()) {
    on<_FetchFunding>(_fetchFunding);
  }

  Future<void> _fetchFunding(
    _FetchFunding event,
    Emitter<FundingState> emit,
  ) async {
    // Use the hasFilters parameter from the event if provided
    final bool hasFilters = event.hasFilters ?? state.hasFilters;

    if (event.isStartFromPageOne ?? false) {
      emit(
        state.copyWith(
          processState: FundingProcessState.loading(),
          fundingItems: [],
          currentPage: 1,
          fromDate: event.fromDate,
          toDate: event.toDate,
          fundFlow: event.fundFlow,
          status: event.status,
          transactionType: event.transactionType,
          hasFilters: hasFilters,
          sortOrder: event.sortOrder ?? state.sortOrder,
          fundingItemsCount: 0,
        ),
      );
    } else {
      emit(state.copyWith(processState: FundingProcessState.loading()));
    }

    String? accountNumber = state.accountNumber;
    String? creationDate = state.creationDate;

    if (accountNumber.isNullOrEmpty || creationDate == null) {
      final client = _getSelectedAccountUseCase();
      accountNumber = client?.accountNumber;
      creationDate = client?.dateCreated;

      emit(
        state.copyWith(
          accountNumber: accountNumber,
          creationDate: creationDate,
        ),
      );
    }

    final DateTime? fromDate = event.fromDate ?? state.fromDate;
    final DateTime? toDate = event.toDate ?? state.toDate;

    final String fromDateStr =
        fromDate == null
            ? DateTime.parse(creationDate!).toIso8601String()
            : fromDate.toIso8601String();
    final String toDateStr =
        toDate == null
            ? _clock.now().toIso8601String()
            : toDate.toIso8601String();

    final body = FundingRequestBody(
      isFunding: true,
      accountNumber: accountNumber!,
      fromDate: fromDateStr,
      toDate: toDateStr,
      pageNumber: state.currentPage,
      pageSize: 10,
      sortingOrder:
          (event.sortOrder != null
                  ? event.sortOrder == SortOrder.ascending
                  : state.sortOrder == SortOrder.ascending)
              ? "Ascending"
              : "Descending",
      fundFlow: event.fundFlow ?? state.fundFlow,
      status: event.status ?? state.status,
      transactionType: event.transactionType ?? state.transactionType,
    );

    final result = await _getFundingUseCase(body).run();
    result.fold(
      (exception) => emit(
        state.copyWith(processState: FundingProcessState.error(exception)),
      ),
      (fundingResponse) {
        final List<FundingItem> newFundingItems = List<FundingItem>.of(
          state.fundingItems,
        );

        if (fundingResponse.list != null) {
          newFundingItems.addAll(fundingResponse.list!);
        }

        // Check for empty results with filters
        final bool emptyResults =
            newFundingItems.isEmpty && hasFilters && state.currentPage == 1;

        final processState =
            emptyResults
                ? FundingProcessState.emptyFilterResult()
                : FundingProcessState.success();

        emit(
          state.copyWith(
            processState: processState,
            fundingItems: newFundingItems,
            currentPage: state.currentPage + 1,
            creationDate: state.creationDate ?? creationDate,
            accountNumber: state.accountNumber ?? accountNumber,
            fundingItemsCount: fundingResponse.totalCount,
            sortOrder: event.sortOrder ?? state.sortOrder,
            fromDate: fromDate,
            toDate: toDate,
            fundFlow: event.fundFlow ?? state.fundFlow,
            status: event.status ?? state.status,
            transactionType: event.transactionType ?? state.transactionType,
            hasFilters: hasFilters,
          ),
        );
      },
    );
  }
}
