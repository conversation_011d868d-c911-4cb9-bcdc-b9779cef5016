import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/funding_activity_response.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

class FundingTile extends StatelessWidget {
  final FundingItem fundingItem;
  final bool showDate;
  final VoidCallback? onTap;
  final bool useCurrencyName;

  const FundingTile({
    Key? key,
    required this.fundingItem,
    this.showDate = false,
    this.onTap,
    this.useCurrencyName = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);

    String? getActivityTypeDisplay() {
      if (fundingItem.activityType == 'Transfer') {
        return fundingItem.activityDetail?.account;
      } else if (fundingItem.activityType == 'Deposit') {
        return fundingItem.activityDetail?.paymentMethod;
      } else if (fundingItem.activityType == 'Withdrawal') {
        return fundingItem.activityDetail?.paymentMethod;
      } else if (fundingItem.activityType == 'CreditRemoved' ||
          fundingItem.activityType == 'BonusCreditApplied' ||
          fundingItem.activityType == 'CreditApplied') {
        return loc.trader_credit;
      }
      return fundingItem.activityTypeName;
    }

    String getActivityTypeSupportingText() {
      if (fundingItem.activityType == 'Transfer') {
        return (fundingItem.activityTypeName ??
                fundingItem.activityType ??
                " ") +
            (" " +
                (fundingItem.activityDetail?.direction?.decapitalize() ?? ""));
      } else if (fundingItem.activityType == 'Adjustment') {
        return fundingItem.activityDetail?.transactionType ?? "";
      }
      return fundingItem.activityTypeName ??
          fundingItem.activityType ??
          'Unknown';
    }

    String getAmountDisplay() {
      String prefix = '';
      if (fundingItem.activityDetail?.amount == null) return '';
      if ((fundingItem.activityDetail?.direction == 'Out' &&
              fundingItem.activityType == 'Transfer') ||
          fundingItem.activityType == "CreditRemoved" ||
          fundingItem.activityType == 'Withdrawal' ||
          ((fundingItem.activityDetail?.amount ?? 0) < 0)) {
        prefix = '-';
      }
      return '$prefix${useCurrencyName ? "" : EquitiFormatter.getCurrencySymbol(fundingItem.activityDetail?.currency)}${EquitiFormatter.decimalPatternDigits(digits: 2, value: fundingItem.activityDetail?.amount?.abs() ?? 0, locale: Localizations.localeOf(context).toString())}';
    }

    Widget getImageFromType() {
      if (fundingItem.activityType == 'Transfer') {
        return trader.Assets.images.fundingTransfer.svg();
      } else if (fundingItem.activityType == 'Deposit') {
        return trader.Assets.images.fundingDeposit.svg();
      } else if (fundingItem.activityType == 'Withdrawal') {
        return trader.Assets.images.fundingWithdraw.svg();
      } else if (fundingItem.activityType == 'Adjustment') {
        return trader.Assets.images.fundingDividendsPaid.svg();
      } else if (fundingItem.activityType == 'CreditRemoved' ||
          fundingItem.activityType == 'CreditApplied' ||
          fundingItem.activityType == 'BonusCreditApplied') {
        return trader.Assets.images.fundingCreditAwarded.svg();
      }
      return SizedBox(width: 24, height: 24);
    }

    Color getAmountColor() {
      switch (fundingItem.activityDetail?.status?.toLowerCase()) {
        case "rejected":
          return theme.foreground.fgQuinary;
        case "accepted":
          if ((fundingItem.activityDetail?.direction == 'Out' &&
                  fundingItem.activityType == 'Transfer') ||
              fundingItem.activityType == "CreditRemoved" ||
              fundingItem.activityType == 'Withdrawal' ||
              ((fundingItem.activityDetail?.amount ?? 0) < 0)) {
            return theme.text.textErrorPrimary;
          }
          return theme.text.textPrimary;
        case "pending":
        default:
          return theme.text.textPrimary;
      }
    }

    String getStatusDisplay() {
      return fundingItem.activityDetail?.statusName ??
          fundingItem.activityDetail?.status ??
          '';
    }

    Color getStatusColor() {
      final status = fundingItem.activityDetail?.status?.toLowerCase() ?? '';
      if (status == 'accepted') {
        return theme.foreground.fgQuinary;
      } else if (status == 'rejected') {
        return theme.text.textErrorPrimary;
      } else if (status == 'pending') {
        return theme.text.textWarningPrimary;
      }
      return theme.foreground.fgQuinary;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        showDate
            ? Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: DuploText(
                text: EquitiFormatter.formatDateWithSuffix(
                  fundingItem.dateTime,
                  locale: Localizations.localeOf(context).toString(),
                ),
                color: context.duploTheme.text.textTertiary,
                style: context.duploTextStyles.textXs,
                fontWeight: DuploFontWeight.medium,
              ),
            )
            : const SizedBox(),
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.background.bgPrimary,
              border: Border(
                top: BorderSide(color: theme.border.borderSecondary, width: 1),
                bottom: BorderSide(
                  color: theme.border.borderSecondary,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    getImageFromType(),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        DuploText(
                          text: getActivityTypeDisplay(),
                          style: style.textSm,
                          color: theme.text.textPrimary,
                          fontWeight: DuploFontWeight.semiBold,
                        ),
                        DuploText(
                          text: getActivityTypeSupportingText(),
                          style: style.textXs,
                          color: theme.text.textTertiary,
                        ),
                      ],
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Directionality(
                      textDirection: TextDirection.ltr,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          DuploText(
                            text:
                                getAmountDisplay().split('.').firstOrNull! +
                                ".",
                            style: style.textSm,
                            color: getAmountColor(),
                            fontWeight: DuploFontWeight.bold,
                          ),
                          DuploText(
                            text:
                                getAmountDisplay()
                                    .split('.')
                                    .elementAtOrNull(1) ??
                                '00',
                            style: style.textXs,
                            color: getAmountColor(),
                            fontWeight:
                                useCurrencyName
                                    ? DuploFontWeight.regular
                                    : DuploFontWeight.bold,
                          ),
                          if (useCurrencyName)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: DuploText(
                                text:
                                    " " +
                                    (fundingItem.activityDetail?.currency ??
                                        ""),
                                style: style.textXxs,
                                color: theme.text.textSecondary,
                              ),
                            ),
                        ],
                      ),
                    ),
                    DuploText(
                      text: getStatusDisplay(),
                      style: style.textXs,
                      color: getStatusColor(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
