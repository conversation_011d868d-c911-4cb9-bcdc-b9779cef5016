import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_filter_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class FundingBottomSheet {
  static void showSort(
    BuildContext context, {
    required SortOrder currentSortOrder,
    required void Function(SortOrder) onSortOrderChanged,
  }) {
    final loc = EquitiLocalization.of(context);

    final allSortOptions = <SelectionOptionModel>[];

    SelectionOptionModel selected = SelectionOptionModel(
      displayText: SortOrderOptions.defaultOption.displayAsNewestToOldest(loc),
      identifier: SortOrderOptions.defaultOption.indentifier(),
    );

    SortOrder.values.toList().reversed.forEach((element) {
      final model = SelectionOptionModel(
        displayText: element.displayAsNewestToOldest(loc),
        identifier: element.indentifier(),
      );
      if (model.identifier == currentSortOrder.indentifier()) {
        selected = model;
      }
      allSortOptions.add(model);
    });

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: loc.trader_sortFundingBy,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          Builder(
            builder:
                (builderContext) => IconButton(
                  icon: Assets.images.closeIc.svg(),
                  onPressed: () => Navigator.pop(builderContext),
                ),
          ),
        ],
      ),
      content: Builder(
        builder:
            (modalContext) => TextSelectionComponentScreen(
              buttonTitle: loc.trader_sortFunding,
              options: allSortOptions,
              pageTitle: loc.trader_sortFundingBy,
              selected: selected,
              onSelection: (selectedOption) {
                final order = selectedOption.identifier.toSortOrder();
                if (order != currentSortOrder) {
                  onSortOrderChanged(order);
                }

                Navigator.pop(modalContext);
              },
            ),
      ),
    );
  }

  static void showFilter(
    BuildContext context, {
    required DateTime? initialFromDate,
    required DateTime? initialToDate,
    required String? initialFundFlow,
    required String? initialStatus,
    required String? initialTransactionType,
    required void Function({
      DateTime? fromDate,
      DateTime? toDate,
      String? fundFlow,
      String? status,
      String? transactionType,
      required bool hasFilters,
    })
    onFiltersApplied,
  }) {
    final loc = EquitiLocalization.of(context);

    DateTime? selectedFromDate = initialFromDate;
    DateTime? selectedToDate = initialToDate;
    String? selectedFundFlow = initialFundFlow;
    String? selectedStatus = initialStatus;
    String? selectedTransactionType = initialTransactionType;

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: loc.trader_filterFundingBy,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          Builder(
            builder:
                (builderContext) => IconButton(
                  icon: Assets.images.closeIc.svg(),
                  onPressed: () => Navigator.pop(builderContext),
                ),
          ),
        ],
      ),
      content: Builder(
        builder:
            (modalContext) => StatefulBuilder(
              builder: (statefulBuilderContext, setState) {
                return FundingFilterBottomSheet(
                  selectedFromDate: selectedFromDate,
                  selectedToDate: selectedToDate,
                  selectedFundFlow: selectedFundFlow,
                  selectedStatus: selectedStatus,
                  selectedTransactionType: selectedTransactionType,
                  onFromDateChanged: (date) {
                    setState(() {
                      selectedFromDate = date;
                      if (selectedToDate != null &&
                          date.isAfter(selectedToDate!)) {
                        selectedToDate = date;
                      }
                    });
                  },
                  onToDateChanged: (date) {
                    setState(() {
                      selectedToDate = date;
                    });
                  },
                  onFundFlowChanged: (fundFlow) {
                    setState(() {
                      selectedFundFlow = fundFlow;
                    });
                  },
                  onStatusChanged: (status) {
                    setState(() {
                      selectedStatus = status;
                    });
                  },
                  onTransactionTypeChanged: (transactionType) {
                    setState(() {
                      selectedTransactionType = transactionType;
                    });
                  },
                  onClearAll: () {
                    setState(() {
                      selectedFromDate = null;
                      selectedToDate = null;
                      selectedFundFlow = null;
                      selectedStatus = null;
                      selectedTransactionType = null;
                    });
                  },
                  onApplyFilters: () {
                    final hasFilters =
                        selectedFromDate != null ||
                        selectedToDate != null ||
                        selectedFundFlow?.isNotEmpty == true ||
                        selectedStatus?.isNotEmpty == true ||
                        selectedTransactionType?.isNotEmpty == true;

                    onFiltersApplied(
                      fromDate: selectedFromDate,
                      toDate: selectedToDate,
                      fundFlow: selectedFundFlow,
                      status: selectedStatus,
                      transactionType: selectedTransactionType,
                      hasFilters: hasFilters,
                    );
                    Navigator.pop(modalContext);
                  },
                );
              },
            ),
      ),
    );
  }
}
