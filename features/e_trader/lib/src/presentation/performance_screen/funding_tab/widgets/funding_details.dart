import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/funding_activity_response.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class FundingDetails extends StatelessWidget {
  final FundingItem fundingItem;
  final String accountNumber;
  const FundingDetails({
    super.key,
    required this.fundingItem,
    required this.accountNumber,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);
    final detailsList = getDetailsExtraListItems(fundingItem, loc);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FundingTile(fundingItem: fundingItem, showDate: false),
        Expanded(
          child: Container(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  DuploKeyValueDisplay(
                    keyColor: theme.text.textSecondary,
                    valueColor: theme.text.textPrimary,
                    keyFontWeight: DuploFontWeight.medium,
                    valueFontWeight: DuploFontWeight.medium,
                    title:
                        Localizations.localeOf(context).toString() == 'ar'
                            ? "${loc.trader_funding_details} ${fundingItem.activityTypeName ?? ""}"
                            : "${fundingItem.activityTypeName ?? ""} ${loc.trader_funding_details}",
                    keyValuePairs: [
                      KeyValuePair(
                        label: loc.trader_date,
                        value: EquitiFormatter.formatDateTimeToDateString(
                          fundingItem.dateTime,
                        ),
                      ),
                      KeyValuePair(
                        label: loc.trader_time,
                        value: EquitiFormatter.formatDateTimeToTimeString(
                          fundingItem.dateTime,
                        ),
                      ),
                      KeyValuePair(
                        label: loc.trader_account,
                        value:
                            fundingItem.activityDetail?.account ??
                            accountNumber,
                      ),
                      ...detailsList,
                    ],
                  ),
                  Expanded(
                    child: Container(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          DuploText(
                            text: loc.trader_needHelpWithThis,
                            style: style.textSm,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 10),
                          DuploButton.secondary(
                            useFullWidth: true,
                            title: loc.trader_contactUs,
                            onTap: () => {},
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<KeyValuePair> getDetailsExtraListItems(
    FundingItem item,
    EquitiLocalization loc,
  ) {
    switch (item.activityType) {
      case "CreditApplied":
      case "CreditRemoved":
      case "Transfer":
        return [
          KeyValuePair(
            label: loc.trader_currency,
            value: item.activityDetail?.currency ?? "-",
          ),
        ];
      case "BonusCreditApplied":
        return [
          KeyValuePair(
            label: loc.trader_currency,
            value: item.activityDetail?.currency ?? "-",
          ),
          KeyValuePair(
            label: loc.trader_campaign,
            value: item.activityDetail?.campaign ?? "-",
          ),
        ];
      case "Adjustment":
        return [
          KeyValuePair(
            label: loc.trader_type,
            value: item.activityDetail?.transactionType ?? "-",
          ),
        ];
      case "Deposit":
      case "Withdrawal":
        return [
          KeyValuePair(
            label: loc.trader_paymentMethod,
            value: item.activityDetail?.paymentMethod ?? "-",
          ),
        ];
    }

    return [];
  }
}
