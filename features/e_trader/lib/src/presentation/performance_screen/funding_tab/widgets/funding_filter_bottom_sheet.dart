import 'dart:io' show Platform;

import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class FundingFilterBottomSheet extends StatelessWidget {
  final DateTime? selectedFromDate;
  final DateTime? selectedToDate;
  final String? selectedFundFlow;
  final String? selectedStatus;
  final String? selectedTransactionType;
  final void Function(DateTime) onFromDateChanged;
  final void Function(DateTime) onToDateChanged;
  final void Function(String?) onFundFlowChanged;
  final void Function(String?) onStatusChanged;
  final void Function(String?) onTransactionTypeChanged;
  final VoidCallback onClearAll;
  final VoidCallback onApplyFilters;

  const FundingFilterBottomSheet({
    Key? key,
    required this.selectedFromDate,
    required this.selectedToDate,
    required this.selectedFundFlow,
    required this.selectedStatus,
    required this.selectedTransactionType,
    required this.onFromDateChanged,
    required this.onToDateChanged,
    required this.onFundFlowChanged,
    required this.onStatusChanged,
    required this.onTransactionTypeChanged,
    required this.onClearAll,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);

    String formatDate(DateTime? date, EquitiLocalization localization) {
      if (date == null) return localization.trader_datePlaceholder;
      return EquitiFormatter.formatDayMonthYear(date);
    }

    Future<void> showPlatformDatePicker({
      required BuildContext context,
      required DateTime? initialDate,
      required DateTime firstDate,
      required DateTime lastDate,
      required void Function(DateTime) onDateSelected,
    }) async {
      DateTime validInitialDate = initialDate ?? diContainer<Clock>().now();
      if (validInitialDate.isAfter(lastDate)) {
        validInitialDate = lastDate;
      }
      if (validInitialDate.isBefore(firstDate)) {
        validInitialDate = firstDate;
      }

      final isDarkMode = diContainer<ThemeManager>().isDarkMode;

      final darkTheme = ThemeData(
        datePickerTheme: DatePickerThemeData(
          weekdayStyle: TextStyle(color: theme.text.textDisabled),
          todayBorder: BorderSide.none,
        ),
        colorScheme: ColorScheme.dark(
          primary: theme.background.bgBrandPrimary,
          onPrimary: theme.text.textPrimary,
          secondary: theme.text.textSecondaryOnBrand,
          onSecondary: theme.text.textSecondary,
          error: theme.foreground.fgErrorPrimary,
          onError: theme.background.bgSecondary,
          surface: theme.background.bgSecondary,
          onSurface: theme.text.textSecondaryOnBrand,
        ),
      );
      if (Platform.isIOS) {
        // iOS custom calendar dialog
        await showDialog<void>(
          context: context,
          builder: (builderContext) {
            DateTime tempDate = validInitialDate;
            return Theme(
              data:
                  isDarkMode
                      ? darkTheme
                      : Theme.of(context).copyWith(
                        colorScheme: ColorScheme.fromSeed(
                          seedColor: theme.text.textSecondaryOnBrand,
                          primary: theme.text.textSecondaryOnBrand,
                          onPrimary: theme.text.textPrimary,
                        ),
                        datePickerTheme: DatePickerThemeData(
                          weekdayStyle: TextStyle(
                            color: theme.text.textDisabled,
                          ),
                          todayBorder: BorderSide.none,
                        ),
                      ),
              child: AlertDialog(
                content: Container(
                  height: 300,
                  width: double.maxFinite,
                  child: CalendarDatePicker(
                    initialDate: tempDate,
                    firstDate: firstDate,
                    lastDate: lastDate,
                    onDateChanged: (date) {
                      tempDate = date;
                    },
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(builderContext),
                    child: DuploText(
                      text: loc.trader_cancel,
                      style: style.textSm,
                      color: theme.text.textPrimary,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      onDateSelected(tempDate);
                      Navigator.pop(builderContext);
                    },
                    child: DuploText(
                      text: loc.trader_ok,
                      style: style.textSm,
                      color: theme.text.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          },
        );
      } else {
        // Android date picker
        final DateTime? picked = await showDatePicker(
          context: context,
          initialDate: validInitialDate,
          firstDate: firstDate,
          lastDate: lastDate,
          builder: (builderContext, child) {
            return Theme(
              data:
                  isDarkMode
                      ? darkTheme
                      : Theme.of(context).copyWith(
                        colorScheme: ColorScheme.fromSeed(
                          seedColor: theme.text.textSecondaryOnBrand,
                          primary: theme.text.textSecondaryOnBrand,
                          onPrimary: theme.text.textPrimary,
                        ),
                        datePickerTheme: DatePickerThemeData(
                          weekdayStyle: TextStyle(
                            color: theme.text.textDisabled,
                          ),
                          todayBorder: BorderSide.none,
                        ),
                      ),
              child: child!,
            );
          },
        );
        if (picked != null) {
          onDateSelected(picked);
        }
      }
    }

    // Define the filter options
    final fundFlowOptions = [
      SelectionItem(id: 'MoneyIn', displayName: loc.trader_moneyIn),
      SelectionItem(id: 'MoneyOut', displayName: loc.trader_moneyOut),
    ];

    final statusOptions = [
      SelectionItem(id: 'Accepted', displayName: loc.trader_accepted),
      SelectionItem(id: 'Pending', displayName: loc.trader_pending),
      SelectionItem(id: 'Rejected', displayName: loc.trader_rejected),
    ];

    final transactionTypeOptions = [
      SelectionItem(id: 'Deposit', displayName: loc.trader_deposit),
      SelectionItem(id: 'Withdrawal', displayName: loc.trader_withdraw),
      SelectionItem(id: 'Transfer', displayName: loc.trader_transfer),
      SelectionItem(id: 'Credit', displayName: loc.trader_credit),
      SelectionItem(id: 'Bonus', displayName: loc.trader_bonus),
      SelectionItem(id: 'Dividend', displayName: loc.trader_dividend),
    ];

    return Container(
      color: theme.background.bgSecondary,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 26),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: loc.trader_selectCustomDatesFundingDesc,
                    style: style.textSm,
                    textAlign: TextAlign.start,
                    color: theme.foreground.fgSecondary,
                  ),
                  const SizedBox(height: 16),
                  InkWell(
                    onTap: () {
                      showPlatformDatePicker(
                        context: context,
                        initialDate: selectedFromDate,
                        firstDate: DateTime(2000),
                        lastDate: selectedToDate ?? diContainer<Clock>().now(),
                        onDateSelected: onFromDateChanged,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.border.borderSecondary),
                        borderRadius: BorderRadius.circular(8),
                        color: theme.background.bgSecondarySubtle,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: loc.trader_from,
                            style: style.textXs,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DuploText(
                                text: formatDate(selectedFromDate, loc),
                                style: style.textMd,
                                color: theme.text.textPlaceholder,
                              ),
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 20,
                                color: theme.foreground.fgDisabled,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  InkWell(
                    onTap: () {
                      showPlatformDatePicker(
                        context: context,
                        initialDate: selectedToDate,
                        firstDate: selectedFromDate ?? DateTime(2000),
                        lastDate: diContainer<Clock>().now(),
                        onDateSelected: onToDateChanged,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.border.borderSecondary),
                        borderRadius: BorderRadius.circular(8),
                        color: theme.background.bgSecondarySubtle,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: loc.trader_to,
                            style: style.textXs,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DuploText(
                                text: formatDate(selectedToDate, loc),
                                style: style.textMd,
                                color: theme.text.textPlaceholder,
                              ),
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 20,
                                color: theme.foreground.fgDisabled,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Divider(color: theme.border.borderSecondary, thickness: 1),
                  const SizedBox(height: 24),
                  DuploWrappedSelectionList(
                    title: loc.trader_fundFlow,
                    items: fundFlowOptions,
                    selectedItem: fundFlowOptions.firstOrNullWhere(
                      (item) => item.id == selectedFundFlow,
                    ),
                    onItemSelected: (item) {
                      onFundFlowChanged(item?.id);
                    },
                  ),
                  const SizedBox(height: 24),
                  Divider(color: theme.border.borderSecondary, thickness: 1),
                  const SizedBox(height: 24),
                  DuploWrappedSelectionList(
                    title: loc.trader_status,
                    items: statusOptions,
                    selectedItem: statusOptions.firstOrNullWhere(
                      (item) => item.id == selectedStatus,
                    ),
                    onItemSelected: (item) {
                      onStatusChanged(item?.id);
                    },
                  ),
                  const SizedBox(height: 24),
                  Divider(color: theme.border.borderSecondary, thickness: 1),
                  const SizedBox(height: 24),
                  DuploWrappedSelectionList(
                    title: loc.trader_TransactionType,
                    items: transactionTypeOptions,
                    selectedItem: transactionTypeOptions.firstOrNullWhere(
                      (item) => item.id == selectedTransactionType,
                    ),
                    onItemSelected: (item) {
                      onTransactionTypeChanged(item?.id);
                    },
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: DuploButton.secondary(
                    title: loc.trader_clearAll,
                    onTap: onClearAll,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DuploButton.defaultPrimary(
                    semanticsIdentifier: "apply_filters_funding",
                    title: loc.trader_applyFilters,
                    onTap: onApplyFilters,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 25),
        ],
      ),
    );
  }
}
