import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/bloc/funding_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_bottom_sheet.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_details.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_loading.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FundingTab extends StatelessWidget {
  const FundingTab({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return Scaffold(
      body: BlocProvider(
        create:
            (_) => diContainer<FundingBloc>()..add(FundingEvent.fetchFunding()),
        child: BlocConsumer<FundingBloc, FundingState>(
          buildWhen: (previous, current) => previous != current,
          listenWhen: (previous, current) => previous != current,
          listener: (BuildContext listenerContext, FundingState state) {
            if (state.processState is FundingErrorProcessState) {
              final toast = DuploToast();
              toast.hidesToastMessage();
              toast.showToastMessage(
                autoCloseDuration: const Duration(seconds: 3),
                context: listenerContext,
                widget: DuploToastMessage(
                  titleMessage: localization.trader_failedToLoad,
                  descriptionMessage:
                      localization.trader_failedToLoadDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () => toast.hidesToastMessage(),
                  actionButtonTitle: localization.trader_reload,
                  onTap: () {
                    toast.hidesToastMessage();
                    // Use the correct context that has access to the bloc
                    listenerContext.read<FundingBloc>().add(
                      FundingEvent.fetchFunding(),
                    );
                  },
                ),
              );
            }
          },
          builder: (blocBuilderContext, state) {
            Widget topButtons = Column(
              children: [
                const SizedBox(height: 12),
                Material(
                  color: Colors.transparent,
                  child: GroupedButtonsWidget(
                    groupedButtonsItemList: [
                      GroupedButtonsItem(
                        title: localization.trader_sort,
                        semanticsIdentifier: "grouped_buttons_sort_funding",
                        buttonIcon: trader.Assets.images.sort.svg(),
                        onTap: () {
                          final fundingBloc =
                              blocBuilderContext.read<FundingBloc>();
                          FundingBottomSheet.showSort(
                            context,
                            currentSortOrder: state.sortOrder,
                            onSortOrderChanged: (sortOrder) {
                              fundingBloc.add(
                                FundingEvent.fetchFunding(
                                  sortOrder: sortOrder,
                                  isStartFromPageOne: true,
                                  hasFilters: state.hasFilters,
                                  fromDate: state.fromDate,
                                  toDate: state.toDate,
                                  fundFlow: state.fundFlow,
                                  status: state.status,
                                  transactionType: state.transactionType,
                                ),
                              );
                            },
                          );
                        },
                      ),
                      GroupedButtonsItem(
                        title: localization.trader_filter,
                        semanticsIdentifier: "grouped_buttons_filter_funding",
                        buttonIcon: trader.Assets.images.filter.svg(),
                        isSelected: state.hasFilters,
                        onTap: () {
                          final fundingBloc =
                              blocBuilderContext.read<FundingBloc>();
                          FundingBottomSheet.showFilter(
                            context,
                            initialFromDate: state.fromDate,
                            initialToDate: state.toDate,
                            initialFundFlow: state.fundFlow,
                            initialStatus: state.status,
                            initialTransactionType: state.transactionType,
                            onFiltersApplied: ({
                              DateTime? fromDate,
                              DateTime? toDate,
                              String? fundFlow,
                              String? status,
                              String? transactionType,
                              required bool hasFilters,
                            }) {
                              fundingBloc.add(
                                FundingEvent.fetchFunding(
                                  fromDate: fromDate,
                                  toDate: toDate,
                                  fundFlow: fundFlow,
                                  status: status,
                                  transactionType: transactionType,
                                  sortOrder: state.sortOrder,
                                  isStartFromPageOne: true,
                                  hasFilters: hasFilters,
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            );

            return switch (state.processState) {
              FundingErrorProcessState() => Container(
                color: theme.background.bgPrimary,
              ),
              FundingEmptyFilterResultProcessState() => Container(
                color: theme.background.bgSecondary,
                child: SafeArea(
                  child: Column(
                    children: [
                      topButtons,
                      Expanded(
                        child: Center(
                          child: EmptyOrErrorStateComponent.empty(
                            svgImage:
                                trader.Assets.images.fundingNoResults.svg(),
                            title: localization.trader_noResults,
                            description:
                                localization.trader_noResultsDescription,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _ => Container(
                color: theme.background.bgSecondary,
                child: SafeArea(
                  child: Column(
                    children: [
                      // Show top buttons only when we have data or are loading
                      (state.fundingItems.isNotEmpty &&
                                  state.processState ==
                                      FundingProcessState.success()) ||
                              state.processState ==
                                  FundingProcessState.loading()
                          ? topButtons
                          : const SizedBox(),
                      Expanded(
                        child: PagedView.list(
                          physics: switch (state.processState) {
                            FundingErrorProcessState() =>
                              const NeverScrollableScrollPhysics(),
                            FundingSuccessProcessState() =>
                              state.fundingItems.isEmpty
                                  ? const NeverScrollableScrollPhysics()
                                  : const AlwaysScrollableScrollPhysics(),
                            _ => const AlwaysScrollableScrollPhysics(),
                          },
                          padding: EdgeInsets.zero,
                          itemCount: state.fundingItems.length,
                          centerError: true,
                          centerLoading: true,
                          centerEmpty: true,
                          hasError: false,
                          isLoading: switch (state.processState) {
                            FundingLoadingProcessState() => true,
                            _ => false,
                          },
                          hasReachedMax:
                              state.fundingItemsCount ==
                              state.fundingItems.length,
                          emptyBuilder:
                              (ctx) => EmptyOrErrorStateComponent.empty(
                                svgImage:
                                    trader.Assets.images.fundingEmptyList.svg(),
                                title: localization.trader_noFunding,
                                description:
                                    localization.trader_noFundingDescription,
                                spaceBetweenImageAndTitle: 0,
                              ),
                          loadingBuilder: (ctx) => const FundingLoading(),
                          errorBuilder: (ctx) => const Text(""),
                          separatorBuilder: (ctx, index) => const SizedBox(),
                          onFetchData: () {
                            blocBuilderContext.read<FundingBloc>().add(
                              FundingEvent.fetchFunding(),
                            );
                          },
                          itemBuilder: (ctx, index) {
                            return FundingTile(
                              showDate:
                                  (index == 0 ||
                                      !areDatesOnSameDay(
                                        state.fundingItems
                                            .elementAtOrNull(index)!
                                            .dateTime,
                                        state.fundingItems
                                            .elementAtOrNull(index - 1)!
                                            .dateTime,
                                      )),
                              fundingItem:
                                  state.fundingItems.elementAtOrNull(index)!,
                              onTap: () {
                                DuploSheet.showNonScrollableModalSheet<void>(
                                  context: context,
                                  title: localization.trader_fundingDetails,
                                  content:
                                      (duploSheetContext) => FundingDetails(
                                        fundingItem:
                                            state.fundingItems.elementAtOrNull(
                                              index,
                                            )!,
                                        accountNumber:
                                            state.accountNumber ?? "-",
                                      ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                      Container(height: 15, color: theme.background.bgPrimary),
                    ],
                  ),
                ),
              ),
            };
          },
        ),
      ),
    );
  }

  bool areDatesOnSameDay(DateTime? isoDate1, DateTime? isoDate2) {
    if (isoDate1 == null || isoDate2 == null) {
      return false;
    }
    return isoDate1.year == isoDate2.year &&
        isoDate1.month == isoDate2.month &&
        isoDate1.day == isoDate2.day;
  }
}
