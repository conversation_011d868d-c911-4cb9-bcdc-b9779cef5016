import 'package:clock/clock.dart';
import 'package:e_trader/src/data/api/activity_request_body.dart';
import 'package:e_trader/src/data/api/statements_activity_response.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_statements_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'statements_bloc.freezed.dart';
part 'statements_event.dart';
part 'statements_state.dart';

class StatementsBloc extends Bloc<StatementsEvent, StatementsState> {
  GetSelectedAccountUseCase _getSelectedAccountUseCase;
  GetStatementsUseCase _getStatementsUseCase;
  Clock _clock;

  StatementsBloc(
    this._getSelectedAccountUseCase,
    this._getStatementsUseCase,
    this._clock,
  ) : super(StatementsState()) {
    on<_FetchStatements>((event, emit) => _fetchStatements(event, emit));
  }

  Future<void> _fetchStatements(
    _FetchStatements event,
    Emitter<StatementsState> emit,
  ) async {
    if (event.isStartFromPageOne ?? false) {
      emit(
        state.copyWith(
          processState: StatementsProcessState.loading(),
          statements: [],
          currentPage: 1,
          fromDate: event.fromDate,
          toDate: event.toDate,
          sortOrder: event.sortOrder ?? state.sortOrder,
          statementsCount: 0,
        ),
      );
    } else {
      emit(state.copyWith(processState: StatementsProcessState.loading()));
    }

    String? accountNumber = state.accountNumber;
    String? creationDate = state.creationDate;

    if (accountNumber.isNullOrEmpty || creationDate == null) {
      final client = _getSelectedAccountUseCase();
      accountNumber = client?.accountNumber;

      creationDate = client?.dateCreated;

      emit(
        state.copyWith(
          accountNumber: accountNumber,
          creationDate: creationDate,
        ),
      );
    }

    final DateTime? fromDate = event.fromDate ?? state.fromDate;
    final DateTime? toDate = event.toDate ?? state.toDate;

    final String fromDateStr =
        fromDate == null
            ? DateTime.parse(creationDate!).toIso8601String()
            : fromDate.toIso8601String();
    final String toDateStr =
        toDate == null
            ? _clock.now().toIso8601String()
            : toDate.toIso8601String();

    final body = ActivityRequestBody(
      accountNumber: accountNumber!,
      fromDate: fromDateStr,
      pageNumber: state.currentPage,
      pageSize: 10,
      isAscending:
          event.sortOrder != null
              ? event.sortOrder == SortOrder.ascending
              : state.sortOrder == SortOrder.ascending,
      toDate: toDateStr,
    );

    final result = await _getStatementsUseCase(body).run();
    result.fold(
      (exception) => emit(
        state.copyWith(processState: StatementsProcessState.error(exception)),
      ),
      (statementsResponse) {
        final List<StatementItem> newStatements = List<StatementItem>.of(
          state.statements,
        );

        if (statementsResponse.statements != null) {
          newStatements.addAll(statementsResponse.statements!);
        }
        emit(
          state.copyWith(
            processState: StatementsProcessState.success(),
            statements: newStatements,
            currentPage: state.currentPage + 1,
            creationDate: state.creationDate ?? creationDate,
            accountNumber: state.accountNumber ?? accountNumber,
            statementsCount: statementsResponse.totalCount ?? 0,
            sortOrder: event.sortOrder ?? state.sortOrder,
            fromDate: fromDate,
            toDate: toDate,
          ),
        );
      },
    );
  }
}
