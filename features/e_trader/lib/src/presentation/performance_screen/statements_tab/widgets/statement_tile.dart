import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/statements_activity_response.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as Trader;
import 'package:prelude/prelude.dart';

class StatementTile extends StatelessWidget {
  final bool showDate;
  final StatementItem statement;
  final void Function() onTap;
  const StatementTile({
    super.key,
    this.showDate = false,
    required this.statement,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        showDate
            ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(12),
                  child: DuploText(
                    text: EquitiFormatter.formatDateWithSuffix(
                      statement.dateTime,
                      locale: Localizations.localeOf(context).toString(),
                    ),
                    color: context.duploTheme.text.textTertiary,
                    style: context.duploTextStyles.textXs,
                    fontWeight: DuploFontWeight.medium,
                  ),
                ),
                Divider(
                  color: context.duploTheme.border.borderSecondary,
                  thickness: 1,
                  indent: 16,
                  endIndent: 16,
                ),
              ],
            )
            : const SizedBox(),
        InkWell(
          splashColor: Colors.transparent,
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Trader.Assets.images.statementIcon.svg(
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DuploText(
                          text: localization.trader_daily,
                          color: context.duploTheme.text.textPrimary,
                          style: context.duploTextStyles.textSm,
                          fontWeight: DuploFontWeight.medium,
                        ),
                        DuploText(
                          text: localization.trader_statement,
                          color: context.duploTheme.text.textTertiary,
                          style: context.duploTextStyles.textSm,
                          fontWeight: DuploFontWeight.regular,
                        ),
                      ],
                    ),
                  ],
                ),
                Icon(Icons.chevron_right, size: 20),
              ],
            ),
          ),
        ),
        Divider(color: context.duploTheme.border.borderSecondary, thickness: 1),
        const SizedBox(height: 8),
      ],
    );
  }
}
