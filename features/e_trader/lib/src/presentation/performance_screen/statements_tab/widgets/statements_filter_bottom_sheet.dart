import 'dart:io' show Platform;

import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class StatementsFilterBottomSheet extends StatelessWidget {
  final DateTime? selectedFromDate;
  final DateTime? selectedToDate;
  final void Function(DateTime) onFromDateChanged;
  final void Function(DateTime) onToDateChanged;
  final VoidCallback onClearAll;
  final VoidCallback onApplyFilters;

  const StatementsFilterBottomSheet({
    Key? key,
    required this.selectedFromDate,
    required this.selectedToDate,
    required this.onFromDateChanged,
    required this.onToDateChanged,
    required this.onClearAll,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);

    String formatDate(DateTime? date, EquitiLocalization localization) {
      if (date == null) return localization.trader_datePlaceholder;
      return EquitiFormatter.formatDayMonthYear(date);
    }

    Future<void> showPlatformDatePicker({
      required BuildContext context,
      required DateTime? initialDate,
      required DateTime firstDate,
      required DateTime lastDate,
      required void Function(DateTime) onDateSelected,
    }) async {
      DateTime validInitialDate = initialDate ?? diContainer<Clock>().now();
      if (validInitialDate.isAfter(lastDate)) {
        validInitialDate = lastDate;
      }
      if (validInitialDate.isBefore(firstDate)) {
        validInitialDate = firstDate;
      }

      final isDarkMode = diContainer<ThemeManager>().isDarkMode;

      final darkTheme = ThemeData(
        datePickerTheme: DatePickerThemeData(
          weekdayStyle: TextStyle(color: theme.text.textDisabled),
          todayBorder: BorderSide.none,
        ),
        colorScheme: ColorScheme.dark(
          primary: theme.background.bgBrandPrimary,
          onPrimary: theme.text.textPrimary,
          secondary: theme.text.textSecondaryOnBrand,
          onSecondary: theme.text.textSecondary,
          error: theme.foreground.fgErrorPrimary,
          onError: theme.background.bgSecondary,
          surface: theme.background.bgSecondary,
          onSurface: theme.text.textSecondaryOnBrand,
        ),
      );

      if (Platform.isIOS) {
        // iOS custom calendar dialog
        await showDialog<void>(
          context: context,
          builder: (builderContext) {
            DateTime tempDate = validInitialDate;
            return Theme(
              data:
                  isDarkMode
                      ? darkTheme
                      : Theme.of(context).copyWith(
                        colorScheme: ColorScheme.fromSeed(
                          seedColor: theme.text.textSecondaryOnBrand,
                          primary: theme.text.textSecondaryOnBrand,
                          onPrimary: theme.text.textPrimary,
                        ),
                        datePickerTheme: DatePickerThemeData(
                          weekdayStyle: TextStyle(
                            color: theme.text.textDisabled,
                          ),
                          todayBorder: BorderSide.none,
                        ),
                      ),
              child: AlertDialog(
                content: Container(
                  height: 300,
                  width: double.maxFinite,
                  child: CalendarDatePicker(
                    initialDate: tempDate,
                    firstDate: firstDate,
                    lastDate: lastDate,
                    onDateChanged: (date) {
                      tempDate = date;
                    },
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(builderContext),
                    child: DuploText(
                      text: loc.trader_cancel,
                      style: style.textSm,
                      color: theme.text.textPrimary,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      onDateSelected(tempDate);
                      Navigator.pop(builderContext);
                    },
                    child: DuploText(
                      text: loc.trader_ok,
                      style: style.textSm,
                      color: theme.text.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          },
        );
      } else {
        // Android date picker
        final DateTime? picked = await showDatePicker(
          context: context,
          initialDate: validInitialDate,
          firstDate: firstDate,
          lastDate: lastDate,
          builder:
              (builderContext, child) => Theme(
                data:
                    isDarkMode
                        ? darkTheme
                        : Theme.of(context).copyWith(
                          colorScheme: ColorScheme.fromSeed(
                            seedColor: theme.text.textSecondaryOnBrand,
                            primary: theme.text.textSecondaryOnBrand,
                            onPrimary: theme.text.textPrimary,
                          ),
                          datePickerTheme: DatePickerThemeData(
                            weekdayStyle: TextStyle(
                              color: theme.text.textDisabled,
                            ),
                            todayBorder: BorderSide.none,
                          ),
                        ),
                child: child!,
              ),
        );
        if (picked != null) {
          onDateSelected(picked);
        }
      }
    }

    return Container(
      color: theme.background.bgSecondary,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 26),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: loc.trader_selectCustomDatesDesc,
                    style: style.textSm,
                    textAlign: TextAlign.start,
                    color: theme.foreground.fgSecondary,
                  ),
                  const SizedBox(height: 16),
                  InkWell(
                    onTap: () {
                      showPlatformDatePicker(
                        context: context,
                        initialDate: selectedFromDate,
                        firstDate: DateTime(2000),
                        lastDate: selectedToDate ?? diContainer<Clock>().now(),
                        onDateSelected: onFromDateChanged,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.border.borderSecondary),
                        borderRadius: BorderRadius.circular(8),
                        color: theme.background.bgSecondarySubtle,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: loc.trader_from,
                            style: style.textXs,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DuploText(
                                text: formatDate(selectedFromDate, loc),
                                style: style.textMd,
                                color: theme.text.textPlaceholder,
                              ),
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 20,
                                color: theme.foreground.fgDisabled,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  InkWell(
                    onTap: () {
                      showPlatformDatePicker(
                        context: context,
                        initialDate: selectedToDate,
                        firstDate: selectedFromDate ?? DateTime(2000),
                        lastDate: diContainer<Clock>().now(),
                        onDateSelected: onToDateChanged,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.border.borderSecondary),
                        borderRadius: BorderRadius.circular(8),
                        color: theme.background.bgSecondarySubtle,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: loc.trader_to,
                            style: style.textXs,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DuploText(
                                text: formatDate(selectedToDate, loc),
                                style: style.textMd,
                                color: theme.text.textPlaceholder,
                              ),
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 20,
                                color: theme.foreground.fgDisabled,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: DuploButton.secondary(
                    title: loc.trader_clearAll,
                    onTap: onClearAll,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DuploButton.defaultPrimary(
                    title: loc.trader_applyFilters,
                    onTap: onApplyFilters,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 25),
        ],
      ),
    );
  }
}
