import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class StatementViewer extends StatefulWidget {
  final String url;

  const StatementViewer({super.key, required this.url});

  @override
  State<StatementViewer> createState() => _StatementViewerState();
}

class _StatementViewerState extends State<StatementViewer> {
  bool _hasError = false;
  final toast = DuploToast();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;

    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      body: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.sizeOf(context).height * 0.8,
        ),
        child:
            _hasError
                ? const SizedBox()
                : SfPdfViewer.network(
                  widget.url,
                  pageSpacing: 0,
                  scrollDirection: PdfScrollDirection.vertical,
                  canShowScrollHead: true,
                  canShowScrollStatus: true,
                  interactionMode: PdfInteractionMode.pan,
                  onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                    setState(() {
                      _hasError = true;
                      toast.showToastMessage(
                        autoCloseDuration: const Duration(seconds: 3),
                        context: context,
                        widget: DuploToastMessage(
                          titleMessage: localization.trader_failedToLoad,
                          descriptionMessage:
                              localization.trader_failedToLoadDescription,
                          messageType: ToastMessageType.error,
                          onLeadingAction: () => toast.hidesToastMessage(),
                          actionButtonTitle:
                              EquitiLocalization.of(context).trader_reload,
                          onTap: () {
                            setState(() {
                              toast.hidesToastMessage();

                              _hasError = false;
                            });
                          },
                        ),
                      );
                    });
                  },
                ),
      ),
    );
  }
}
