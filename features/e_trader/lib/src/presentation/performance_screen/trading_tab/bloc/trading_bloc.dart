import 'package:clock/clock.dart';
import 'package:e_trader/src/data/api/deal_by_id_request_model.dart';
import 'package:e_trader/src/data/api/deal_by_id_response_model.dart';
import 'package:e_trader/src/data/api/trading_request_model.dart';
import 'package:e_trader/src/data/api/trading_response_model.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/usecase/deal_by_id_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/trading_activity_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'trading_event.dart';
part 'trading_state.dart';
part 'trading_bloc.freezed.dart';

class TradingBloc extends Bloc<TradingEvent, TradingState> {
  final Clock _clock;
  final DealByIdUseCase _dealByIdUseCase;
  final TradingActivityUseCase _tradingActivityUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;

  TradingBloc(
    this._clock,
    this._dealByIdUseCase,
    this._tradingActivityUseCase,
    this._getSelectedAccountUseCase,
  ) : super(TradingState()) {
    on<_GetTrades>(_getTrades);
    on<_GetTradeReceipt>(_getTradeReceipt);
  }
  Future<void> _getTrades(_GetTrades event, Emitter<TradingState> emit) async {
    final bool hasFilters = event.hasFilters ?? state.hasFilters;

    if (event.isStartFromPageOne ?? false) {
      emit(
        state.copyWith(
          trades: [],
          currentPage: 1,
          fromDate: event.fromDate,
          toDate: event.toDate,
          tradeType: event.tradeType,
          hasFilters: hasFilters,
          tradingProcessState: TradingProcessState.loading(),
          isSortSelected: event.isSortingSelected ?? state.isSortSelected,
          sortOrder: event.sortingOrder ?? state.sortOrder,
        ),
      );
    }

    String? accountNumber = state.accountNumber;
    String? creationDate = state.creationDate;

    if ((accountNumber?.isEmpty ?? true) || creationDate == null) {
      final client = _getSelectedAccountUseCase();
      accountNumber = client?.accountNumber;
      creationDate = client?.dateCreated;

      if ((accountNumber?.isEmpty ?? true) || creationDate == null) {
        if (!isClosed)
          emit(
            state.copyWith(
              tradingProcessState: TradingProcessState.error(
                (state.errorCounter ?? 0) + 1,
              ),
            ),
          );
        return;
      }
      if (!isClosed)
        emit(
          state.copyWith(
            accountNumber: accountNumber,
            creationDate: creationDate,
            accountCurrency: client?.homeCurrency,
          ),
        );
    }

    final DateTime? fromDate = event.fromDate ?? state.fromDate;
    final DateTime? toDate = event.toDate ?? state.toDate;

    final String fromDateStr;
    if (fromDate == null) {
      fromDateStr = DateTime.parse(creationDate).toIso8601String();
    } else {
      final DateTime startOfDay = DateTime(
        fromDate.year,
        fromDate.month,
        fromDate.day,
      );
      fromDateStr = startOfDay.toIso8601String();
    }

    final String toDateStr;
    if (toDate == null) {
      toDateStr = _clock.now().toIso8601String();
    } else {
      final DateTime endOfDay = DateTime(
        toDate.year,
        toDate.month,
        toDate.day,
        23,
        59,
        59,
        999,
      );
      toDateStr = endOfDay.toIso8601String();
    }

    final body = TradingRequestModel(
      accountNumber: accountNumber!,
      dealSortBy: 'Date',
      fromDate: fromDateStr,
      toDate: toDateStr,
      pageNumber: state.currentPage,
      pageSize: 10,
      sortingOrder:
          (event.sortingOrder != null
                  ? event.sortingOrder == SortOrder.ascending
                  : state.sortOrder == SortOrder.ascending)
              ? "Ascending"
              : "Descending",
      tradeType: event.tradeType ?? state.tradeType,
    );
    final result = await _tradingActivityUseCase(body).run();

    result.fold(
      (error) {
        if (!isClosed)
          emit(
            state.copyWith(
              tradingProcessState: TradingProcessState.error(
                (state.errorCounter ?? 0) + 1,
              ),
              errorCounter: (state.errorCounter ?? 0) + 1,
            ),
          );
      },
      (trades) {
        if (!isClosed) {
          final List<TradingListModel> newTradingItems =
              List<TradingListModel>.of(state.trades);

          if (trades.list.isNotEmpty) {
            final closedTrades =
                trades.list
                    .where((trade) => trade.activityType == 'CloseTrade')
                    .toList();
            newTradingItems.addAll(closedTrades);
          }

          final bool emptyResults =
              newTradingItems.isEmpty &&
              state.hasFilters &&
              state.currentPage == 1;

          final processState =
              emptyResults
                  ? TradingProcessState.emptyFilterResult()
                  : TradingProcessState.success();
          emit(
            state.copyWith(
              tradingProcessState: processState,
              trades: newTradingItems,
              currentPage: state.currentPage + 1,
              creationDate: state.creationDate ?? creationDate,
              accountNumber: state.accountNumber ?? accountNumber,
              totalCount: trades.totalCount,
              sortOrder: event.sortingOrder ?? state.sortOrder,
              fromDate: fromDate,
              toDate: toDate,
              tradeType: event.tradeType ?? state.tradeType,
              hasFilters: hasFilters,
              isSortSelected: event.isSortingSelected ?? state.isSortSelected,
              errorCounter: 0,
            ),
          );
        }
      },
    );
  }

  Future<void> _getTradeReceipt(
    _GetTradeReceipt event,
    Emitter<TradingState> emit,
  ) async {
    emit(state.copyWith(tradingProcessState: TradingProcessState.loading()));
    String? accountNumber = state.accountNumber;

    if (accountNumber?.isEmpty ?? true) {
      final client = _getSelectedAccountUseCase();
      accountNumber = client?.accountNumber;

      if (accountNumber?.isEmpty ?? true) {
        if (!isClosed)
          emit(
            state.copyWith(
              tradingProcessState: TradingProcessState.error(
                (state.errorCounter ?? 0) + 1,
              ),
              errorCounter: (state.errorCounter ?? 0) + 1,
            ),
          );
        return;
      }
      if (!isClosed)
        emit(
          state.copyWith(
            accountNumber: state.accountNumber ?? accountNumber,
            errorCounter: 0,
          ),
        );
    }
    final result =
        await _dealByIdUseCase(
          DealByIdRequestModel(
            accountNumber: state.accountNumber ?? accountNumber!,
            dealId: event.dealId ?? state.tradeReceiptDetails?.id ?? 0,
          ),
        ).run();

    result.fold(
      (error) {
        if (!isClosed)
          emit(
            state.copyWith(
              tradingProcessState: TradingProcessState.error(
                (state.errorCounter ?? 0) + 1,
              ),
              errorCounter: (state.errorCounter ?? 0) + 1,
            ),
          );
      },
      (tradeReceiptDetails) {
        if (!isClosed)
          emit(
            state.copyWith(
              tradeReceiptDetails: tradeReceiptDetails,
              tradingProcessState: TradingProcessState.success(),
              errorCounter: 0,
            ),
          );
      },
    );
  }
}
