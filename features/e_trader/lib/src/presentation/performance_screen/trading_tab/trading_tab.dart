import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/bloc/trading_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/closed_trade_details.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_bottom_sheet.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_tab_shimmer.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:prelude/prelude.dart';

class TradingTab extends StatelessWidget {
  const TradingTab({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create: (_) => diContainer<TradingBloc>()..add(TradingEvent.getTrades()),
      child: BlocConsumer<TradingBloc, TradingState>(
        listener: (BuildContext listenerContext, TradingState state) {
          if (state.tradingProcessState is TradingProcessStateError) {
            final toast = DuploToast();
            toast.hidesToastMessage();
            toast.showToastMessage(
              autoCloseDuration: const Duration(seconds: 3),
              context: listenerContext,
              widget: DuploToastMessage(
                titleMessage:
                    state.errorCounter == 1
                        ? localization.trader_failedToLoad
                        : localization.trader_contactSupportTeam,
                descriptionMessage:
                    state.errorCounter == 1
                        ? localization.trader_failedToLoadDescription
                        : localization.trader_contactSupportDescription,
                messageType: ToastMessageType.error,
                onLeadingAction: () => toast.hidesToastMessage(),
                actionButtonTitle:
                    state.errorCounter == 1
                        ? localization.trader_reload
                        : localization.trader_raiseSupportTicket,
                onTap: () {
                  toast.hidesToastMessage();
                  listenerContext.read<TradingBloc>().add(
                    TradingEvent.getTrades(),
                  );
                },
              ),
            );
          }
        },
        buildWhen: (previous, current) => previous != current,
        listenWhen:
            (previous, current) =>
                previous.tradingProcessState != current.tradingProcessState,
        builder:
            (builderContext, state) => switch (state.tradingProcessState) {
              TradingProcessStateLoading() => const TradingTabShimmer(),
              TradingProcessStateError() => Container(
                color: theme.background.bgPrimary,
              ),
              TradingProcessStateEmptyFilterResult() => SafeArea(
                child: Container(
                  color: theme.background.bgSecondary,
                  child: Column(
                    children: [
                      Container(
                        color: Colors.transparent,
                        margin: EdgeInsetsDirectional.symmetric(vertical: 14),
                        child: GroupedButtonsWidget(
                          margin: 16,
                          groupedButtonsItemList: [
                            GroupedButtonsItem(
                              semanticsIdentifier: "grouped_buttons_sort",
                              title: localization.trader_sort,
                              isSelected: state.isSortSelected,
                              buttonIcon: trader.Assets.images.sort.svg(),
                              onTap: () {
                                final tradingBloc =
                                    builderContext.read<TradingBloc>();
                                TradingBottomSheet.showSort(
                                  context,
                                  currentSortOrder: state.sortOrder,
                                  onSortOrderChanged: (sortOrder) {
                                    tradingBloc.add(
                                      TradingEvent.getTrades(
                                        sortingOrder: sortOrder,
                                        isSortingSelected: true,
                                        isStartFromPageOne: true,
                                        tradeType:
                                            state.tradeType?.isNotEmpty == true
                                                ? state.tradeType
                                                : null,
                                        fromDate: state.fromDate,
                                        toDate: state.toDate,
                                        hasFilters: state.hasFilters,
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                            GroupedButtonsItem(
                              semanticsIdentifier: "grouped_buttons_filter",
                              title: localization.trader_filter,
                              buttonIcon: trader.Assets.images.filter.svg(),
                              isSelected: state.hasFilters,
                              onTap: () {
                                final tradingBloc =
                                    builderContext.read<TradingBloc>();
                                TradingBottomSheet.showFilter(
                                  context,
                                  initialFromDate: state.fromDate,
                                  initialToDate: state.toDate,
                                  initialTradeType: state.tradeType,
                                  onFiltersApplied: ({
                                    DateTime? fromDate,
                                    DateTime? toDate,
                                    String? tradeType,
                                    required bool hasFilters,
                                  }) {
                                    tradingBloc.add(
                                      TradingEvent.getTrades(
                                        fromDate: fromDate,
                                        toDate: toDate,
                                        sortingOrder: state.sortOrder,
                                        tradeType: tradeType,
                                        isStartFromPageOne: true,
                                        hasFilters: hasFilters,
                                        isSortingSelected: state.isSortSelected,
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: EmptyOrErrorStateComponent.empty(
                          svgImage: trader.Assets.images.tradingNoResults.svg(),
                          title: localization.trader_noResults,
                          description: localization.trader_noResultsDescription,
                          spaceBetweenImageAndTitle: 0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _ => Container(
                color: theme.background.bgPrimary,
                child: SafeArea(
                  child: Column(
                    children: [
                      if (state.trades.isNotEmpty &&
                          state.tradingProcessState !=
                              TradingProcessState.error(
                                state.errorCounter ?? 0,
                              ) &&
                          state.tradingProcessState !=
                              TradingProcessState.loading())
                        Container(
                          color: Colors.transparent,
                          margin: EdgeInsetsDirectional.only(
                            top: 14,
                            bottom: 8,
                          ),
                          child: GroupedButtonsWidget(
                            margin: 16,
                            groupedButtonsItemList: [
                              GroupedButtonsItem(
                                semanticsIdentifier: "grouped_buttons_sort",
                                title: localization.trader_sort,
                                isSelected: state.isSortSelected,
                                buttonIcon: trader.Assets.images.sort.svg(),
                                onTap: () {
                                  final tradingBloc =
                                      builderContext.read<TradingBloc>();
                                  TradingBottomSheet.showSort(
                                    context,
                                    currentSortOrder: state.sortOrder,
                                    onSortOrderChanged: (sortOrder) {
                                      if (!tradingBloc.isClosed) {
                                        tradingBloc.add(
                                          TradingEvent.getTrades(
                                            sortingOrder: sortOrder,
                                            isSortingSelected: true,
                                            isStartFromPageOne: true,
                                            tradeType:
                                                state.tradeType?.isNotEmpty ==
                                                        true
                                                    ? state.tradeType
                                                    : null,
                                            fromDate: state.fromDate,
                                            toDate: state.toDate,
                                            hasFilters: state.hasFilters,
                                          ),
                                        );
                                      }
                                    },
                                  );
                                },
                              ),
                              GroupedButtonsItem(
                                semanticsIdentifier: "grouped_buttons_filter",
                                isSelected: state.hasFilters,
                                title: localization.trader_filter,
                                buttonIcon: trader.Assets.images.filter.svg(),
                                onTap: () {
                                  final tradingBloc =
                                      builderContext.read<TradingBloc>();
                                  TradingBottomSheet.showFilter(
                                    context,
                                    initialFromDate: state.fromDate,
                                    initialToDate: state.toDate,
                                    initialTradeType: state.tradeType,
                                    onFiltersApplied: ({
                                      DateTime? fromDate,
                                      DateTime? toDate,
                                      String? tradeType,
                                      required bool hasFilters,
                                    }) {
                                      if (!tradingBloc.isClosed) {
                                        tradingBloc.add(
                                          TradingEvent.getTrades(
                                            fromDate: fromDate,
                                            toDate: toDate,
                                            sortingOrder: state.sortOrder,
                                            tradeType: tradeType,
                                            isStartFromPageOne: true,
                                            hasFilters: hasFilters,
                                            isSortingSelected:
                                                state.isSortSelected,
                                          ),
                                        );
                                      }
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      Expanded(
                        child: PagedView.list(
                          centerEmpty: true,
                          centerLoading: true,
                          padding: EdgeInsets.zero,
                          itemCount: state.trades.length,
                          errorBuilder: (ctx) => const SizedBox(),
                          loadingBuilder: (ctx) => const SizedBox(),
                          separatorBuilder: (ctx, index) => const SizedBox(),
                          hasReachedMax:
                              state.totalCount == state.trades.length,
                          isLoading: switch (state.tradingProcessState) {
                            TradingProcessStateLoading() => true,
                            _ => false,
                          },
                          emptyBuilder:
                              (ctx) => EmptyOrErrorStateComponent.empty(
                                svgImage:
                                    trader.Assets.images.emptyTradings.svg(),
                                title: localization.trader_noTrading,
                                description:
                                    localization.trader_noTradingDescription,
                                spaceBetweenImageAndTitle: 0,
                              ),
                          physics: switch (state.tradingProcessState) {
                            TradingProcessStateEmpty() ||
                            TradingProcessStateEmptyFilterResult() =>
                              const NeverScrollableScrollPhysics(),
                            _ => const AlwaysScrollableScrollPhysics(),
                          },
                          onFetchData:
                              () => builderContext.read<TradingBloc>().add(
                                TradingEvent.getTrades(),
                              ),
                          itemBuilder: (itemBuilderContext, index) {
                            final currentTrade =
                                state.trades.elementAtOrNull(index)!;
                            final previousTrade =
                                index > 0
                                    ? state.trades.elementAtOrNull(index - 1)
                                    : null;
                            final bool showDateHeader =
                                index == 0 ||
                                !areDatesOnSameDay(
                                  currentTrade.dateTime,
                                  previousTrade?.dateTime,
                                );
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (showDateHeader)
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 12,
                                      horizontal: 16,
                                    ),
                                    child: DuploText(
                                      text:
                                          EquitiFormatter.formatDateWithSuffix(
                                            currentTrade.dateTime,
                                            locale:
                                                Localizations.localeOf(
                                                  context,
                                                ).toString(),
                                          ),
                                      color:
                                          itemBuilderContext
                                              .duploTheme
                                              .text
                                              .textTertiary,
                                      style:
                                          itemBuilderContext
                                              .duploTextStyles
                                              .textXs,
                                      fontWeight: DuploFontWeight.medium,
                                    ),
                                  ),
                                DuploTap(
                                  useMaterial: true,
                                  onTap:
                                      () =>
                                          DuploSheet.showNonScrollableModalSheet<
                                            void
                                          >(
                                            context: itemBuilderContext,
                                            title:
                                                localization
                                                    .trader_closedTradeDetails,
                                            content:
                                                (duploSheetContext) =>
                                                    ClosedTradeDetails(
                                                      tradingItem: currentTrade,
                                                    ),
                                          ),
                                  child: TradingTile(
                                    tradingItem: currentTrade,
                                    accountCurrency:
                                        state.accountCurrency ?? "",
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 15),
                    ],
                  ),
                ),
              ),
            },
      ),
    );
  }

  bool areDatesOnSameDay(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) {
      return false;
    }
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
