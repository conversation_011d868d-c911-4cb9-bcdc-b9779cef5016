import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_filter_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class TradingBottomSheet {
  static void showSort(
    BuildContext context, {
    required SortOrder currentSortOrder,
    required void Function(SortOrder) onSortOrderChanged,
  }) {
    final loc = EquitiLocalization.of(context);
    final allSortOptions = <SelectionOptionModel>[];

    SelectionOptionModel selected = SelectionOptionModel(
      displayText: SortOrderOptions.defaultOption.displayAsNewestToOldest(loc),
      identifier: SortOrderOptions.defaultOption.indentifier(),
    );

    for (final sortOrder in SortOrder.values.toList().reversed) {
      final model = SelectionOptionModel(
        displayText: sortOrder.displayAsNewestToOldest(loc),
        identifier: sortOrder.indentifier(),
      );
      if (model.identifier == currentSortOrder.indentifier()) {
        selected = model;
      }
      allSortOptions.add(model);
    }

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: loc.trader_sortTradesBy,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          Builder(
            builder:
                (builderContext) => IconButton(
                  icon: Assets.images.closeIc.svg(),
                  onPressed: () => Navigator.pop(builderContext),
                ),
          ),
        ],
      ),
      content: Builder(
        builder:
            (modalContext) => TextSelectionComponentScreen(
              buttonTitle: loc.trader_sortTrades,
              options: allSortOptions,
              pageTitle: loc.trader_sortTradesBy,
              selected: selected,
              onSelection: (selectedOption) {
                final order = selectedOption.identifier.toSortOrder();
                if (order != currentSortOrder) {
                  onSortOrderChanged(order);
                }
                Navigator.pop(modalContext);
              },
            ),
      ),
    );
  }

  static void showFilter(
    BuildContext context, {
    required DateTime? initialFromDate,
    required DateTime? initialToDate,
    required String? initialTradeType,
    required void Function({
      DateTime? fromDate,
      DateTime? toDate,
      String? tradeType,
      required bool hasFilters,
    })
    onFiltersApplied,
  }) {
    final loc = EquitiLocalization.of(context);

    final initialFilterData = TradingFilterData(
      fromDate: initialFromDate,
      toDate: initialToDate,
      tradeType: initialTradeType,
    );

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: loc.trader_filterTradesBy,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          Builder(
            builder:
                (builderContext) => IconButton(
                  icon: Assets.images.closeIc.svg(),
                  onPressed: () => Navigator.pop(builderContext),
                ),
          ),
        ],
      ),
      content: TradingFilterBottomSheet(
        initialFilterData: initialFilterData,
        onApplyFilters: (updatedFilterData) {
          final hasFilters =
              updatedFilterData.fromDate != null ||
              updatedFilterData.toDate != null ||
              updatedFilterData.tradeType?.isNotEmpty == true;

          onFiltersApplied(
            fromDate: updatedFilterData.fromDate,
            toDate: updatedFilterData.toDate,
            tradeType: updatedFilterData.tradeType,
            hasFilters: hasFilters,
          );
        },
      ),
    );
  }
}
