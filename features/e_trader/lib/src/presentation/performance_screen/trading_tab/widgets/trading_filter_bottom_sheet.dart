import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class TradingFilterBottomSheet extends StatefulWidget {
  final TradingFilterData initialFilterData;
  final void Function(TradingFilterData) onApplyFilters;

  const TradingFilterBottomSheet({
    Key? key,
    required this.initialFilterData,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  State<TradingFilterBottomSheet> createState() =>
      _TradingFilterBottomSheetState();
}

class _TradingFilterBottomSheetState extends State<TradingFilterBottomSheet> {
  late TradingFilterData _filterData;
  SelectionItem? _selectedTradeType;

  @override
  void initState() {
    super.initState();
    _filterData = widget.initialFilterData;
    _initializeSelectedItems();
  }

  void _initializeSelectedItems() {
    if (_filterData.tradeType != null) {
      final matches =
          [
            SelectionItem(id: 'Sell', displayName: 'Sell'),
            SelectionItem(id: 'Buy', displayName: 'Buy'),
          ].where((item) => item.id == _filterData.tradeType).toList();
      if (matches.isNotEmpty) {
        _selectedTradeType = matches.firstOrNull;
      }
    }
  }

  String _formatDate(DateTime? date, EquitiLocalization localization) {
    if (date == null) return localization.trader_datePlaceholder;
    return EquitiFormatter.formatFromToDayMonthYear(
      date,
      locale: Localizations.localeOf(context).toString(),
    );
  }

  Future<void> _showPlatformDatePicker({
    required DuploThemeData theme,
    required EquitiLocalization loc,
    required BuildContext context,
    required DateTime? initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    required TextStyles style,
    required void Function(DateTime) onDateSelected,
  }) async => await showDialog<void>(
    context: context,
    builder: (builderContext) {
      DateTime tempDate = initialDate ?? DateTime.now();
      if (tempDate.isAfter(lastDate)) {
        tempDate = lastDate;
      }
      if (tempDate.isBefore(firstDate)) {
        tempDate = firstDate;
      }

      final isDarkMode = diContainer<ThemeManager>().isDarkMode;

      final darkTheme = ThemeData(
        datePickerTheme: DatePickerThemeData(
          weekdayStyle: TextStyle(color: theme.text.textDisabled),
          todayBorder: BorderSide.none,
        ),
        colorScheme: ColorScheme.dark(
          primary: theme.background.bgBrandPrimary,
          onPrimary: theme.text.textPrimary,
          secondary: theme.text.textSecondaryOnBrand,
          onSecondary: theme.text.textSecondary,
          error: theme.foreground.fgErrorPrimary,
          onError: theme.background.bgSecondary,
          surface: theme.background.bgSecondary,
          onSurface: theme.text.textSecondaryOnBrand,
        ),
      );

      return Theme(
        data:
            isDarkMode
                ? darkTheme
                : Theme.of(context).copyWith(
                  colorScheme: ColorScheme.fromSeed(
                    seedColor: theme.text.textSecondaryOnBrand,
                    primary: theme.text.textSecondaryOnBrand,
                    onPrimary: theme.text.textPrimary,
                  ),
                  datePickerTheme: DatePickerThemeData(
                    weekdayStyle: TextStyle(color: theme.text.textDisabled),
                    todayBorder: BorderSide.none,
                  ),
                ),
        child: AlertDialog(
          content: SizedBox(
            height: 300,
            width: double.maxFinite,
            child: CalendarDatePicker(
              initialDate: tempDate,
              firstDate: firstDate,
              lastDate: lastDate,
              onDateChanged: (date) => tempDate = date,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(builderContext),
              child: DuploText(
                text: loc.trader_cancel,
                style: style.textSm,
                color: theme.text.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () {
                onDateSelected(tempDate);
                Navigator.pop(builderContext);
              },
              child: DuploText(
                text: loc.trader_ok,
                style: style.textSm,
                color: theme.text.textPrimary,
              ),
            ),
          ],
        ),
      );
    },
  );

  void _clearAll() {
    setState(() {
      _filterData = TradingFilterData.empty();
      _selectedTradeType = null;
    });
    widget.onApplyFilters(TradingFilterData());
  }

  void _applyFilters() {
    final updatedFilterData = TradingFilterData(
      fromDate: _filterData.fromDate,
      toDate: _filterData.toDate,
      tradeType: _selectedTradeType?.id,
    );

    widget.onApplyFilters(updatedFilterData);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);

    return Container(
      color: theme.background.bgSecondary,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 26),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: loc.trader_selectCustomDatesTradingDesc,
                    style: style.textSm,
                    textAlign: TextAlign.start,
                    color: theme.foreground.fgSecondary,
                  ),
                  const SizedBox(height: 16),
                  InkWell(
                    onTap:
                        () => _showPlatformDatePicker(
                          theme: theme,
                          style: style,
                          loc: loc,
                          context: context,
                          initialDate: _filterData.fromDate,
                          firstDate: DateTime(2000),
                          lastDate: _filterData.toDate ?? DateTime.now(),
                          onDateSelected: (date) {
                            setState(() {
                              _filterData = _filterData.copyWith(
                                fromDate: date,
                              );
                              if (_filterData.toDate != null &&
                                  date.isAfter(_filterData.toDate!)) {
                                _filterData = _filterData.copyWith(
                                  clearToDate: true,
                                );
                              }
                            });
                          },
                        ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.border.borderSecondary),
                        borderRadius: BorderRadius.circular(8),
                        color: theme.background.bgSecondarySubtle,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: loc.trader_from,
                            style: style.textXs,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DuploText(
                                text: _formatDate(_filterData.fromDate, loc),
                                style: style.textMd,
                                color: theme.text.textPlaceholder,
                              ),
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 20,
                                color: theme.foreground.fgDisabled,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  InkWell(
                    onTap:
                        () => _showPlatformDatePicker(
                          theme: theme,
                          style: style,
                          loc: loc,
                          context: context,
                          initialDate: _filterData.toDate,
                          firstDate: _filterData.fromDate ?? DateTime(2000),
                          lastDate: DateTime.now(),
                          onDateSelected: (date) {
                            setState(() {
                              _filterData = _filterData.copyWith(toDate: date);
                            });
                          },
                        ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.border.borderSecondary),
                        borderRadius: BorderRadius.circular(8),
                        color: theme.background.bgSecondarySubtle,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: loc.trader_to,
                            style: style.textXs,
                            color: theme.text.textSecondary,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DuploText(
                                text: _formatDate(_filterData.toDate, loc),
                                style: style.textMd,
                                color: theme.text.textPlaceholder,
                              ),
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 20,
                                color: theme.foreground.fgDisabled,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Divider(color: theme.border.borderSecondary, thickness: 1),
                  const SizedBox(height: 24),
                  DuploWrappedSelectionList(
                    title: loc.trader_tradeType,
                    items: [
                      SelectionItem(id: 'Sell', displayName: loc.trader_sell),
                      SelectionItem(id: 'Buy', displayName: loc.trader_buy),
                    ],
                    selectedItem: _selectedTradeType,
                    onItemSelected: (item) {
                      setState(() {
                        _selectedTradeType = item;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: DuploButton.secondary(
                    title: loc.trader_clearAll,
                    onTap: _clearAll,
                    isDisabled:
                        _filterData.fromDate == null &&
                        _filterData.toDate == null &&
                        _filterData.tradeType == null &&
                        _selectedTradeType == null,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 3,
                  child: DuploButton.defaultPrimary(
                    title: loc.trader_applyFilters,
                    onTap: _applyFilters,
                    isDisabled:
                        _filterData.fromDate == null &&
                        _filterData.toDate == null &&
                        _filterData.tradeType == null &&
                        _selectedTradeType == null,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 25),
        ],
      ),
    );
  }
}

class TradingFilterData {
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? tradeType;

  const TradingFilterData({this.fromDate, this.toDate, this.tradeType});

  TradingFilterData copyWith({
    DateTime? fromDate,
    DateTime? toDate,
    String? tradeType,
    bool clearFromDate = false,
    bool clearToDate = false,
    bool clearTradeType = false,
  }) => TradingFilterData(
    fromDate: clearFromDate ? null : (fromDate ?? this.fromDate),
    toDate: clearToDate ? null : (toDate ?? this.toDate),
    tradeType: clearTradeType ? null : (tradeType ?? this.tradeType),
  );

  static TradingFilterData empty() => const TradingFilterData();
}
