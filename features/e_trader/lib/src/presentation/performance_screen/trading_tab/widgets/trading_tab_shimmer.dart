import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

class TradingTabShimmer extends StatelessWidget {
  const TradingTabShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return Container(
      color: theme.background.bgPrimary,
      child: Column(
        children: [
          Container(
            color: Colors.transparent,
            margin: EdgeInsetsDirectional.symmetric(vertical: 14),
            child: GroupedButtonsWidget(
              margin: 16,
              groupedButtonsItemList: [
                GroupedButtonsItem(
                  title: localization.trader_sort,
                  buttonIcon: trader.Assets.images.sort.svg(),
                  onTap: () => {},
                ),
                GroupedButtonsItem(
                  title: localization.trader_filter,
                  buttonIcon: trader.Assets.images.filter.svg(),
                  onTap: () => {},
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.separated(
              shrinkWrap: true,
              itemBuilder:
                  (builderContext, index) => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploShimmer(
                        child: Container(
                          margin: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          decoration: BoxDecoration(
                            color: theme.background.bgPrimary,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          height: 18,
                          width: 183,
                        ),
                      ),
                      DuploShimmer(child: Divider()),
                      Container(
                        margin: EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                DuploShimmer(
                                  child: Container(
                                    height: 24,
                                    width: 24,
                                    margin: const EdgeInsetsDirectional.only(
                                      end: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color: theme.background.bgPrimary,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                                Column(
                                  children: [
                                    DuploShimmer(
                                      child: Container(
                                        height: 16,
                                        width: 100,
                                        decoration: BoxDecoration(
                                          color: theme.background.bgPrimary,
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 6),
                                    DuploShimmer(
                                      child: Container(
                                        height: 16,
                                        width: 100,
                                        decoration: BoxDecoration(
                                          color: theme.background.bgPrimary,
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                DuploShimmer(
                                  child: Container(
                                    height: 16,
                                    width: 100,
                                    decoration: BoxDecoration(
                                      color: theme.background.bgPrimary,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 6),
                                DuploShimmer(
                                  child: Container(
                                    height: 16,
                                    width: 100,
                                    decoration: BoxDecoration(
                                      color: theme.background.bgPrimary,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      DuploShimmer(child: Divider()),
                      Container(
                        margin: EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                DuploShimmer(
                                  child: Container(
                                    height: 24,
                                    width: 24,
                                    margin: const EdgeInsetsDirectional.only(
                                      end: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color: theme.background.bgPrimary,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                                Column(
                                  children: [
                                    DuploShimmer(
                                      child: Container(
                                        height: 16,
                                        width: 100,
                                        decoration: BoxDecoration(
                                          color: theme.background.bgPrimary,
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 6),
                                    DuploShimmer(
                                      child: Container(
                                        height: 16,
                                        width: 100,
                                        decoration: BoxDecoration(
                                          color: theme.background.bgPrimary,
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                DuploShimmer(
                                  child: Container(
                                    height: 16,
                                    width: 100,
                                    decoration: BoxDecoration(
                                      color: theme.background.bgPrimary,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 6),
                                DuploShimmer(
                                  child: Container(
                                    height: 16,
                                    width: 100,
                                    decoration: BoxDecoration(
                                      color: theme.background.bgPrimary,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
              separatorBuilder:
                  (separatorContext, index) => DuploShimmer(child: Divider()),
              itemCount: 3,
            ),
          ),
        ],
      ),
    );
  }
}
