import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_response_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class TradingTile extends StatelessWidget {
  final TradingListModel tradingItem;
  final String accountCurrency;
  const TradingTile({
    super.key,
    required this.tradingItem,
    required this.accountCurrency,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.background.bgPrimary,
        border: BorderDirectional(
          top: BorderSide(color: theme.border.borderSecondary),
          start: BorderSide(
            width: 4,
            color:
                tradingItem.tradeDetail.tradeType == "Buy"
                    ? theme.utility.utilitySuccess700
                    : theme.foreground.fgErrorPrimary,
          ),
          end: BorderSide(color: theme.border.borderSecondary),
          bottom: BorderSide(color: theme.border.borderSecondary),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsetsDirectional.only(
                top: 16,
                bottom: 16,
                start: 10,
              ),
              child: Row(
                children: [
                  DuploCachedNetworkImage(
                    imageUrl: tradingItem.tradeDetail.logoUrl,
                    imageWidth: 35,
                    imageHeight: 35,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        DuploText(
                          text: tradingItem.tradeDetail.friendlyName,
                          color: theme.text.textPrimary,
                          style: style.textSm,
                          fontWeight: DuploFontWeight.medium,
                        ),
                        const SizedBox(height: 2),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6),
                          height: 22,
                          child: DuploText(
                            text:
                                "${tradingItem.tradeDetail.tradeType == "Buy" ? localization.trader_buy : localization.trader_sell} ${EquitiFormatter.formatNumber(value: tradingItem.tradeDetail.volume / 10000, locale: Localizations.localeOf(context).toString())} ${EquitiLocalization.of(context).trader_lots}",
                            style: context.duploTextStyles.textXs,
                            color:
                                tradingItem.tradeDetail.tradeType == "Buy"
                                    ? theme.utility.utilitySuccess700
                                    : theme.utility.utilityError700,
                            fontWeight: DuploFontWeight.medium,
                            maxLines: 1,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              width: 2,
                              color:
                                  tradingItem.tradeDetail.tradeType == "Buy"
                                      ? theme.utility.utilitySuccess100
                                      : theme.utility.utilityError100,
                            ),
                            color:
                                tradingItem.tradeDetail.tradeType == "Buy"
                                    ? theme.utility.utilitySuccess50
                                    : theme.utility.utilityError50,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsetsDirectional.only(
              top: 14,
              bottom: 14,
              end: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    if (tradingItem.tradeDetail.profit >= 0 &&
                        Localizations.localeOf(context).toString() != 'ar')
                      DuploText(
                        text: '+',
                        style: style.textSm,
                        fontWeight: DuploFontWeight.bold,
                        color: theme.text.textSuccessPrimary,
                      ),
                    DuploText(
                      text: EquitiFormatter.decimalPatternDigits(
                        locale: Localizations.localeOf(context).toString(),
                        value: tradingItem.tradeDetail.profit,
                        digits: 2,
                      ),
                      style: style.textSm,
                      fontWeight: DuploFontWeight.bold,
                      color:
                          tradingItem.tradeDetail.profit >= 0
                              ? theme.text.textSuccessPrimary
                              : theme.text.textErrorPrimary,
                    ),
                    if (tradingItem.tradeDetail.profit >= 0 &&
                        Localizations.localeOf(context).toString() == 'ar')
                      DuploText(
                        text: '+',
                        style: style.textSm,
                        fontWeight: DuploFontWeight.bold,
                        color: theme.text.textSuccessPrimary,
                      ),
                    SizedBox(width: 5),
                    DuploText(
                      text: accountCurrency,
                      style: style.textXs,
                      color:
                          tradingItem.tradeDetail.profit >= 0
                              ? theme.text.textSuccessPrimary
                              : theme.text.textErrorPrimary,
                    ),
                  ],
                ),
                DuploText(
                  text: tradingItem.activityTypeName,
                  style: style.textSm,
                  maxLines: 1,
                  color: theme.text.textSecondary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
