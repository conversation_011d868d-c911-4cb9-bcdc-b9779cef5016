import 'package:e_trader/src/presentation/model/pip_information_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'margin_information_model.freezed.dart';

@unfreezed
abstract class MarginInformationModel with _$MarginInformationModel {
  const MarginInformationModel._();
  factory MarginInformationModel({
    required double marginFree,
    required double requiredMargin,
    required double maxLot,
    required double minLot,
    required double requiredMarginPercentage,
    required double notionalValue,
    required PipInformationModel pipInformation,
    @Default("") String accountCurrency,
    @Default(2) int digits,
  }) = _MarginInformationModel;

  String get getMarginPercentage =>
      (requiredMarginPercentage).toStringAsFixed(digits) + "%";

  String get getPIPValue => pipInformation.getPIPValue(accountCurrency, digits);

  String get getRequiredMargin =>
      accountCurrency + (requiredMargin).toStringAsFixed(digits);

  String get getMarginFree =>
      (marginFree) >= 0
          ? "+$accountCurrency" + marginFree.abs().toStringAsFixed(digits)
          : "-$accountCurrency" +
              marginFree.abs().abs().toStringAsFixed(digits);

  String get getNotionalValue =>
      accountCurrency + (notionalValue).toStringAsFixed(digits);
}
