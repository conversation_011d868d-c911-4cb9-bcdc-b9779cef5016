import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class ParsedPriceText extends StatelessWidget {
  const ParsedPriceText({
    super.key,
    required this.value,
    required this.color,
    required this.digits,
  });
  final double value;
  final Color color;
  final int digits;

  @override
  Widget build(BuildContext context) {
    final formattedValue = EquitiFormatter.decimalPatternDigits(
      value: value,
      digits: digits,
      locale: Localizations.localeOf(context).toString(),
    );

    // Count digits only (excluding decimal point)
    final digitsOnly = formattedValue.replaceAll('.', '');
    final totalDigits = digitsOnly.length;

    // Determine split point: last 3 digits should be enlarged
    final enlargedDigitCount = totalDigits > 3 ? 3 : totalDigits;
    final normalDigitCount = totalDigits - enlargedDigitCount;

    // Build spans by iterating through formatted string
    final spans = <DuploTextSpan>[];
    final buffer = StringBuffer();
    bool isCurrentlyEnlarged = false;
    int digitsSeen = 0;

    for (final char in formattedValue.split('')) {
      if (char == '.') {
        buffer.write(char);
      } else {
        // This is a digit
        final shouldBeEnlarged = digitsSeen >= normalDigitCount;

        // If style changes, flush current buffer and start new span
        if (shouldBeEnlarged != isCurrentlyEnlarged && buffer.isNotEmpty) {
          spans.add(
            _createSpan(buffer.toString(), isCurrentlyEnlarged, context),
          );
          buffer.clear();
        }

        isCurrentlyEnlarged = shouldBeEnlarged;
        buffer.write(char);
        digitsSeen++;
      }
    }

    // Add final span
    if (buffer.isNotEmpty) {
      spans.add(_createSpan(buffer.toString(), isCurrentlyEnlarged, context));
    }

    return DuploText.rich(spans: spans);
  }

  DuploTextSpan _createSpan(
    String text,
    bool isEnlarged,
    BuildContext context,
  ) {
    return DuploTextSpan(
      text: text,
      style:
          isEnlarged
              ? context.duploTextStyles.textSm
              : context.duploTextStyles.textXs,
      color: color,
      fontWeight: isEnlarged ? DuploFontWeight.bold : DuploFontWeight.medium,
    );
  }
}
