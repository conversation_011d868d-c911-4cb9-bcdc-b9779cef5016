import 'package:e_trader/src/assets/assets.gen.dart';
import 'package:e_trader/src/data/api/withdraw_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/material.dart';

part 'withdraw_tile_model.freezed.dart';

@freezed
abstract class WithdrawTileModel with _$WithdrawTileModel {
  factory WithdrawTileModel({
    required String title,
    required Widget Function() asset,
    required String url,
  }) = _WithdrawTileModel;
}

extension WithdrawTypeExt on WithdrawalTypes {
  WithdrawTileModel get withdrawTile {
    return switch (this) {
      Selcom() => WithdrawTileModel(
        title: "Neteller",
        asset:
            () => _getIconAndText(
              title: "Neteller",
              image: Assets.images.netellerIc.svg(),
            ),
        url: url,
      ),
      Skrill() => WithdrawTileModel(
        title: "Skrill",
        asset: () => Assets.images.skrillIc.svg(height: 18.73, width: 55),
        url: url,
      ),
      Neteller() => WithdrawTileModel(
        title: "Neteller",
        asset: () => Assets.images.netellerIc.svg(),
        url: url,
      ),
      Bank() => WithdrawTileModel(
        title: "Bank",
        asset:
            () => _getIconAndText(
              title: "Bank/Local Bank Transfer",
              image: Assets.images.localBankIconLight.image(
                width: 38,
                height: 38,
              ),
            ),
        url: url,
      ),
      Dinarak() => WithdrawTileModel(
        title: "Dinarak",
        asset:
            () => Assets.images.dinarakLight.svg(height: 33.74, width: 89.55),
        url: url,
      ),
      Gate2Pay() => WithdrawTileModel(
        title: "Equiti Prepaid Card",
        asset: () {
          final title = "Equiti Prepaid Card";
          final List<String> words = title.split(" ");
          final String firstWord = words.firstOrNull ?? "null";
          final String restOfTheText = title.substring(firstWord.length).trim();
          return Row(
            children: [
              Text(
                "$firstWord ",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF00AFAB),
                ),
              ),
              Text(
                restOfTheText,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF273A47),
                ),
              ),
            ],
          );
        },
        url: url,
      ),
      SafeCharge() => WithdrawTileModel(
        title: "Debit/Credit Card",
        asset:
            () => _getIconAndText(
              title: "Debit/Credit Card",
              image: Assets.images.creditCardIconLight.svg(),
            ),
        url: url,
      ),
      InstantBanking() => WithdrawTileModel(
        title: "Cliq",
        asset: () => Assets.images.cliq.image(height: 26.52, width: 55),
        url: url,
      ),
      VaultsPay() => WithdrawTileModel(
        title: "Debit/Credit Card",
        asset:
            () => _getIconAndText(
              title: "Debit/Credit Card",
              image: Assets.images.creditCardIconLight.svg(),
            ),
        url: url,
      ),
      ELipaUG() => WithdrawTileModel(
        title: "MTN & Airtel Uganda",
        asset: () => _defaultView("MTN & Airtel Uganda"),
        url: url,
      ),
      Safaricom() => WithdrawTileModel(
        title: "MPesa",
        asset: () => _defaultView("MPesa"),
        url: url,
      ),
      Unknown element => WithdrawTileModel(
        title: element.name,
        asset: () => _defaultView(element.name),
        url: url,
      ),
    };
  }

  Widget _defaultView(String title) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w500,
        color: Color(0xFF191C1F),
      ),
    );
  }

  Widget _getIconAndText({required Widget image, required String title}) {
    return Row(
      children: [
        image,
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF191C1F),
          ),
        ),
      ],
    );
  }
}
