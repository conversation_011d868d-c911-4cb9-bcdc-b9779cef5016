import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as Trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/price_alert.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/update_active_alerts_hub_use_case.dart';
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/bloc/active_price_alerts_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/modify_price_alert/widget/modify_price_alert_widget.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_list_item_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:visibility_detector/visibility_detector.dart';

class ActivePriceAlertsWidget extends StatelessWidget {
  const ActivePriceAlertsWidget({
    super.key,
    this.symbol,
    this.showEditableOptions = false,
  });

  final String? symbol;
  final bool showEditableOptions;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => diContainer<ActivePriceAlertsBloc>(),
      child: _ActivePriceAlertsContent(
        symbol: symbol,
        showEditableOptions: showEditableOptions,
      ),
    );
  }
}

class _ActivePriceAlertsContent extends StatefulWidget {
  final String? symbol;
  final bool showEditableOptions;

  const _ActivePriceAlertsContent({
    this.symbol,
    this.showEditableOptions = false,
  });

  @override
  State<_ActivePriceAlertsContent> createState() =>
      _ActivePriceAlertsContentState();
}

class _ActivePriceAlertsContentState extends State<_ActivePriceAlertsContent>
    with PerformanceObserverMixin {
  bool _hasSubscribed = false;
  bool _hasRegistered = false;

  void _showModifyPriceAlert(PriceAlert alert, BuildContext context) async {
    final deletedAlertId = await DuploSheet.showModalSheetV2<String?>(
      context,
      appBar: DuploAppBar(
        title: EquitiLocalization.of(context).trader_modifyAlert,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      content: ColoredBox(
        color: context.duploTheme.background.bgSecondary,
        child: Padding(
          padding: EdgeInsets.only(bottom: 24),
          child: ModifyPriceAlertWidget(alert: alert),
        ),
      ),
    );

    // If an alert was deleted, remove it from local state immediately
    if (deletedAlertId != null && mounted) {
      context.read<ActivePriceAlertsBloc>().add(
        ActivePriceAlertsEvent.removeDeletedAlert(deletedAlertId),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<ActivePriceAlertsBloc, ActivePriceAlertsState>(
      listenWhen:
          (previous, current) =>
              previous.processState != current.processState &&
              current.processState is ActivePriceAlertsConnected,
      listener: (listenerContext, _) => _subscribe(),
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return VisibilityDetector(
          key: const Key('active_price_alerts_content'),
          onVisibilityChanged: (info) {
            if (info.visibleFraction > 0) {
              if (!_hasRegistered) {
                builderContext.read<ActivePriceAlertsBloc>().add(
                  ActivePriceAlertsEvent.startActiveAlerts(
                    symbol: widget.symbol,
                  ),
                );
                _hasRegistered = true;
              }
            } else {
              if (_hasRegistered) {
                _unsubscribe();
                _hasRegistered = false;
              }
            }
          },
          //Don't remove this SizedBox as it is required for the visibility detector to work
          child: SizedBox(
            child: CustomScrollView(
              slivers: [
                switch (state.processState) {
                  ActivePriceAlertsLoading() ||
                  ActivePriceAlertsConnected() => DuploShimmerList.sliver(
                    hasLeading: true,
                    hasTrailing: true,
                    itemShimmerType: DuploShimmerType.static,
                  ),
                  ActivePriceAlertsSuccess() => () {
                    if (state.alerts.isEmpty) {
                      return SliverFillRemaining(
                        hasScrollBody: false,
                        child: EmptyOrErrorStateComponent.empty(
                          description:
                              localization.trader_noActiveAlertsDescription,
                          title: localization.trader_noAlerts,
                          svgImage:
                              Trader.Assets.images.noActivePriceAlerts.svg(),
                        ),
                      );
                    }

                    return SliverPadding(
                      padding: const EdgeInsets.only(bottom: 25),
                      sliver: SliverList.builder(
                        itemCount: state.alerts.length,
                        itemBuilder: (sliverBuilderContext, index) {
                          final alert = state.alerts[index];
                          return DuploTap(
                            onTap:
                                () => _showModifyPriceAlert(
                                  alert,
                                  sliverBuilderContext,
                                ),
                            child: PriceAlertListItemWidget(
                              distance: alert.distance,
                              alertPrice: alert.priceAlertPrice,
                              currentPrice: alert.currentPrice,
                              symbolName: alert.symbolName,
                              productLogoUrl: alert.productLogoUrl,
                              digits: alert.digits,
                              priceDirection: alert.priceDirection,
                              tradeType:
                                  alert.direction == 2
                                      ? TradeType.sell
                                      : TradeType.buy,
                            ),
                          );
                        },
                      ),
                    );
                  }(),
                  ActivePriceAlertsError() => SliverFillRemaining(
                    hasScrollBody: false,
                    child: EmptyOrErrorStateComponent.defaultError(
                      builderContext,
                      () => builderContext.read<ActivePriceAlertsBloc>().add(
                        ActivePriceAlertsEvent.startActiveAlerts(
                          symbol: widget.symbol,
                        ),
                      ),
                    ),
                  ),
                  _ => const SliverToBoxAdapter(child: SizedBox.shrink()),
                },
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      print('ActivePriceAlertsWidget Subscribing to active alerts');
      if (mounted)
        context.read<ActivePriceAlertsBloc>().add(
          ActivePriceAlertsEvent.updateActiveAlerts(
            TradingSocketEvent.priceAlert.subscribe,
            widget.symbol,
          ),
        );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      print('ActivePriceAlertsWidget Unsubscribing from active alerts');
      try {
        diContainer<UpdateActiveAlertsHubUseCase>().call(
          eventType: TradingSocketEvent.priceAlert.unsubscribe,
          symbol: widget.symbol,
        );
      } catch (e) {
        print('Error unsubscribing from active alerts: $e');
      }

      _hasSubscribed = false;
    }
  }
}
