import 'dart:async';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/active_alert_response.dart';
import 'package:e_trader/src/domain/model/price_alert.dart';
import 'package:e_trader/src/domain/usecase/get_active_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_active_alerts_hub_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'active_price_alerts_bloc.freezed.dart';
part 'active_price_alerts_event.dart';
part 'active_price_alerts_state.dart';

class ActivePriceAlertsBloc
    extends Bloc<ActivePriceAlertsEvent, ActivePriceAlertsState>
    with DisposableMixin {
  final GetActiveAlertUseCase _getActiveAlertUseCase;
  final UpdateActiveAlertsHubUseCase _updateActiveAlertsHubUseCase;
  final LoggerBase _logger;
  final _alertsById = <String, ActiveAlertResponse>{};

  ActivePriceAlertsBloc(
    this._getActiveAlertUseCase,
    this._updateActiveAlertsHubUseCase,
    this._logger,
  ) : super(const ActivePriceAlertsState()) {
    on<ActivePriceAlertsEvent>((event, emit) async {
      if (event is _StartActiveAlerts) {
        await _onStartActiveAlerts(event, emit);
      } else if (event is _UpdateActiveAlerts) {
        return _onUpdateActiveAlerts(event);
      } else if (event is _RemoveDeletedAlert) {
        return _onRemoveDeletedAlert(event, emit);
      }
    }, transformer: droppable());
    on<_ProcessAlert>(
      (event, emit) => _processAlert(event, emit),
      transformer: restartable(),
    );
    on<_EmitError>(
      (event, emit) => _processError(emit),
      transformer: restartable(),
    );
  }

  FutureOr<void> _onStartActiveAlerts(
    _StartActiveAlerts event,
    Emitter<ActivePriceAlertsState> emit,
  ) async {
    emit(state.copyWith(processState: ActivePriceAlertsProcessState.loading()));

    final result =
        await TaskEither<Exception, Stream<ActiveAlertResponse?>>.Do(($) async {
          final alertsStream = await $(
            _getActiveAlertUseCase.getActiveAlerts(
              symbol: event.symbol,
              subscriberId: '${ActivePriceAlertsBloc}_$hashCode',
              eventType: TradingSocketEvent.priceAlert.register,
            ),
          );
          return alertsStream;
        }).run();

    return result.fold(
      (error) {
        addError(error);
        emit(
          state.copyWith(processState: ActivePriceAlertsProcessState.error()),
        );
      },
      (stream) {
        emit(
          state.copyWith(
            processState: ActivePriceAlertsProcessState.connected(),
          ),
        );
        addSubscription(
          stream.listen(
            (alertResponse) =>
                add(ActivePriceAlertsEvent.processAlert(alertResponse)),
            onError: (Object? error) => add(ActivePriceAlertsEvent.emitError()),
          ),
        );
      },
    );
  }

  void _processAlert(
    _ProcessAlert event,
    Emitter<ActivePriceAlertsState> emit,
  ) {
    final alertResponse = event.alertResponse;

    if (alertResponse == null) {
      emit(
        state.copyWith(
          processState: ActivePriceAlertsProcessState.success(),
          alerts: [],
        ),
      );
      return;
    }

    final target = alertResponse.target;
    final alert = alertResponse.alert;
    final alertId = alert.priceAlertId;

    if (target == 'priceAlertDeleted' || target == 'priceAlertTriggered') {
      _alertsById.remove(alertId);
    } else {
      _alertsById[alertId] = alertResponse;
    }

    final alerts =
        _alertsById.values.map((response) => response.alert).toList();

    emit(
      state.copyWith(
        processState: ActivePriceAlertsProcessState.success(),
        alerts: alerts,
      ),
    );
  }

  FutureOr<void> _processError(Emitter<ActivePriceAlertsState> emit) {
    emit(state.copyWith(processState: ActivePriceAlertsProcessState.error()));
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    super.addError(error, stackTrace);
    _logger.logError(error, stackTrace: stackTrace);
  }

  FutureOr<void> _onUpdateActiveAlerts(_UpdateActiveAlerts event) {
    try {
      _updateActiveAlertsHubUseCase(eventType: event.eventType);
    } catch (e) {
      addError(e);
    }
  }

  void _onRemoveDeletedAlert(
    _RemoveDeletedAlert event,
    Emitter<ActivePriceAlertsState> emit,
  ) {
    _alertsById.remove(event.alertId);
    final alerts =
        _alertsById.values.map((response) => response.alert).toList();

    emit(
      state.copyWith(
        processState: ActivePriceAlertsProcessState.success(),
        alerts: alerts,
      ),
    );
  }
}
