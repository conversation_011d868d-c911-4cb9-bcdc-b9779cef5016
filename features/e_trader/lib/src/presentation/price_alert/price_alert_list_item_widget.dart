import 'package:cached_network_image/cached_network_image.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class PriceAlertListItemWidget extends StatelessWidget {
  final String symbolName;
  final String productLogoUrl;
  final double alertPrice;
  final TradeType tradeType;
  final int digits;
  final double? distance;
  final double? currentPrice;
  final int? priceDirection;

  PriceAlertListItemWidget({
    Key? key,
    required this.alertPrice,
    required this.symbolName,
    required this.productLogoUrl,
    required this.tradeType,
    required this.digits,
    this.currentPrice,
    this.priceDirection,
    this.distance,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final locale = Localizations.localeOf(context).toString();

    final boxColor =
        tradeType == TradeType.buy
            ? theme.utility.utilitySuccess50
            : theme.utility.utilityError50;

    final boxText = tradeType == TradeType.buy ? "BUY" : "SELL";

    final boxTextColor =
        tradeType == TradeType.buy
            ? theme.utility.utilitySuccess700
            : theme.utility.utilityError700;

    double? calculatedDistance = null;
    Color? statusColor = null;
    IconData? statusIcon = null;
    if (currentPrice != null) {
      bool winnning = currentPrice! > alertPrice;
      statusColor =
          winnning
              ? theme.utility.utilitySuccess600
              : theme.utility.utilityError600;
      statusIcon = winnning ? Icons.trending_up : Icons.trending_down;
      calculatedDistance = this.distance ?? (alertPrice - currentPrice!);
    }

    return Container(
      color: theme.background.bgPrimary,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: 4,
                color:
                    tradeType == TradeType.buy
                        ? theme.text.textSuccessPrimary
                        : theme.text.textErrorPrimary,
                height: 72,
              ),
              VerticalDivider(
                color: boxTextColor,
                thickness: 4,
                width: 4,
                indent: 0,
                endIndent: 0,
              ),
              Row(
                children: [
                  const SizedBox(width: 8),
                  CachedNetworkImage(
                    imageUrl: productLogoUrl,
                    width: 40,
                    height: 40,
                  ),
                  const SizedBox(width: 8),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 10),
                      DuploText(
                        text: symbolName,
                        style: duploTextStyles.textSm,
                        fontWeight: DuploFontWeight.medium,
                        color: theme.text.textPrimary,
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: boxColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DuploText(
                          text: boxText,
                          style: duploTextStyles.textXs,
                          fontWeight: DuploFontWeight.medium,
                          color: boxTextColor,
                        ),
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                ],
              ),
              Spacer(),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  DuploText(
                    text: EquitiFormatter.formatDynamicDigits(
                      value: alertPrice,
                      digits: digits,
                      locale: locale,
                    ),
                    style: duploTextStyles.textSm,
                    fontWeight: DuploFontWeight.bold,
                    color: theme.text.textPrimary,
                  ),
                  SizedBox(height: 4),
                  if (statusColor != null &&
                      statusIcon != null &&
                      calculatedDistance != null)
                    Row(
                      children: [
                        DuploText(
                          text:
                              '${calculatedDistance > 0 ? '+' : ''}${EquitiFormatter.formatNumberWithZeroDefault(value: calculatedDistance, digits: 2, locale: locale)}',
                          style: duploTextStyles.textXs,
                          fontWeight: DuploFontWeight.medium,
                          color: statusColor,
                        ),
                        const SizedBox(width: 3),

                        Icon(statusIcon, color: statusColor, size: 16),
                        const SizedBox(width: 8),
                        DuploText(
                          text:
                              currentPrice != null
                                  ? EquitiFormatter.formatDynamicDigits(
                                    value: currentPrice!,
                                    digits: digits,
                                    locale: locale,
                                  )
                                  : '',
                          style: duploTextStyles.textXs,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textSecondary,
                        ),
                      ],
                    )
                  else
                    const SizedBox(height: 20),
                ],
              ),
              const SizedBox(width: 16),
            ],
          ),
          Divider(color: theme.border.borderSecondary, thickness: 1, height: 1),
        ],
      ),
    );
  }
}
