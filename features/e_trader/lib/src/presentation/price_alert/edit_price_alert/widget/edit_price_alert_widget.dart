import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/formatter/decimal_text_input_formatter.dart';
import 'package:e_trader/src/domain/model/set_price_alert_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard_input_control.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class _PriceAlertDistanceWidget extends StatelessWidget {
  final double distance;
  final int digits;

  const _PriceAlertDistanceWidget({
    required this.distance,
    required this.digits,
  });

  @override
  Widget build(BuildContext context) {
    final loc = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final text = EquitiFormatter.decimalPatternDigits(
      value: distance,
      digits: digits,
      locale: Localizations.localeOf(context).toString(),
    );

    final color =
        distance >= 0 ? theme.text.textSecondary : theme.text.textErrorPrimary;

    return Container(
      child: DuploText.rich(
        spans: [
          DuploTextSpan(
            text: "${loc.trader_distance}: ",
            style: duploTextStyles.textXs,
            color: theme.text.textBrandPrimary,
          ),
          DuploTextSpan(
            text: text,
            style: duploTextStyles.textXs,
            color: color,
          ),
        ],
        textAlign: TextAlign.start,
      ),
    );
  }
}

class EditPriceAlertWidget extends StatefulWidget {
  final double symbolPrice;
  final int digits;
  final TradeType tradeType;
  final double enteredPrice;
  final TradingKeyboardInputControl inputControl;
  final void Function(EditPriceAlertState) onStateChanged;
  final String initLocale;
  const EditPriceAlertWidget({
    super.key,
    required this.inputControl,
    required this.symbolPrice,
    required this.enteredPrice,
    required this.digits,
    required this.tradeType,
    required this.onStateChanged,
    required this.initLocale,
  });

  @override
  State<EditPriceAlertWidget> createState() => _EditPriceAlertWidgetState();
}

class _EditPriceAlertWidgetState extends State<EditPriceAlertWidget>
    with AutomaticKeepAliveClientMixin {
  late final TextEditingController newController;

  @override
  void initState() {
    super.initState();

    newController = TextEditingController();
    newController.text = EquitiFormatter.formatDynamicDigits(
      value: widget.enteredPrice,
      digits: widget.digits,
      locale: widget.initLocale,
    );
    context.read<EditPriceAlertBloc>()..add(
      EditPriceAlertEvent.setInitialValues(
        widget.tradeType,
        widget.symbolPrice,
        widget.digits,
        widget.enteredPrice,
        '',
      ),
    );
  }

  dispose() {
    newController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(EditPriceAlertWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.tradeType != widget.tradeType) {
      final l10n = EquitiLocalization.of(context);

      context.read<EditPriceAlertBloc>()..add(
        EditPriceAlertEvent.tradeTypeChanged(
          widget.tradeType,
          widget.symbolPrice,
          l10n.trader_inputValidationErrorMessage,
        ),
      );
    } else if (oldWidget.symbolPrice != widget.symbolPrice) {
      context.read<EditPriceAlertBloc>()
        ..add(EditPriceAlertEvent.symbolPriceChanged(widget.symbolPrice));
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final l10n = EquitiLocalization.of(context);
    final TextDirection currentDirection = Directionality.of(context);
    final bool isRTL = currentDirection == TextDirection.rtl;
    return BlocBuilder<EditPriceAlertBloc, EditPriceAlertState>(
      buildWhen: (previous, current) => current != previous,
      builder: (builderContext, state) {
        return switch (state) {
          EditPriceAlertSuccess(:final info) => () {
            widget.onStateChanged(state);

            if (info.textFieldValue == null) {
              newController.text = EquitiFormatter.formatDynamicDigits(
                value: info.enteredPrice,
                digits: widget.digits,
                locale: Localizations.localeOf(builderContext).toString(),
              );
            }

            return StepperControlWidget(
              segmentControWidget: HighlightOptionBoxWidget(
                oneTimeSelection: true,
                selectedIndex: info.selectedPercentOptionIndex,
                options:
                    EditedPriceAlertModel.percentOptions.map((e) {
                      final value = EquitiFormatter.formatNumber(
                        value: e,
                        locale:
                            Localizations.localeOf(builderContext).toString(),
                      );
                      return isRTL
                          ? "$value%${e > 0 ? "+" : ""}"
                          : "${e > 0 ? "+" : ""}$value%";
                    }).toList(),
                onSelectionChange: (value, index) {
                  builderContext.read<EditPriceAlertBloc>().add(
                    EditPriceAlertEvent.percentOptionChanged(
                      index,
                      l10n.trader_inputValidationErrorMessage,
                    ),
                  );
                },
              ),
              inputWidget: StepperNumberInputWithKeyboardWidget(
                digits: widget.digits,
                formatters: [
                  DecimalTextInputFormatter(decimalRange: info.digits),
                ],
                inputControl: widget.inputControl,
                prescisionFactor: info.digits,
                hintText: l10n.trader_enterPrice,
                onValueChange: (value) {
                  builderContext.read<EditPriceAlertBloc>().add(
                    EditPriceAlertEvent.inputFieldValueChanged(
                      value,
                      l10n.trader_inputValidationErrorMessage,
                    ),
                  );
                },
                changeFactor: info.changeFactor,
                inputEditingController: newController,
              ),
              footerWidget:
                  (info.validationErrorMessage == null)
                      ? _PriceAlertDistanceWidget(
                        distance: info.distance,
                        digits: info.digits,
                      )
                      : DuploText(
                        text: info.validationErrorMessage,
                        style: duploTextStyles.textXs,
                        color: theme.text.textErrorPrimary,
                        textAlign: TextAlign.start,
                      ),
              title: l10n.trader_alertPrice,
              bordered: true,
            );
          }(),
          _ => const SizedBox.shrink(),
        };
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
