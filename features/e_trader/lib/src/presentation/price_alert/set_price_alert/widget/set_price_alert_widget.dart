import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/set_price_alert_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/buy_sell/buy_sell_buttons.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/widget/edit_price_alert_widget.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_list_item_widget.dart';
import 'package:e_trader/src/presentation/price_alert/set_price_alert/bloc/set_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard_input_control.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SetPriceAlertWidget extends StatefulWidget {
  final String symbol;
  final String symbolImageUrl;
  final bool? unRegisterKeyboard;
  final void Function(bool)? onHideTabbar;

  const SetPriceAlertWidget({
    super.key,
    required this.symbol,
    required this.symbolImageUrl,
    this.unRegisterKeyboard,
    this.onHideTabbar,
  });

  @override
  State<SetPriceAlertWidget> createState() => _SetPriceAlertWidgetState();
}

class _SetPriceAlertWidgetState extends State<SetPriceAlertWidget>
    with AutomaticKeepAliveClientMixin {
  TradingKeyboardInputControl _inputControl = TradingKeyboardInputControl();
  final animationDuration = Duration(milliseconds: 250);
  double stepperControlDefaultPosition = 24 + 75 + 24;

  @override
  void didUpdateWidget(SetPriceAlertWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.unRegisterKeyboard != widget.unRegisterKeyboard) {
      if (widget.unRegisterKeyboard == true) {
        _inputControl.unregister();
        _inputControl.hide();
      }
    }
  }

  void _showSuccessToastMessage(BuildContext context, SetPriceAlertModel info) {
    final toast = DuploToast();
    toast.showToastMessage(
      context: context,
      widget: DuploToastDecoratorWidget(
        messageType: ToastMessageType.success,
        statusColor:
            info.selctedTradeType == TradeType.buy
                ? context.duploTheme.utility.utilitySuccess600
                : context.duploTheme.utility.utilityError600,
        contentWidget: PriceAlertListItemWidget(
          alertPrice: info.enteredPrice,
          symbolName: info.prices.symbol,
          productLogoUrl: widget.symbolImageUrl,
          digits: info.prices.digits,
          tradeType: info.selctedTradeType,
        ),
        titleMessage: EquitiLocalization.of(context).trader_alertSaved,
        onLeadingAction: () {
          toast.hidesToastMessage();
        },
        onTap: () {
          toast.hidesToastMessage();
        },
      ),
    );
  }

  void _showFailureToastMessage() {
    final toast = DuploToast();
    toast.showToastMessage(
      context: context,
      widget: DuploToastMessage(
        titleMessage: EquitiLocalization.of(context).trader_alertNotSaved,
        descriptionMessage:
            EquitiLocalization.of(context).trader_unableToModifyAlertMessage,
        messageType: ToastMessageType.error,
        onLeadingAction: () {
          toast.hidesToastMessage();
        },
      ),
    );
  }

  void _saveAlert(BuildContext blocBuilderContext, SetPriceAlertModel info) {
    if (info.viewState == SetPriceAlertViewState.idle &&
        info.vaidationError == false) {
      blocBuilderContext.read<SetPriceAlertBloc>().add(
        SetPriceAlertEvent.onSaveAlert(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final l10n = EquitiLocalization.of(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider<SetPriceAlertBloc>(
          create:
              (_) =>
                  diContainer<SetPriceAlertBloc>()..add(
                    SetPriceAlertEvent.fetchSymbolDetails(widget.symbol, null),
                  ),
        ),
        BlocProvider<EditPriceAlertBloc>(
          create: (_) => diContainer<EditPriceAlertBloc>(),
        ),
      ],
      child: BlocConsumer<SetPriceAlertBloc, SetPriceAlertState>(
        listenWhen: (previous, current) {
          return switch (previous) {
            SetPriceAlertSuccess(info: final previousInfo) => switch (current) {
              SetPriceAlertSuccess(info: final currentInfo) =>
                currentInfo.viewState != previousInfo.viewState,
              _ => false,
            },
            _ => false,
          };
        },
        listener: (listnerContext, state) {
          if (state case SetPriceAlertSuccess(:final info)) {
            if (info.viewState == SetPriceAlertViewState.creationSuccess) {
              Navigator.of(listnerContext).maybePop();
              _showSuccessToastMessage(context, info);
            } else if (info.viewState ==
                SetPriceAlertViewState.creationFailed) {
              _showFailureToastMessage();
            }
          }
        },
        buildWhen: (previous, current) => current != previous,
        builder: (blocBuilderContext, state) {
          final totalWidth = MediaQuery.sizeOf(blocBuilderContext).width;
          final widthAvaliable =
              MediaQuery.sizeOf(blocBuilderContext).width - (16 * 2);
          return switch (state) {
            SetPriceAlertLoading() => Padding(
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
              child: Column(
                children: [
                  const DuploMultipleShimmersInRow(count: 2, height: 80),
                  SizedBox(height: 16),
                  const DuploMultipleShimmersInRow(count: 1, height: 140),
                ],
              ),
            ),
            SetPriceAlertError() => Center(
              child: EmptyOrErrorStateComponent.defaultError(
                blocBuilderContext,
                () {
                  blocBuilderContext.read<SetPriceAlertBloc>().add(
                    SetPriceAlertEvent.fetchSymbolDetails(widget.symbol, null),
                  );
                },
              ),
            ),
            SetPriceAlertSuccess(:final info) => ValueListenableBuilder<bool>(
              valueListenable: _inputControl.visible,
              builder: (buildContext, visible, __) {
                widget.onHideTabbar?.call(visible);
                return Container(
                  padding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                  width: totalWidth,
                  child: Stack(
                    alignment: AlignmentDirectional.topCenter,
                    children: [
                      Positioned(
                        bottom: 0,
                        child: AnimatedOpacity(
                          duration: animationDuration,
                          opacity: visible ? 1 : 0,
                          child: Container(
                            width: widthAvaliable,
                            child: TradingKeyboard(
                              inputControl: _inputControl,
                              onDone: () {
                                _inputControl.hide();
                                FocusScope.of(context).unfocus();
                              },
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 24,
                        child: AnimatedOpacity(
                          duration: animationDuration,
                          opacity: visible ? 0 : 1,
                          child: Container(
                            width: widthAvaliable,
                            child: BuySellButtons(
                              digits: info.prices.digits,
                              onTap: (tradeType) {
                                blocBuilderContext
                                    .read<SetPriceAlertBloc>()
                                    .add(
                                      SetPriceAlertEvent.tradeTypeChanged(
                                        tradeType,
                                      ),
                                    );
                              },
                              spread: info.prices.spread,
                              buyButtonState:
                                  info.selctedTradeType == TradeType.buy
                                      ? BuySellButtonState.selected(
                                        info.prices.ask,
                                      )
                                      : BuySellButtonState.active(
                                        info.prices.ask,
                                      ),
                              sellButtonState:
                                  info.selctedTradeType == TradeType.sell
                                      ? BuySellButtonState.selected(
                                        info.prices.bid,
                                      )
                                      : BuySellButtonState.active(
                                        info.prices.bid,
                                      ),
                            ),
                          ),
                        ),
                      ),
                      AnimatedPositioned(
                        duration: animationDuration,
                        top: visible ? 8 : stepperControlDefaultPosition,
                        child: Container(
                          width: widthAvaliable,
                          child: EditPriceAlertWidget(
                            inputControl: _inputControl,
                            enteredPrice: info.enteredPrice,
                            tradeType: info.selctedTradeType,
                            symbolPrice:
                                info.selctedTradeType == TradeType.buy
                                    ? info.prices.ask
                                    : info.prices.bid,
                            digits: info.prices.digits,
                            onStateChanged: (editState) {
                              blocBuilderContext.read<SetPriceAlertBloc>().add(
                                SetPriceAlertEvent.onEditPriceStateChanged(
                                  editState,
                                ),
                              );
                            },
                            initLocale:
                                Localizations.localeOf(buildContext).toString(),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 20,
                        child: IgnorePointer(
                          ignoring: visible,
                          child: AnimatedOpacity(
                            duration: animationDuration,
                            opacity: visible ? 0 : 1,
                            child: Container(
                              width: widthAvaliable,
                              child:
                                  info.selctedTradeType == TradeType.buy
                                      ? DuploButton.buyPrimary(
                                        title: l10n.trader_saveAlert,
                                        onTap: () {
                                          _saveAlert(blocBuilderContext, info);
                                        },
                                        isDisabled: info.vaidationError,
                                        loadingText:
                                            EquitiLocalization.of(
                                              context,
                                            ).trader_saving,
                                        isLoading:
                                            info.viewState ==
                                            SetPriceAlertViewState.creating,
                                      )
                                      : DuploButton.sellPrimary(
                                        title: l10n.trader_saveAlert,
                                        onTap: () {
                                          _saveAlert(blocBuilderContext, info);
                                        },
                                        isDisabled: info.vaidationError,
                                        loadingText:
                                            EquitiLocalization.of(
                                              context,
                                            ).trader_saving,
                                        isLoading:
                                            info.viewState ==
                                            SetPriceAlertViewState.creating,
                                      ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          };
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
