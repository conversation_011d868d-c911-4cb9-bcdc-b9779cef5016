import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/market_order_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/pending_order_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

typedef CreateTradeArgs =
    ({
      int digits,
      String symbolCode,
      String symbolImageUrl,
      String assetType,
      double minLot,
      double maxLot,
      bool isForex,
    });

class CreateTradeWidget extends StatefulWidget {
  const CreateTradeWidget({super.key, required this.args});
  final CreateTradeArgs args;

  @override
  State<CreateTradeWidget> createState() => _CreateTradeWidgetState();
}

class _CreateTradeWidgetState extends State<CreateTradeWidget>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(() {
      if (!tabController.indexIsChanging &&
          _currentTabIndex != tabController.index) {
        setState(() {
          _currentTabIndex = tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return DuploTabBar(
      tabController: tabController,
      tabTitles: [
        DuploTabBarTitle(
          text: localization.trader_marketOrder,
          semanticsIdentifier: 'market_order_tab',
        ),
        DuploTabBarTitle(
          text: localization.trader_pendingOrder,
          semanticsIdentifier: 'pending_order_tab',
        ),
      ],
      tabViews: [
        BlocBuilder<CreateTradeBloc, CreateTradeState>(
          buildWhen: (previous, current) => previous != current,
          builder:
              (builderContext, state) => switch (state.processState) {
                CreateOrderLoadingProcessState() => const Center(
                  child: CircularProgressIndicator.adaptive(),
                ),
                CreateOrderDisconnectedProcessState() => Center(
                  child: Text(localization.trader_somethingWentWrong),
                ),
                _ => MarketOrderWidget(
                  args: (
                    symbol: widget.args.symbolCode,
                    assetType: widget.args.assetType,
                    tradeType: state.tradeType,
                    accountNumber: state.accountNumber!,
                    marginInformation: state.marginInformation,
                    symbolQuoteModel: state.symbolQuoteModel!,
                    minLot: widget.args.minLot,
                    maxLot: widget.args.maxLot,
                    symbolImageUrl: widget.args.symbolImageUrl,
                    tabIndex: _currentTabIndex,
                    isForex: widget.args.isForex,
                  ),
                ),
              },
        ),
        BlocBuilder<CreateTradeBloc, CreateTradeState>(
          buildWhen: (previous, current) => previous != current,
          builder: (builderContext, state) {
            return switch (state.processState) {
              CreateOrderLoadingProcessState() => const Center(
                child: CircularProgressIndicator.adaptive(),
              ),
              CreateOrderDisconnectedProcessState() => Center(
                child: Text(localization.trader_somethingWentWrong),
              ),
              _ => PendingOrderWidget(
                args: (
                  tradeType: state.tradeType,
                  assetType: widget.args.assetType,
                  accountNumber: state.accountNumber!,
                  marginInformation: state.marginInformation,
                  symbolQuoteModel: state.symbolQuoteModel!,
                  minLot: widget.args.minLot,
                  maxLot: widget.args.maxLot,
                  symbolImageUrl: widget.args.symbolImageUrl,
                  tabIndex: _currentTabIndex,
                  isForex: widget.args.isForex,
                ),
              ),
            };
          },
        ),
      ],
      isScrollable: false,
      isFlex: false,
    );
  }
}
