import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/socket/order_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_pending_order/bloc/modify_pending_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/modify_pending_order_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

Future<void> modifyPendingOrderBottomSheet(
  BuildContext context,
  OrderModel orderModel,
) {
  final localization = EquitiLocalization.of(context);
  final theme = context.duploTheme;
  return DuploSheet.showModalSheetV2(
    context,
    appBar: DuploAppBar(
      automaticallyImplyLeading: false,
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
      title: EquitiLocalization.of(context).trader_orderDetails,
    ),
    builder:
        (child) => BlocProvider(
          create:
              (blocProviderContext) =>
                  diContainer<ModifyPendingOrderBloc>(param1: orderModel),
          child: child,
        ),
    content: ColoredBox(
      color: theme.background.bgSecondary,
      child: ModifyPendingOrderView(order: orderModel),
    ),
    bottomBar: BlocBuilder<ModifyPendingOrderBloc, ModifyPendingOrderState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return Padding(
          padding: EdgeInsetsDirectional.all(16),
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: DuploButton.secondary(
                  title: localization.trader_delete,
                  loadingText: localization.trader_deleting,
                  isLoading: switch (state.currentState) {
                    DeletingOrderProcessState() => true,
                    _ => false,
                  },
                  onTap:
                      () => blocBuilderContext
                          .read<ModifyPendingOrderBloc>()
                          .add(const ModifyPendingOrderEvent.deleteOrder()),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: DuploButton.primaryBold(
                  isLoading: switch (state.currentState) {
                    ModifyPlacingPendingOrderProcessState() => true,
                    _ => false,
                  },
                  title: localization.trader_saveOrder,
                  loadingText: localization.trader_saving,
                  isDisabled: switch (state.currentState) {
                    ModifyPlacingPendingOrderProcessState() => false,
                    _ =>
                      !(state.isValid() &&
                          (state.orderPrice != orderModel.openPrice ||
                              state.stopLoss != orderModel.stopLoss ||
                              state.takeProfit != orderModel.takeProfit)),
                  },
                  onTap: () {
                    blocBuilderContext.read<ModifyPendingOrderBloc>().add(
                      const ModifyPendingOrderEvent.submit(),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}
