import 'dart:async';

import 'package:e_trader/src/data/api/modify_order_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/margin_requirment_hub_request_model.dart';
import 'package:e_trader/src/data/socket/order_model.dart';
import 'package:e_trader/src/data/socket/order_response.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/model/margin_requirment.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/delete_order_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_order_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_margin_requirment_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_orders_use_case.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:e_trader/src/presentation/model/pip_information_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';
import 'package:rxdart/rxdart.dart';

part 'modify_pending_order_bloc.freezed.dart';
part 'modify_pending_order_event.dart';
part 'modify_pending_order_state.dart';

class ModifyPendingOrderBloc
    extends Bloc<ModifyPendingOrderEvent, ModifyPendingOrderState> {
  ModifyPendingOrderBloc(
    SubscribeToOrdersUseCase subscribeToOrderUseCase,
    ModifyOrderUseCase modifyOrderUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    SubscribeToMarginRequirmentUseCase subscribeToMarginRequirmentUseCase,
    DeleteOrderUseCase deleteOrderUseCase,
    OrderModel orderModel,
  ) : _subscribeToOrdersUseCase = subscribeToOrderUseCase,
      _modifyOrderUseCase = modifyOrderUseCase,
      _getAccountNumberUseCase = getAccountNumberUseCase,
      _subscribeToMarginRequirmentUseCase = subscribeToMarginRequirmentUseCase,
      _deleteOrderUseCase = deleteOrderUseCase,
      super(_ModifyPendingOrderState(orderModel: orderModel)) {
    on<ModifyPendingOrderEvent>((event, emit) async {
      await switch (event) {
        _Initialize() => _initialize(emit),
        _ModifyPendingOrderPriceChanged value => _orderPriceChanged(
          value,
          emit,
        ),
        _ModifyPendingOrderTakeProfitChanged value => _takeProfitChanged(
          value,
          emit,
        ),
        _ModifyPendingOrderStopLossChanged value => _stopLossChanged(
          value,
          emit,
        ),
        _ModifyPendingOrderSubmit() => _submit(emit),
        _ModifyPendingOrderDeleteOrder() => _deleteOrder(emit),
      };
    });
    add(ModifyPendingOrderEvent.initialize());
  }

  final SubscribeToOrdersUseCase _subscribeToOrdersUseCase;
  final SubscribeToMarginRequirmentUseCase _subscribeToMarginRequirmentUseCase;
  final ModifyOrderUseCase _modifyOrderUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final DeleteOrderUseCase _deleteOrderUseCase;

  FutureOr<void> _initialize(Emitter<ModifyPendingOrderState> emit) async {
    final result =
        await TaskEither<Exception, (List<Stream<Object?>>, String)>.Do((
          $,
        ) async {
          final accountNumber = await $(
            _getAccountNumberUseCase().toTaskEither(),
          );
          final streams = await $(
            TaskEither.sequenceList([
              _subscribeToOrdersUseCase(
                accountNumber: accountNumber,
                eventType: TradingSocketEvent.orders.subscribe,
                subscriberId: '${ModifyPendingOrderBloc}_$hashCode',
              ),
              _getMarginRequirementHubStream(
                symbolCode: state.orderModel.symbol,
                tradeType: state.orderModel.tradeType,
                volume: state.orderModel.volume,
                accountNumber: accountNumber,
              ),
            ]),
          );

          return (streams, accountNumber);
        }).run();

    return result.fold(
      (error) {
        addError(error);
        emit(
          state.copyWith(
            currentState: ModifyPendingOrderProcessState.disconnected(),
          ),
        );
      },
      (value) => emit.forEach(
        Rx.combineLatest(value.$1, (List<Object?> data) {
          final orderModel = (data.firstOrNull as OrderResponse?)?.order;
          final marginRequirement = data.elementAtOrNull(1) as MarginRequirment;
          return (orderModel, marginRequirement);
        }),
        onData: (streams) {
          final orderModel = streams.$1;
          final marginRequirement = streams.$2;
          final accountNumber = value.$2;
          OrderModel selectedOrderModel = state.orderModel;
          if (orderModel != null && orderModel.id == selectedOrderModel.id) {
            selectedOrderModel = orderModel;
          }
          return state.copyWith(
            orderModel: selectedOrderModel,
            accountNumber: accountNumber,
            marginInformation: _getMarginInformation(marginRequirement),
            currentState: ModifyPendingOrderProcessState.connected(),
          );
        },
        onError: (error, stackTrace) {
          addError(error, stackTrace);
          return state.copyWith(
            currentState: ModifyPendingOrderProcessState.disconnected(),
          );
        },
      ),
    );
  }

  MarginInformationModel _getMarginInformation(
    MarginRequirment marginRequirmentModel,
  ) {
    var marginInformation = state.marginInformation;
    if (marginInformation == null) {
      marginInformation = MarginInformationModel(
        marginFree: marginRequirmentModel.marginFree,
        requiredMargin: marginRequirmentModel.requiredMargin,
        requiredMarginPercentage:
            marginRequirmentModel.requiredMarginPercentage,
        maxLot: marginRequirmentModel.maxLot,
        minLot: marginRequirmentModel.minLot,
        notionalValue: marginRequirmentModel.notionalValue,
        pipInformation: PipInformationModel(
          pipValue: marginRequirmentModel.pipValue,
          onePip: marginRequirmentModel.onePip,
          pipMultipler: marginRequirmentModel.multiply,
        ),
      );
    } else {
      marginInformation.marginFree = marginRequirmentModel.marginFree;
      marginInformation.requiredMargin = marginRequirmentModel.requiredMargin;
      marginInformation.requiredMarginPercentage =
          marginRequirmentModel.requiredMarginPercentage;
      marginInformation.maxLot = marginRequirmentModel.maxLot;
      marginInformation.minLot = marginRequirmentModel.minLot;
      marginInformation.notionalValue = marginRequirmentModel.notionalValue;

      marginInformation.pipInformation.pipValue =
          marginRequirmentModel.pipValue;
      marginInformation.pipInformation.onePip = marginRequirmentModel.onePip;
      marginInformation.pipInformation.pipMultipler =
          marginRequirmentModel.multiply;
    }
    return marginInformation;
  }

  TaskEither<Exception, Stream<MarginRequirment>>
  _getMarginRequirementHubStream({
    required int volume,
    required TradeType tradeType,
    required String symbolCode,
    required String accountNumber,
  }) {
    final model = MarginRequirmentHubRequestModel(
      accountNumber: accountNumber,
      symbol: symbolCode,
      volume: volume,
      tradeType: tradeType,
    );

    return _subscribeToMarginRequirmentUseCase(
      model,
      subscriberId: '${ModifyPendingOrderBloc}_$hashCode',
      eventType: TradingSocketEvent.marginRequirements.subscribe,
    );
  }

  FutureOr<void> _orderPriceChanged(
    _ModifyPendingOrderPriceChanged value,
    Emitter<ModifyPendingOrderState> emit,
  ) {
    emit(state.copyWith(orderPriceState: value.state));
  }

  FutureOr<void> _takeProfitChanged(
    _ModifyPendingOrderTakeProfitChanged value,
    Emitter<ModifyPendingOrderState> emit,
  ) {
    emit(state.copyWith(takeProfitState: value.state));
  }

  FutureOr<void> _stopLossChanged(
    _ModifyPendingOrderStopLossChanged value,
    Emitter<ModifyPendingOrderState> emit,
  ) {
    emit(state.copyWith(stopLossState: value.state));
  }

  Future<void> _submit(Emitter<ModifyPendingOrderState> emit) async {
    emit(
      state.copyWith(
        currentState: ModifyPendingOrderProcessState.placingOrder(),
      ),
    );
    final result =
        await _modifyOrderUseCase(
          ModifyOrderModel(
            accountNumber: state.accountNumber!,
            orderId: state.orderModel.id.toString(),
            price: state.orderPrice,
            takeProfit: state.takeProfit,
            stopLoss: state.stopLoss,
          ),
        ).run();
    result.fold(
      (exception) {
        if (exception is PositionsAndOrdersException &&
            exception.errors.containsKey("IsMarketOpen")) {
          final isMarketOpenError =
              exception.errors["IsMarketOpen"]?.firstOrNull;
          if (isMarketOpenError?.toLowerCase() == "false") {
            emit(
              state.copyWith(
                currentState: ModifyPendingOrderProcessState.marketClosed(),
              ),
            );
            return;
          }
        } else {
          emit(
            state.copyWith(
              currentState: ModifyPendingOrderProcessState.orderError(),
            ),
          );
        }
        addError(exception);
      },
      (response) {
        emit(
          state.copyWith(
            currentState: ModifyPendingOrderProcessState.orderSuccess(),
          ),
        );
      },
    );
  }

  Future<void> _deleteOrder(Emitter<ModifyPendingOrderState> emit) async {
    emit(
      state.copyWith(
        currentState: ModifyPendingOrderProcessState.deletingOrder(),
      ),
    );

    final result =
        await _deleteOrderUseCase(
          accountNumber: state.accountNumber!,
          orderId: state.orderModel.id.toString(),
        ).run();

    result.fold(
      (exception) {
        if (exception is PositionsAndOrdersException &&
            exception.errors.containsKey("IsMarketOpen")) {
          final isMarketOpenError =
              exception.errors["IsMarketOpen"]?.firstOrNull;
          if (isMarketOpenError?.toLowerCase() == "false") {
            emit(
              state.copyWith(
                currentState: ModifyPendingOrderProcessState.marketClosed(),
              ),
            );
            return;
          }
        } else
          emit(
            state.copyWith(
              currentState:
                  ModifyPendingOrderProcessState.orderDeletionFailure(),
            ),
          );
        addError(exception);
      },
      (isSuccess) {
        isSuccess
            ? emit(
              state.copyWith(
                currentState:
                    ModifyPendingOrderProcessState.orderDeletionSuccess(),
              ),
            )
            : emit(
              state.copyWith(
                currentState:
                    ModifyPendingOrderProcessState.orderDeletionFailure(),
              ),
            );
      },
    );
  }
}
