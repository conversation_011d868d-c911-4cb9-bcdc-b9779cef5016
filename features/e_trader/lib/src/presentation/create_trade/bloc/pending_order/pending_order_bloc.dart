import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/create_order_request_model.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/calculate_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/create_order_use_case.dart';
import 'package:e_trader/src/domain/usecase/validate_margin_allocation_use_case.dart';
import 'package:e_trader/src/domain/validators/trade_error.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/order_size_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/pending_order_widget.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'pending_order_bloc.freezed.dart';
part 'pending_order_event.dart';
part 'pending_order_state.dart';

class PendingOrderBloc extends Bloc<PendingOrderEvent, PendingOrderState> {
  PendingOrderBloc(
    PendingOrderArgs args,
    this._validateMarginAllocationUseCase,
    this._calculateVolumeUseCase,
    this._createOrderUseCase,
    this._equitiTraderNavigation,
  ) : super(
        _PendingOrderState(
          accountNumber: args.accountNumber,
          marginInformation: args.marginInformation,
          symbolQuoteModel: args.symbolQuoteModel,
          symbolCode: args.symbolQuoteModel.symbol,
          tradeType: args.tradeType,
          orderSizeState: TradeComponentState.success(args.minLot),
          takeProfitState: TradeComponentState.inactive(),
          stopLossState: TradeComponentState.inactive(),
          marginAllocationState:
              args.marginInformation == null
                  ? TradeComponentState.inactive()
                  : _validateMarginAllocationUseCase(
                    args.marginInformation!.requiredMarginPercentage,
                  )
                  ? TradeComponentState.success(null)
                  : TradeComponentState.error(
                    TradeError.marginAllocation(
                      message: 'Margin allocation is not valid',
                    ),
                  ),
          orderPriceState: TradeComponentState.loading(),
        ),
      ) {
    on<PendingOrderEvent>(
      (event, emit) => switch (event) {
        _PendingOrderSizeChanged val => _orderSizeChanged(emit, val),
        _TakeProfitChanged val => _takeProfitChanged(emit, val),
        _StopLossChanged val => _stopLossChanged(emit, val),
        _ValuesChanged val => _handleValueChanged(emit, val),
        _PendingOrderPriceChanged val => _priceChanged(emit, val),
        _Submit val => _submit(emit, val),
        _GoToPortfolioEvent _ => _goToPortfolio(),
      },
      transformer: sequential(),
    );
  }

  final ValidateMarginAllocationUseCase _validateMarginAllocationUseCase;
  final CalculateVolumeUseCase _calculateVolumeUseCase;
  final CreateOrderUseCase _createOrderUseCase;
  final EquitiTraderNavigation _equitiTraderNavigation;

  FutureOr<void> _takeProfitChanged(
    Emitter<PendingOrderState> emit,
    _TakeProfitChanged event,
  ) {
    emit(state.copyWith(takeProfitState: event.state));
  }

  FutureOr<void> _orderSizeChanged(
    Emitter<PendingOrderState> emit,
    _PendingOrderSizeChanged event,
  ) {
    emit(state.copyWith(orderSizeState: event.state));
  }

  FutureOr<void> _stopLossChanged(
    Emitter<PendingOrderState> emit,
    _StopLossChanged event,
  ) {
    emit(state.copyWith(stopLossState: event.state));
  }

  FutureOr<void> _handleValueChanged(
    Emitter<PendingOrderState> emit,
    _ValuesChanged event,
  ) {
    emit(
      state.copyWith(
        marginAllocationState: TradeComponentState<void, TradeError>.loading(),
      ),
    );
    final marginAllocationState = _validateMarginAllocation(event.args);
    emit(
      state.copyWith(
        tradeType: event.args.tradeType,
        marginInformation: event.args.marginInformation,
        symbolQuoteModel: event.args.symbolQuoteModel,
        marginAllocationState: marginAllocationState,
      ),
    );
  }

  TradeComponentState<void, TradeError> _validateMarginAllocation(
    PendingOrderArgs args,
  ) {
    final marginInformation = args.marginInformation;
    if (marginInformation == null) return TradeComponentState.inactive();
    return _validateMarginAllocationUseCase(
          marginInformation.requiredMarginPercentage,
        )
        ? TradeComponentState<void, TradeError>.success(null)
        : TradeComponentState.error(
          TradeError.marginAllocation(
            message: 'Margin allocation is not valid',
          ),
        );
  }

  FutureOr<void> _priceChanged(
    Emitter<PendingOrderState> emit,
    _PendingOrderPriceChanged val,
  ) {
    emit(state.copyWith(orderPriceState: val.state));
  }

  FutureOr<void> _submit(Emitter<PendingOrderState> emit, _Submit _) async {
    emit(state.copyWith(currentState: PendingOrderProcessState.placingOrder()));

    final model = CreateOrderRequestModel(
      accountNumber: state.accountNumber,
      symbolName: state.symbolCode,
      volume: _calculateVolumeUseCase(state.orderSizeState.value),
      tradeType: state.tradeType!,
      takeProfit: state.takeProfit,
      stopLoss: state.stopLoss,
      price: state.orderPriceState.value,
    );

    final result = await _createOrderUseCase(model).run();
    result.fold(
      (exception) {
        if (exception is PositionsAndOrdersException &&
            exception.errors.containsKey("IsMarketOpen")) {
          final isMarketOpenError =
              exception.errors["IsMarketOpen"]?.firstOrNull;
          if (isMarketOpenError?.toLowerCase() == "false") {
            emit(
              state.copyWith(
                currentState: PendingOrderProcessState.marketClosed(),
              ),
            );
            return;
          }
        } else
          emit(
            state.copyWith(currentState: PendingOrderProcessState.orderError()),
          );
        addError(exception);
      },
      (orderResponse) {
        if (orderResponse.isOk) {
          emit(
            state.copyWith(
              currentState: PendingOrderProcessState.orderSuccess(),
            ),
          );
        } else {
          emit(
            state.copyWith(
              currentState: PendingOrderProcessState.orderError(
                errorMessage: orderResponse.errorMessage,
              ),
            ),
          );
        }
      },
    );
  }

  FutureOr<void> _goToPortfolio() {
    _equitiTraderNavigation.navigateToPortfolio(
      result: TradeConfirmationResult.viewOrders,
    );
  }
}
