import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/create_trade_request_model.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/calculate_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/create_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/validate_margin_allocation_use_case.dart';
import 'package:e_trader/src/domain/validators/trade_error.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/order_size_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/market_order_widget.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'market_order_bloc.freezed.dart';
part 'market_order_event.dart';
part 'market_order_state.dart';

class MarketOrderBloc extends Bloc<MarketOrderEvent, MarketOrderState> {
  MarketOrderBloc(
    MarketOrderArgs args,
    this._createTradeUseCase,
    this._calculateVolumeUseCase,
    this._validateMarginAllocationUseCase,
    this._equitiTraderNavigation,
  ) : super(
        MarketOrderState(
          accountNumber: args.accountNumber,
          marginInformation: args.marginInformation,
          symbolQuoteModel: args.symbolQuoteModel,
          symbolCode: args.symbol,
          tradeType: args.tradeType,
          openPrice: 0,
          orderSizeState:
              args.marginInformation == null
                  ? TradeComponentState.inactive()
                  : TradeComponentState.success(args.marginInformation!.minLot),
          takeProfitState: TradeComponentState.inactive(),
          stopLossState: TradeComponentState.inactive(),
          marginAllocationState:
              args.marginInformation == null
                  ? TradeComponentState.inactive()
                  : _validateMarginAllocationUseCase(
                    args.marginInformation!.requiredMarginPercentage,
                  )
                  ? TradeComponentState.success(null)
                  : TradeComponentState.error(
                    TradeError.marginAllocation(
                      message: 'Margin allocation is not valid',
                    ),
                  ),
        ),
      ) {
    on<MarketOrderEvent>(
      (event, emit) => switch (event) {
        _MarketOrderSizeChanged val => _orderSizeChanged(emit, val),
        _TakeProfitChanged val => _takeProfitChanged(emit, val),
        _StopLossChanged val => _stopLossChanged(emit, val),
        _ValuesChanged val => _handleValueChanged(emit, val),
        _Submit val => _submit(emit, val),
        _GoToPortfolioEvent() => _goToPortfolio(),
      },
      transformer: sequential(),
    );
  }
  final ValidateMarginAllocationUseCase _validateMarginAllocationUseCase;
  final CreateTradeUseCase _createTradeUseCase;
  final CalculateVolumeUseCase _calculateVolumeUseCase;
  final EquitiTraderNavigation _equitiTraderNavigation;

  TradeComponentState<void, TradeError> _validateMarginAllocation(
    MarketOrderArgs args,
  ) {
    final marginInformation = args.marginInformation;
    if (marginInformation == null) return TradeComponentState.inactive();
    return _validateMarginAllocationUseCase(
          marginInformation.requiredMarginPercentage,
        )
        ? TradeComponentState<void, TradeError>.success(null)
        : TradeComponentState.error(
          TradeError.marginAllocation(
            message: 'Margin allocation is not valid',
          ),
        );
  }

  FutureOr<void> _handleValueChanged(
    Emitter<MarketOrderState> emit,
    _ValuesChanged event,
  ) {
    emit(
      state.copyWith(
        marginAllocationState: TradeComponentState<void, TradeError>.loading(),
      ),
    );
    final marginAllocationState = _validateMarginAllocation(event.args);
    emit(
      state.copyWith(
        marginInformation: event.args.marginInformation,
        symbolQuoteModel: event.args.symbolQuoteModel,
        tradeType: event.args.tradeType,
        marginAllocationState: marginAllocationState,
      ),
    );
  }

  FutureOr<void> _takeProfitChanged(
    Emitter<MarketOrderState> emit,
    _TakeProfitChanged event,
  ) {
    emit(state.copyWith(takeProfitState: event.state));
  }

  FutureOr<void> _orderSizeChanged(
    Emitter<MarketOrderState> emit,
    _MarketOrderSizeChanged event,
  ) {
    emit(state.copyWith(orderSizeState: event.state));
  }

  FutureOr<void> _stopLossChanged(
    Emitter<MarketOrderState> emit,
    _StopLossChanged event,
  ) {
    emit(state.copyWith(stopLossState: event.state));
  }

  FutureOr<void> _submit(Emitter<MarketOrderState> emit, _Submit _) async {
    emit(state.copyWith(currentState: MarketOrderProcessState.placingOrder()));
    final model = CreateTradeRequestModel(
      accountNumber: state.accountNumber,
      symbolCode: state.symbolCode,
      volume: _calculateVolumeUseCase(state.orderSizeState.value),
      tradeType: state.tradeType!,
      takeProfit: state.takeProfit,
      stopLoss: state.stopLoss,
    );

    final result = await _createTradeUseCase(model).run();
    result.fold(
      (exception) {
        if (exception is PositionsAndOrdersException &&
            exception.errors.containsKey("IsMarketOpen")) {
          final isMarketOpenError =
              exception.errors["IsMarketOpen"]?.firstOrNull;
          if (isMarketOpenError?.toLowerCase() == "false") {
            emit(
              state.copyWith(
                currentState: MarketOrderProcessState.marketClosed(),
              ),
            );
            return;
          }
        } else
          emit(
            state.copyWith(currentState: MarketOrderProcessState.orderError()),
          );

        addError(exception);
      },
      (success) {
        if (success.isSuccess) {
          emit(
            state.copyWith(
              openPrice: success.openPrice,
              currentState: MarketOrderProcessState.orderSuccess(),
            ),
          );
        } else {
          emit(
            state.copyWith(
              currentState: MarketOrderProcessState.orderError(
                errorCode: success.errorMessage,
              ),
            ),
          );
        }
      },
    );
  }

  FutureOr<void> _goToPortfolio() {
    _equitiTraderNavigation.navigateToPortfolio(
      result: TradeConfirmationResult.viewTrades,
    );
  }

  // In your MarketOrderBloc:
}
