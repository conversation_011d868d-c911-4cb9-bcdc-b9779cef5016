import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_price_widget.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_price_bloc.freezed.dart';
part 'order_price_event.dart';
part 'order_price_state.dart';

class OrderPriceBloc extends Bloc<OrderPriceEvent, OrderPriceState> {
  OrderPriceBloc(
    SubscribeToSymbolQuotesUseCase subscribeToProductQuotesUseCase,
    OrderPriceArgs args,
  ) : super(
        OrderPriceState(
          currentState:
              args.isDisabled
                  ? TradeComponentState.inactive()
                  : TradeComponentState.loading(),
        ),
      ) {
    on<OrderPriceEvent>(
      (event, emit) => switch (event) {
        _UpdateOrderPrice value => _updateOrderPrice(value, emit),
        _MarketPriceChanged value => _marketPriceChanged(value, emit),
      },
      transformer: sequential(),
    );

    add(
      OrderPriceEvent.updateOrderPrice(
        inputPrice: args.initialPrice.toString(),
      ),
    );
  }

  FutureOr<void> _updateOrderPrice(
    _UpdateOrderPrice event,
    Emitter<OrderPriceState> emit,
  ) {
    final orderPrice = double.tryParse(event.inputPrice);
    if (orderPrice == null || orderPrice < 0) {
      emit(
        state.copyWith(
          currentState: const TradeComponentState.error(
            OrderPriceErrorCode.invalid,
          ),
        ),
      );
    } else {
      emit(
        state.copyWith(currentState: TradeComponentState.success(orderPrice)),
      );
    }
  }

  void _marketPriceChanged(
    _MarketPriceChanged value,
    Emitter<OrderPriceState> emit,
  ) {
    final orderPrice = double.tryParse(value.inputPrice);

    if (orderPrice == null) {
      emit(
        state.copyWith(
          currentState: const TradeComponentState.error(
            OrderPriceErrorCode.invalid,
          ),
        ),
      );
      return;
    }
    if ((orderPrice > value.marketPrice)) {
      emit(
        state.copyWith(
          isBuyStopReached: true,
          isBuyLimitReached: false,
          isSellLimitReached: true,
          isSellStopReached: false,
        ),
      );
    } else if (orderPrice < value.marketPrice) {
      emit(
        state.copyWith(
          isBuyStopReached: false,
          isBuyLimitReached: true,
          isSellLimitReached: false,
          isSellStopReached: true,
        ),
      );
    }
  }
}
