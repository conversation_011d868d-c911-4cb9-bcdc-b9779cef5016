import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_trade/bloc/modify_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/modify_trade_content.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

Future<void> modifyTradeBottomSheet(
  BuildContext context,
  PositionModel positionModel,
) {
  final localization = EquitiLocalization.of(context);

  return DuploSheet.showModalSheetV2(
    context,
    appBar: DuploAppBar(
      title: EquitiLocalization.of(context).trader_modifiyTrade,
      automaticallyImplyLeading: false,
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    ),
    builder:
        (child) => BlocProvider(
          create:
              (blocProviderContext) =>
                  diContainer<ModifyTradeBloc>(param1: positionModel),
          child: child,
        ),
    content: ModifyTradeContent(trade: positionModel),
    bottomBar: BlocBuilder<ModifyTradeBloc, ModifyTradeState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return Padding(
          padding: EdgeInsets.all(16),
          child: DuploButton.primaryBold(
            isLoading: switch (state.currentState) {
              ModifyPlacingTradeProcessState() => true,
              _ => false,
            },
            title: localization.trader_save,
            loadingText: localization.trader_saving,
            isDisabled: switch (state.currentState) {
              ModifyPlacingTradeProcessState() => false,
              _ =>
                !(state.isValid() &&
                    (state.stopLoss != positionModel.stopLoss ||
                        state.takeProfit != positionModel.takeProfit)),
            },
            onTap: () {
              blocBuilderContext.read<ModifyTradeBloc>().add(
                const ModifyTradeEvent.submit(),
              );
            },
          ),
        );
      },
    ),
  );
}
