import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_trade/bloc/modify_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_limit_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ModifyTradeContent extends StatelessWidget {
  final PositionModel trade;
  const ModifyTradeContent({super.key, required this.trade});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<ModifyTradeBloc, ModifyTradeState>(
      listenWhen:
          (previous, current) => previous.currentState != current.currentState,
      listener: (listenerContext, state) {
        if (state.currentState is ModifyTradeSuccessProcessState) {
          DuploToast().showToastMessage(
            context: listenerContext,
            widget: DuploToastTrade(
              titleMessage:
                  EquitiLocalization.of(
                    listenerContext,
                  ).trader_tradeModificationSaved,
              trade: TradeToastModel(
                symbolImage: state.positionModel.productLogoUrl,
                symbolName: state.positionModel.symbol,
                lotSize: EquitiFormatter.formatNumber(
                  value: state.positionModel.lotSize,
                  locale: locale,
                ),
                price: EquitiFormatter.decimalPatternDigits(
                  value: state.positionModel.currentPrice,
                  digits: trade.digits,
                  locale: locale,
                ),
                type:
                    state.positionModel.positionType == TradeType.buy
                        ? TradeToastType.buy
                        : TradeToastType.sell,
              ),
              type: ToastMessageType.success,
              onLeadingAction: () {
                DuploToast().hidesToastMessage();
                if (listenerContext.mounted) Navigator.pop(listenerContext);
              },
            ),
          );
          Navigator.pop(listenerContext);
        } else if (state.currentState is ModifyTradeMarketClosedProcessState) {
          final toast = DuploToast();
          toast.showToastMessage(
            context: context,
            widget: DuploToastMessage(
              titleMessage: localization.trader_marketIsClosed,
              descriptionMessage:
                  localization.trader_modifyTrade_marketIsClosedDescription,
              messageType: ToastMessageType.error,
              onLeadingAction: () => toast.hidesToastMessage(),
            ),
          );
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return switch (state.currentState) {
          ModifyTradeLoadingProcessState() => ListView.builder(
            itemCount: 10,
            itemBuilder: (ctx, index) {
              return DuploShimmerListItem(hasLeading: false, hasTrailing: true);
            },
          ),
          ModifyTradeDisconnectedProcessState() => Center(
            child: Text(localization.trader_somethingWentWrong),
          ),
          _ => Container(
            color: theme.background.bgSecondary,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.symmetric(
                    vertical: 22.0,
                    horizontal: 16.0,
                  ),
                  child: _ModifyTradeForm(trade: trade),
                ),
              ],
            ),
          ),
        };
      },
    );
  }
}

class _ModifyTradeForm extends StatelessWidget {
  const _ModifyTradeForm({required this.trade});

  final PositionModel trade;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocBuilder<ModifyTradeBloc, ModifyTradeState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return Card(
          elevation: 0,
          color: theme.background.bgPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
            side: BorderSide(color: theme.border.borderSecondary, width: 1.0),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                OrderLimitWidget(
                  orderType: OrderType.marketOrder,
                  isEnabled: trade.takeProfit != 0.0,
                  orderLimitType: OrderLimitType.takeProfit,
                  digits: trade.digits,
                  tradeType: trade.positionType,
                  pipValue: state.marginInformation!.pipInformation.pipValue,
                  pipMultipler:
                      state.marginInformation!.pipInformation.pipMultipler,
                  pipSize: state.marginInformation!.pipInformation.onePip,
                  currentPrice: state.positionModel.currentPrice,
                  initialPrice: trade.takeProfit,
                  methodOrder: [
                    MethodTypeEnum.distance,
                    MethodTypeEnum.price,
                    MethodTypeEnum.profitOrLoss,
                  ],
                  onOrderLimitStateChanged:
                      (orderLimitState) =>
                          blocBuilderContext.read<ModifyTradeBloc>().add(
                            ModifyTradeEvent.takeProfitChanged(orderLimitState),
                          ),
                ),
                const SizedBox(height: 8.0),
                Divider(color: theme.border.borderSecondary),
                const SizedBox(height: 8.0),
                OrderLimitWidget(
                  orderType: OrderType.marketOrder,
                  isEnabled: trade.stopLoss != 0.0,
                  orderLimitType: OrderLimitType.stopLoss,
                  digits: trade.digits,
                  tradeType: trade.positionType,
                  pipValue: state.marginInformation!.pipInformation.pipValue,
                  pipMultipler:
                      state.marginInformation!.pipInformation.pipMultipler,
                  pipSize: state.marginInformation!.pipInformation.onePip,
                  currentPrice: state.positionModel.currentPrice,
                  initialPrice: trade.stopLoss,
                  methodOrder: [
                    MethodTypeEnum.distance,
                    MethodTypeEnum.price,
                    MethodTypeEnum.profitOrLoss,
                  ],
                  onOrderLimitStateChanged: (orderLimitState) {
                    blocBuilderContext.read<ModifyTradeBloc>().add(
                      ModifyTradeEvent.stopLossChanged(orderLimitState),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
