import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

//TODO(Danya): replace with duplo component when its created
class BuySellLimitContainer extends StatelessWidget {
  final TradeType tradeType;
  final ContainerType containerType;
  const BuySellLimitContainer({
    super.key,
    required this.tradeType,
    required this.containerType,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color:
            tradeType == TradeType.buy
                ? theme.utility.utilitySuccess50
                : theme.utility.utilityError50,
        border: Border.all(
          color:
              tradeType == TradeType.buy
                  ? theme.utility.utilitySuccess200
                  : theme.utility.utilityError200,
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      child: DuploText(
        text:
            containerType == ContainerType.buySellLimit
                ? tradeType == TradeType.buy
                    ? localization.trader_buyLimit
                    : localization.trader_sellLimit
                : tradeType == TradeType.buy
                ? localization.trader_buyStop
                : localization.trader_sellStop,
        style: context.duploTextStyles.textSm,
        color:
            tradeType == TradeType.buy
                ? theme.utility.utilitySuccess700
                : theme.utility.utilityError700,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

enum ContainerType { buySellLimit, buySellStop }
