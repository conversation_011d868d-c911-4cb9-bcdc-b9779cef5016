import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/data/api/trade_account_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/account_balance_hub_response.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_trading_account_balance_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'insights_bloc.freezed.dart';
part 'insights_event.dart';
part 'insights_state.dart';

class InsightsBloc extends Bloc<InsightsEvent, InsightsState>
    with DisposableMixin {
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final SubscribeToTradingAccountBalanceUseCase _getTradingAccountUseCase;
  final UpdateTradingAccountBalanceHubUseCase
  _updateTradingAccountBalanceHubUseCase;
  InsightsBloc(
    this._getSelectedAccountUseCase,
    this._getTradingAccountUseCase,
    this._updateTradingAccountBalanceHubUseCase,
  ) : super(InsightsState()) {
    on<InsightsEvent>((event, emit) async {
      if (event is _SubscribeToMarginAllocations) {
        await _subscribeToMarginAllocations(emit);
      } else if (event is _UpdateMarginAllocations) {
        _updateMarginAllocations(event.eventType);
      }
    }, transformer: droppable());

    on<_ProcessMarginAllocations>((event, emit) {
      _processMarginAllocations(event, emit);
    }, transformer: restartable());
    on<_ProcessError>((_, emit) {
      _processError(emit);
    }, transformer: restartable());
  }

  Future<void> _subscribeToMarginAllocations(
    Emitter<InsightsState> emit,
  ) async {
    final result =
        await TaskEither<
          Exception,
          (TradingAccountModel, Stream<AccountBalanceHubResponse?>)
        >.Do(($) async {
          final client = _getSelectedAccountUseCase();
          if (client == null) {
            throw Exception('No account found');
          }
          final getTradingAccountResult = await $(
            _getTradingAccountUseCase(
              [client.accountNumber],
              subscriberId: '${InsightsBloc}_$hashCode',
              eventType: TradingSocketEvent.accountBalance.register,
            ),
          );

          return (client, getTradingAccountResult);
        }).run();

    return result.fold(
      (error) {
        addError(error);
        emit(
          state.copyWith(insightsProcessState: InsightsProcessState.error()),
        );
      },
      (value) {
        final client = value.$1;
        final stream = value.$2;
        emit(
          state.copyWith(
            insightsProcessState: InsightsProcessState.connected(),
          ),
        );
        addSubscription(
          stream.distinct().listen(
            (accountBalanceResponse) {
              add(
                InsightsEvent.processMarginAllocations(
                  accountBalanceResponse,
                  client,
                ),
              );
            },
            onError: (Object? error) {
              add(InsightsEvent.processError(error));
            },
          ),
        );
      },
    );
  }

  FutureOr<void> _updateMarginAllocations(EventType eventType) {
    try {
      _updateTradingAccountBalanceHubUseCase(eventType: eventType);
    } catch (e) {
      addError(e);
    }
  }

  void _processMarginAllocations(
    _ProcessMarginAllocations event,
    Emitter<InsightsState> emit,
  ) {
    final accountBalanceResponse = event.accountBalanceHubResponse;
    final account = event.account;
    if (accountBalanceResponse == null ||
        accountBalanceResponse.account.productsMarginAllocation.isEmpty) {
      emit(
        state.copyWith(
          insightsProcessState: InsightsProcessState.empty(),
          marginAllocationUpdates: null,
        ),
      );
      return;
    }
    emit(
      state.copyWith(
        insightsProcessState: InsightsProcessState.success(),
        marginAllocationUpdates: accountBalanceResponse.account,
        accountCurrency: account.homeCurrency,
      ),
    );
  }

  void _processError(Emitter<InsightsState> emit) {
    emit(state.copyWith(insightsProcessState: InsightsProcessState.error()));
  }
}
