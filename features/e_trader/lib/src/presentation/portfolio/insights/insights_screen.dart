import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as assetsTrader;
import 'package:e_trader/src/data/api/category_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/presentation/portfolio/insights/bloc/insights_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/insights/insights_shimmer.dart';
import 'package:e_trader/src/presentation/portfolio/insights/margin_allocation.dart';
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:visibility_detector/visibility_detector.dart';

class InsightsScreen extends StatefulWidget {
  const InsightsScreen();

  @override
  State<InsightsScreen> createState() => _InsightsScreenContentState();
}

class _InsightsScreenContentState extends State<InsightsScreen>
    with PerformanceObserverMixin {
  bool _isSubscribed = false;
  bool _hasRegistered = false;

  @override
  Widget build(BuildContext _) {
    return VisibilityDetector(
      key: const Key('insights_screen'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0) {
          if (!_hasRegistered) {
            context.read<InsightsBloc>().add(
              InsightsEvent.subscribeToMarginAllocations(),
            );
            _hasRegistered = true;
          }
        } else {
          if (_hasRegistered) {
            _unsubscribe();
            _hasRegistered = false;
          }
        }
      },
      //Don't remove this SizedBox as it is required for the visibility detector to work
      child: SizedBox(
        child: CustomScrollView(
          slivers: [
            BlocConsumer<InsightsBloc, InsightsState>(
              listenWhen:
                  (previous, current) =>
                      previous.insightsProcessState !=
                          current.insightsProcessState &&
                      current.insightsProcessState
                          is InsightsProcessStateConnected,
              listener: (listenerContext, state) => _subscribe(),
              buildWhen:
                  (previous, current) =>
                      previous.insightsProcessState !=
                          current.insightsProcessState ||
                      previous.marginAllocationUpdates !=
                          current.marginAllocationUpdates,
              builder:
                  (
                    insightsBuilderContext,
                    insightsState,
                  ) => switch (insightsState.insightsProcessState) {
                    InsightsProcessStateSuccess() ||
                    InsightsProcessStateConnected() => () {
                      return BlocSelector<
                        CategoriesBloc,
                        CategoriesState,
                        List<CategoryModel>
                      >(
                        selector:
                            (state) =>
                                state.categories
                                    .where(
                                      (category) => category.id != 'watchlist',
                                    )
                                    .toList(),
                        builder: (buildContext, categories) {
                          if (categories.isEmpty) {
                            return InsightsShimmer();
                          }
                          if (insightsState.insightsProcessState
                                  is InsightsProcessStateEmpty ||
                              insightsState.marginAllocationUpdates == null)
                            return const _NoInsightsWidget();
                          return SliverToBoxAdapter(
                            child: MarginAllocation(categories: categories),
                          );
                        },
                      );
                    }(),
                    InsightsProcessStateLoading() => const InsightsShimmer(),
                    InsightsProcessStateEmpty() => const _NoInsightsWidget(),
                    InsightsProcessStateError() => SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.empty(
                        svgImage:
                            assetsTrader.Assets.images.insightsError.svg(),
                        title:
                            EquitiLocalization.of(
                              insightsBuilderContext,
                            ).trader_insightsSomethingWentWrong,
                        description:
                            EquitiLocalization.of(
                              insightsBuilderContext,
                            ).trader_insightsErrorDescription,
                      ),
                    ),
                  },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
  }

  void _subscribe() {
    if (!_isSubscribed) {
      if (mounted)
        context.read<InsightsBloc>().add(
          InsightsEvent.updateMarginAllocations(
            TradingSocketEvent.accountBalance.subscribe,
          ),
        );
      _isSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_isSubscribed) {
      try {
        diContainer<UpdateTradingAccountBalanceHubUseCase>().call(
          eventType: TradingSocketEvent.accountBalance.unsubscribe,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _isSubscribed = false;
    }
  }
}

class _NoInsightsWidget extends StatelessWidget {
  const _NoInsightsWidget();

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      hasScrollBody: false,
      child: EmptyOrErrorStateComponent.empty(
        svgImage: assetsTrader.Assets.images.portfolioEmptyInsightList.svg(),
        title: EquitiLocalization.of(context).trader_noInsightsYet,
        description:
            EquitiLocalization.of(context).trader_noInsightsDescription,
      ),
    );
  }
}
