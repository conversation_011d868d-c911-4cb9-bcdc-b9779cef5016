import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/category_model.dart';
import 'package:e_trader/src/data/api/products_margin_allocation_model.dart';
import 'package:e_trader/src/presentation/portfolio/insights/bloc/insights_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class MarginAllocation extends StatelessWidget {
  const MarginAllocation({super.key, required this.categories});

  final List<CategoryModel> categories;

  @override
  Widget build(BuildContext context) {
    final insightsState = context.read<InsightsBloc>().state;
    final categoryColors = mapCategoryColors(context);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: context.duploTheme.border.borderSecondary),
        color: context.duploTheme.background.bgPrimary,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.all(16),
        physics: NeverScrollableScrollPhysics(),
        itemCount: categories.length,
        itemBuilder: (listViewContext, index) {
          final categoriesWithMargin =
              categories.map((category) {
                final matchingProduct = insightsState
                    .marginAllocationUpdates
                    ?.productsMarginAllocation
                    .firstWhere(
                      (product) => product.category == category.name,
                      orElse:
                          () => ProductsMarginAllocationModel(
                            category: category.name,
                          ),
                    );

                // Calculate profit proportionally based on margin allocation percentage
                final marginAllocationPercentage =
                    matchingProduct?.marginAllocationPercentage ?? 0.0;

                return {
                  'category': category,
                  'marginAllocation': marginAllocationPercentage,
                  'amount': matchingProduct?.marginAllocation ?? 0.0,
                };
              }).toList();

          categoriesWithMargin.sort(
            (a, b) => (b['marginAllocation'] as double).compareTo(
              a['marginAllocation'] as double,
            ),
          );

          final sortedItem = categoriesWithMargin.elementAtOrNull(index);
          final category = sortedItem?['category'] as CategoryModel;
          final categoryName = category.name;
          final marginAllocation = sortedItem?['marginAllocation'] as double;
          final amount = sortedItem?['amount'] as double;

          return categoryAllocation(
            context: listViewContext,
            categoryName: categoryName,
            color:
                categoryColors[categoryName] ??
                listViewContext.duploTheme.utility.utilityGray700,
            amount: amount,
            marginAllocation: marginAllocation,
            currency: insightsState.accountCurrency,
          );
        },
        separatorBuilder:
            (seperatorContext, index) =>
                index == categories.length - 1 ? SizedBox.shrink() : Divider(),
      ),
    );
  }

  Map<String, Color> mapCategoryColors(BuildContext context) {
    final colors = getCategoriesColors(context);
    Map<String, Color> categoryColorMap = {};

    for (int i = 0; i < categories.length; i++) {
      categoryColorMap[categories[i].name] =
          colors.elementAtOrNull(i) ??
          context.duploTheme.utility.utilityGray700;
    }

    return categoryColorMap;
  }

  List<Color> getCategoriesColors(BuildContext context) {
    List<Color> marginAllocationColors = [
      context.duploTheme.utility.utilityBrand900,
      context.duploTheme.utility.utilityBrand800,
      context.duploTheme.utility.utilityBrand700,
      context.duploTheme.utility.utilityBrand500,
      context.duploTheme.utility.utilityBrand400,
      context.duploTheme.utility.utilityBrand200,
    ];
    return marginAllocationColors;
  }

  Padding categoryAllocation({
    required Color color,
    required double amount,
    required String currency,
    required String categoryName,
    required BuildContext context,
    required double marginAllocation,
  }) => Padding(
    padding: const EdgeInsetsDirectional.symmetric(vertical: 12),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: DuploText(
                text: categoryName,
                style: context.duploTextStyles.textSm,
                fontWeight: DuploFontWeight.semiBold,
                color: context.duploTheme.text.textTertiary,
              ),
            ),
            Flexible(
              child: _MarginPercentageText(percentage: marginAllocation),
            ),
          ],
        ),
        SizedBox(height: 6),
        LinearProgressIndicator(
          minHeight: 8,
          value: marginAllocation / 100,
          borderRadius: BorderRadius.circular(4),
          valueColor: AlwaysStoppedAnimation(color),
          backgroundColor: context.duploTheme.utility.utilityGray200,
        ),
        SizedBox(height: 6),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Flexible(
              child: _MarginAmountText(amount: amount, currency: currency),
            ),
          ],
        ),
      ],
    ),
  );
}

class _MarginPercentageText extends StatelessWidget {
  final double percentage;

  const _MarginPercentageText({required this.percentage});

  @override
  Widget build(BuildContext context) {
    final duploTextStyle = context.duploTextStyles;
    final locale = Localizations.localeOf(context).toString();
    final formattedPercentage = EquitiFormatter.decimalPatternDigits(
      value: percentage,
      digits: 2,
      locale: locale,
    );

    final parts = formattedPercentage.split('.');
    final wholePart = parts.elementAtOrNull(0) ?? '';
    final decimalPart = parts.elementAtOrNull(1) ?? '';

    return DuploText.rich(
      spans: [
        DuploTextSpan(
          text: wholePart,
          style: duploTextStyle.textMd,
          fontWeight: DuploFontWeight.bold,
          color: context.duploTheme.text.textTertiary,
        ),
        DuploTextSpan(
          text: '.$decimalPart%',
          style: duploTextStyle.textSm,
          fontWeight: DuploFontWeight.bold,
          color: context.duploTheme.text.textTertiary,
        ),
      ],
    );
  }
}

class _MarginAmountText extends StatelessWidget {
  final double amount;
  final String currency;

  const _MarginAmountText({required this.amount, required this.currency});

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final formattedAmount = EquitiFormatter.decimalPatternDigits(
      value: amount,
      digits: 2,
      locale: locale,
    );

    final parts = formattedAmount.split('.');
    final wholePart = parts.elementAtOrNull(0) ?? '';
    final decimalPart = parts.elementAtOrNull(1) ?? '';

    return DuploText.rich(
      spans: [
        DuploTextSpan(
          text: wholePart,
          style: context.duploTextStyles.textXs,
          fontWeight: DuploFontWeight.medium,
          color: context.duploTheme.text.textQuaternary,
        ),
        DuploTextSpan(
          text: '.$decimalPart $currency',
          style: context.duploTextStyles.textXxs,
          fontWeight: DuploFontWeight.medium,
          color: context.duploTheme.text.textQuaternary,
        ),
      ],
    );
  }
}
