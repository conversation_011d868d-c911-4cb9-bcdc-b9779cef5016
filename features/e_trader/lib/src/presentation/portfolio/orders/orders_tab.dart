import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/get_trade_size_from_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_order_hub_use_case.dart';
import 'package:e_trader/src/presentation/create_trade/modify_pending_order_bottom_sheet.dart';
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/orders/widgets/orders_loading.dart';
import 'package:e_trader/src/presentation/positions_and_trades/order_list_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:visibility_detector/visibility_detector.dart';

class OrdersTab extends StatefulWidget {
  const OrdersTab({this.symbolName = ''});
  final String symbolName;

  @override
  State<OrdersTab> createState() => _OrdersTabContentState();
}

class _OrdersTabContentState extends State<OrdersTab>
    with PerformanceObserverMixin {
  bool _isSubscribed = false;
  bool _hasRegistered = false;

  void _subscribe() {
    if (!_isSubscribed) {
      if (mounted)
        context.read<OrdersBloc>().add(
          OrdersEvent.updateOrders(TradingSocketEvent.orders.subscribe),
        );
      _isSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_isSubscribed) {
      try {
        diContainer<UpdateOrderHubUseCase>().call(
          eventType: TradingSocketEvent.orders.unsubscribe,
          symbolName: widget.symbolName,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _isSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return VisibilityDetector(
      key: const ValueKey('OrdersTab'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0) {
          if (!_hasRegistered) {
            context.read<OrdersBloc>().add(OrdersEvent.loadOrders());
            _hasRegistered = true;
          }
        } else {
          if (_hasRegistered) {
            _unsubscribe();
            _hasRegistered = false;
          }
        }
      },
      child: Container(
        child: CustomScrollView(
          slivers: [
            BlocConsumer<OrdersBloc, OrdersState>(
              listenWhen:
                  (previous, current) =>
                      previous.processState != current.processState &&
                      current.processState is OrdersConnected,
              listener: (listenerContext, state) => _subscribe(),
              buildWhen: (previous, current) => previous != current,
              builder:
                  (blocContext, state) => switch (state.processState) {
                    OrdersLoadingState() => const OrdersLoading(),
                    OrdersError() => SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.error(
                        description: localization.trader_errorDescription,
                        title: localization.trader_somethingWentWrong,
                        svgImage: trader.Assets.images.bug.svg(),
                        raiseIssueText: localization.trader_raiseTicket,
                        onRaiseIssue: () {
                          debugPrint("Raise ticket");
                        },
                        retryButtonText: localization.trader_reload,
                        onTapRetry:
                            () => blocContext.read<OrdersBloc>().add(
                              OrdersEvent.loadOrders(),
                            ),
                      ),
                    ),
                    OrdersSuccessState() || OrdersConnected() => () {
                      if (state.orders.isEmpty) {
                        return SliverFillRemaining(
                          hasScrollBody: false,
                          child: EmptyOrErrorStateComponent.empty(
                            svgImage:
                                trader.Assets.images.portfolioEmptyOrderList
                                    .svg(),
                            title: localization.trader_noOrders,
                            description:
                                localization.trader_noActiveOrdersDescription,
                          ),
                        );
                      } else
                        return SliverMainAxisGroup(
                          slivers: [
                            SliverList.builder(
                              itemCount: state.orders.values.length,
                              itemBuilder: (ctx, index) {
                                final order =
                                    state.orders.values.elementAtOrNull(index)!;
                                return OrderListTile(
                                  productIconURL: order.productLogoUrl,
                                  productName: order.symbol,
                                  lots: diContainer<
                                        GetTradeSizeFromVolumeUseCase
                                      >()
                                      .call(order.volume),
                                  tradeType: order.tradeType,
                                  currentPrice: order.currentPrice,
                                  priceChange: order.priceChange,
                                  orderPrice: order.openPrice,
                                  digits: order.digits,
                                  tpValue: order.takeProfit,
                                  slValue: order.stopLoss,
                                  entryOrderType: order.entryOrderType,
                                  onTap: () {
                                    modifyPendingOrderBottomSheet(
                                      context,
                                      order,
                                    );
                                  },
                                );
                              },
                            ),
                          ],
                        );
                    }(),
                  },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
  }
}
