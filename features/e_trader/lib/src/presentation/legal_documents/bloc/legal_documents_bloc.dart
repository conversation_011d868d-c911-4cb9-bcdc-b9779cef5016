import 'package:e_trader/src/domain/model/legal_document.dart';
import 'package:e_trader/src/domain/usecase/get_legal_documents_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';

part 'legal_documents_bloc.freezed.dart';
part 'legal_documents_event.dart';
part 'legal_documents_state.dart';

class LegalDocumentsBloc
    extends Bloc<LegalDocumentsEvent, LegalDocumentsState> {
  LegalDocumentsBloc(this._getLegalDocumentsUseCase, this._logger)
    : super(const LegalDocumentsState()) {
    on<_OnGetLegalDocuments>(_onGetLegalDocuments);
    add(LegalDocumentsEvent.onGetLegalDocuments());
  }

  final GetLegalDocumentsUseCase _getLegalDocumentsUseCase;
  final LoggerBase _logger;

  Future<void> _onGetLegalDocuments(
    _OnGetLegalDocuments event,
    Emitter<LegalDocumentsState> emit,
  ) async {
    // await _startPerformanceMonitoring();
    final localizedLegalDocumentsResponse =
        await _getLegalDocumentsUseCase().run();

    localizedLegalDocumentsResponse.fold(
      (exception) {
        addError(exception);
        emit(state.copyWith(currentState: LegalDocumentProcessState.error()));
      },
      (localizedDocumentsContent) {
        emit(
          state.copyWith(
            legalDocumentContent: localizedDocumentsContent,
            currentState: LegalDocumentProcessState.success(),
          ),
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    super.addError(error, stackTrace);
    _logger.logError(error, stackTrace: stackTrace);
  }

  // Future<void> _startPerformanceMonitoring() async {
  //   await locator<FirebasePerformanceUtil>().startEvent(
  //     PerformanceEventType.screen,
  //     RoutesConstants.legalDocumentsScreen,
  //   );
  // }
}
