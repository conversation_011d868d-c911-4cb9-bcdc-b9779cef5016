import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/legal_documents/bloc/legal_documents_bloc.dart';
import 'package:e_trader/src/presentation/legal_documents/widgets/new_legal_document_tile_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class LegalDocumentsScreen extends StatelessWidget {
  const LegalDocumentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create: (_) => diContainer<LegalDocumentsBloc>(),
      child: Scaffold(
        appBar: DuploAppBar(title: localization.trader_legalDocuments),
        body: BlocBuilder<LegalDocumentsBloc, LegalDocumentsState>(
          buildWhen:
              (previous, current) =>
                  previous.currentState != current.currentState ||
                  !mapEquals(
                    previous.legalDocumentContent,
                    current.legalDocumentContent,
                  ),
          builder: (blocBuilderContext, state) {
            return switch (state.currentState) {
              LegalDocumentLoadingState() => Center(
                child: const CircularProgressIndicator.adaptive(),
              ),
              LegalDocumentErrorState() => Center(
                child: DuploText(
                  text: localization.trader_somethingWentWrong,
                  style: duploTextStyles.textSm,
                  color: theme.text.textPrimary,
                ),
              ),
              _ => () {
                if (state.legalDocumentContent?.isEmpty == true) {
                  return Center(
                    child: DuploText(
                      text: localization.trader_noLegalDocumentsFound,
                      style: duploTextStyles.textSm,
                      color: theme.text.textPrimary,
                    ),
                  );
                }
                return SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 16, bottom: 45),
                    child: Container(
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 8,
                        top: 16,
                        bottom: 16.0,
                      ),
                      margin: const EdgeInsets.only(left: 16, right: 16),
                      height: (state.legalDocumentContent?.length ?? 0) * 54.0,
                      child: ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: state.legalDocumentContent?.length ?? 0,
                        itemBuilder: (BuildContext listViewContext, int index) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 30),
                            child: InkWell(
                              onTap: ()
                              // async
                              {
                                debugPrint(
                                  'URLs: ${state.legalDocumentContent ?? "null"}',
                                );
                                // await launchUrl(
                                //   mode: Platform.isAndroid
                                //       ? LaunchMode.externalApplication
                                //       : LaunchMode.inAppWebView,
                                //   Uri.parse(state.legalDocumentsModel!.values
                                //           .toList()[index]
                                //           .documentUrl ??
                                //       ''),
                                // );
                              },
                              child: NewLegalDocumentTileView(
                                title:
                                    state.legalDocumentContent!.keys
                                        .toList()
                                        .elementAtOrNull(index) ??
                                    "null",
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              }(),
            };
          },
        ),
      ),
    );
  }
}
