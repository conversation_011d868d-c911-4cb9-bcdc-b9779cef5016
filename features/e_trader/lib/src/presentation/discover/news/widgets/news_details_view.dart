import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/tag_list.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:prelude/prelude.dart';

class NewsDetailsView extends StatelessWidget {
  const NewsDetailsView({super.key, required this.newsItemDetails});
  final NewsItemDetails newsItemDetails;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = DuploTextStyles.of(context);
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              newsItemDetails.symbols.length == 1
                  ? Row(
                    children: [
                      Platform.environment.containsKey('FLUTTER_TEST')
                          ? CircleAvatar(backgroundColor: Colors.red)
                          : SvgPicture.network(
                            newsItemDetails.id,
                            width: 29,
                            height: 29,
                            placeholderBuilder:
                                (_) => CircleAvatar(
                                  radius: 15,
                                  backgroundColor: theme.border.borderSecondary,
                                ),
                          ),
                      SizedBox(width: 8),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: newsItemDetails.symbols.firstOrNull,
                            style: duploTextStyles.textMd,
                            fontWeight: DuploFontWeight.semiBold,
                            color: theme.text.textPrimary,
                          ),
                          DuploText(
                            text: "Apple Inc.",
                            style: duploTextStyles.textXs,
                            fontWeight: DuploFontWeight.regular,
                            color: theme.text.textTertiary,
                          ),
                        ],
                      ),
                    ],
                  )
                  : DuploText(
                    text: newsItemDetails.formattedDate(localization),
                    style: duploTextStyles.textXs,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textQuaternary,
                  ),
              newsItemDetails.source == 'Dow Jones'
                  ? Padding(
                    padding: const EdgeInsets.only(top: 4.0),

                    child: trader.Assets.images.djLogo.svg(),
                  )
                  : DuploText(
                    text: newsItemDetails.source,
                    style: duploTextStyles.textXs,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textPrimary,
                  ),
            ],
          ),
          newsItemDetails.symbols.length == 1
              ? Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: DuploText(
                  text: newsItemDetails.formattedDate(localization),
                  style: duploTextStyles.textXs,
                  fontWeight: DuploFontWeight.medium,
                  color: theme.text.textQuaternary,
                ),
              )
              : SizedBox.shrink(),
          const SizedBox(height: 4),
          Baseline(
            baseline: 0,
            baselineType: TextBaseline.alphabetic,
            child: DuploText(
              text: newsItemDetails.title,
              style: duploTextStyles.textMd,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          TagList(tags: newsItemDetails.tags),
          const SizedBox(height: 24),

          HtmlWidget(
            newsItemDetails.content,
            textStyle: TextStyle(color: theme.foreground.fgSecondary),
          ),
        ],
      ),
    );
  }
}
