import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

class TagList extends StatelessWidget {
  final List<String> tags;
  const TagList({required this.tags});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 4, // Space between tags
      runSpacing: 4, // Space between lines
      children:
          tags.map((tag) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [DuploTagContainer.sm(text: tag)],
            );
          }).toList(),
    );
  }
}
