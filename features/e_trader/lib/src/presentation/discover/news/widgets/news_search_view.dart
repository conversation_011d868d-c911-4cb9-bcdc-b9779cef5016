import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/events_news_local_data_use_case.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_list_item.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/previous_searches_list.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

void showNewsSearchView({
  required BuildContext parentContext,
  required TextEditingController textController,
  required String title,
}) {
  DuploSheet.showModalSheetV2<void>(
    parentContext,
    settings: const RouteSettings(name: 'news_search_view'),
    appBar: DuploAppBar(
      title: EquitiLocalization.of(parentContext).trader_searchNews,
      automaticallyImplyLeading: false,
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(parentContext),
        ),
      ],
    ),
    content: BlocProvider(
      create: (_) => diContainer<NewsBloc>(),
      child: _NewsSearchView(
        parentContext: parentContext,
        textController: textController,
        title: title,
      ),
    ),
  );
}

class _NewsSearchView extends StatefulWidget {
  const _NewsSearchView({
    required this.parentContext,
    required this.textController,
    required this.title,
  });

  final BuildContext parentContext;
  final TextEditingController textController;
  final String title;

  @override
  State<_NewsSearchView> createState() => _NewsSearchViewState();
}

class _NewsSearchViewState extends State<_NewsSearchView> {
  late List<String> _previousSearches;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _previousSearches =
        diContainer<EventsNewsLocalDataUseCase>().getNewsPreviousSearches();
    _scrollController = ScrollController();
  }

  void _onSearchChanged(BuildContext context, String query) {
    context.read<NewsBloc>().add(NewsEvent.searchNews(query: query));
    if (query.isEmpty) {
      setState(() {
        _previousSearches =
            diContainer<EventsNewsLocalDataUseCase>().getNewsPreviousSearches();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = widget.parentContext.duploTheme;
    final localization = EquitiLocalization.of(context);

    return BlocBuilder<NewsBloc, NewsState>(
      buildWhen: (prev, curr) => prev != curr,
      builder: (builderContext, state) {
        return Container(
          decoration: BoxDecoration(
            color: theme.background.bgSecondary,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(10)),
          ),
          height: MediaQuery.sizeOf(context).height * 0.9,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 82,
                width: double.infinity,
                child: Column(
                  children: [
                    Container(
                      color: theme.background.bgPrimary,
                      padding: const EdgeInsets.all(16),
                      child: DuploSearchInputField(
                        key: const Key('search_box_bottom_sheet'),
                        controller: widget.textController,
                        hintText: widget.title,
                        onChanged:
                            (query) => _onSearchChanged(builderContext, query),
                      ),
                    ),
                    Divider(color: theme.border.borderSecondary, height: 1),
                  ],
                ),
              ),
              if (widget.textController.text.length >= 2 &&
                  state.processState is NewsSuccess &&
                  state.count != 0)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                  child: DuploText(
                    text:
                        "${EquitiFormatter.formatNumber(value: state.count, locale: Localizations.localeOf(context).toString())} ${localization.trader_resultsFound}",
                    color: theme.text.textTertiary,
                    style: context.duploTextStyles.textSm,
                  ),
                ),
              if (widget.textController.text.isEmpty)
                Expanded(
                  child: PreviousSearchesList(
                    listOfPreviousSearches: _previousSearches,
                    onTap: (query) {
                      widget.textController.text = query;
                      _onSearchChanged(builderContext, query);
                    },
                  ),
                ),
              if (widget.textController.text.length > 1)
                Expanded(
                  child: PagedView.list(
                    isLoading: switch (state.processState) {
                      NewsLoading() => true,
                      _ => false,
                    },
                    scrollController: _scrollController,
                    loadingBuilder: (ctx) {
                      return widget.textController.text.length < 1
                          ? const SizedBox()
                          : Container(
                            height: MediaQuery.sizeOf(context).height * 0.9,
                            width: double.infinity,
                            child: EventsNewsLoadingView(),
                          );
                    },
                    onFetchData: () {
                      final currentScroll = _scrollController.position.pixels;

                      if (currentScroll != 0) {
                        if (!state.hasReachedMax &&
                            state.processState !=
                                const NewsProcessState.loadingMore()) {
                          builderContext.read<NewsBloc>().add(
                            NewsEvent.searchNews(
                              loadMore: true,
                              query: widget.textController.text,
                            ),
                          );
                        }
                      }
                    },
                    padding: EdgeInsets.zero,
                    itemCount: state.groupedNews.length,
                    separatorBuilder: (ctx, index) => SizedBox(height: 16),
                    emptyBuilder:
                        (ctx) => Container(
                          height: MediaQuery.sizeOf(context).height * 0.7,
                          child: EmptyOrErrorStateComponent.empty(
                            title:
                                EquitiLocalization.of(context).trader_noResults,
                            description:
                                EquitiLocalization.of(
                                  context,
                                ).trader_noResultsDescription,
                            svgImage: trader.Assets.images.emptySearch.svg(
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                        ),
                    errorBuilder:
                        (ctx) => Container(
                          height: MediaQuery.sizeOf(context).height * 0.7,
                          child: EmptyOrErrorStateComponent.error(
                            description:
                                localization
                                    .trader_somethingWentWrongDescription,
                            title: localization.trader_somethingWentWrong,
                            svgImage: trader.Assets.images.bug.svg(),
                            retryButtonText: localization.trader_reload,
                            onTapRetry:
                                () => context.read<NewsBloc>().add(
                                  NewsEvent.searchNews(
                                    query: widget.textController.text,
                                  ),
                                ),
                          ),
                        ),
                    itemBuilder: (ctx, index) {
                      final Object? item = state.groupedNews[index];

                      if (item is NewsItem) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: NewsListItem(newsItemDetails: item.item),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
