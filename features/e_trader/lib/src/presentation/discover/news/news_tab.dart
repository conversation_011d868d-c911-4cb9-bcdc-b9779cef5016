import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_list_item.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_search_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class NewsTab extends StatefulWidget {
  const NewsTab({this.isInMarketDetails = false});
  final bool isInMarketDetails;

  @override
  State<NewsTab> createState() => _NewsTabState();
}

class _NewsTabState extends State<NewsTab> {
  late final TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);
    return CustomScrollView(
      slivers: [
        BlocBuilder<NewsBloc, NewsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            return switch (state.processState) {
              NewsLoading() => SliverFillRemaining(
                hasScrollBody: false,
                child: EventsNewsLoadingView(),
              ),
              NewsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  blocBuilderContext,
                  () {
                    blocBuilderContext.read<NewsBloc>().add(
                      NewsEvent.fetchNews(),
                    );
                  },
                ),
              ),
              _ => SliverMainAxisGroup(
                slivers: [
                  if (!widget.isInMarketDetails)
                    SliverToBoxAdapter(
                      child: ColoredBox(
                        color: theme.background.bgSecondary,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: DuploSearchInputField(
                            key: Key('symbols_search'),
                            onChanged: (_) {
                              debugPrint('onTextChanged');
                            },
                            controller: _textController,
                            hintText: l10n.trader_search,
                            isDisabled: true,
                            onTap:
                                () => showNewsSearchView(
                                  parentContext: context,
                                  textController: _textController,
                                  title: l10n.trader_search,
                                ),
                          ),
                        ),
                      ),
                    ),

                  SliverPersistentHeader(
                    pinned: true,
                    delegate: _StickyDateHeaderDelegate(
                      child: Container(
                        color: theme.background.bgSecondary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        alignment: Alignment.centerLeft,
                        width: double.infinity,
                        child: DuploText(
                          textAlign: TextAlign.start,
                          text: state.dateLabel,
                          style: context.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ),
                  ),

                  // News content
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: PagedView.sliver(
                      padding: EdgeInsets.zero,
                      itemCount: state.groupedNews.length,
                      centerError: true,
                      centerLoading: true,
                      centerEmpty: true,
                      isLoading: switch (state.processState) {
                        NewsLoading() => true,
                        _ => false,
                      },
                      loadingBuilder:
                          (ctx) => Center(
                            child:
                                _textController.text.length < 1
                                    ? const SizedBox()
                                    : EventsNewsLoadingView(),
                          ),
                      emptyBuilder:
                          (ctx) => EmptyOrErrorStateComponent.empty(
                            title: l10n.trader_nothingToShow,
                            description: l10n.trader_noResultsDescription,
                            svgImage: trader.Assets.images.emptySearch.svg(
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                      errorBuilder:
                          (ctx) => EmptyOrErrorStateComponent.error(
                            description:
                                l10n.trader_somethingWentWrongDescription,
                            title: l10n.trader_somethingWentWrong,
                            svgImage: trader.Assets.images.bug.svg(),
                            retryButtonText: l10n.trader_reload,
                            onTapRetry:
                                () => blocBuilderContext.read<NewsBloc>().add(
                                  NewsEvent.fetchNews(),
                                ),
                          ),
                      hasReachedMax: state.hasReachedMax,
                      separatorBuilder: (ctx, index) => SizedBox(height: 16),
                      onFetchData: () {
                        if (!state.hasReachedMax &&
                            state.processState !=
                                const NewsProcessState.loadingMore()) {
                          blocBuilderContext.read<NewsBloc>().add(
                            NewsEvent.fetchNews(loadMore: true),
                          );
                        }
                      },
                      itemBuilder: (BuildContext ctx, int index) {
                        final item = state.groupedNews[index];
                        if (item is DayPart || item is DateTime) {
                          if (item is DayPart) {
                            blocBuilderContext.read<NewsBloc>().add(
                              NewsEvent.updateDateLabel(
                                dateLabel: DateFormatter.localizedDay(
                                  item,
                                  l10n,
                                ),
                              ),
                            );
                          } else if (item is DateTime) {
                            // Format the specific date as "Jul 7, 2025"
                            blocBuilderContext.read<NewsBloc>().add(
                              NewsEvent.updateDateLabel(
                                dateLabel: DateFormat(
                                  'MMM d, yyyy',
                                ).format(item),
                              ),
                            );
                          }

                          return const SizedBox.shrink();
                        } else if (item is NewsItem) {
                          return NewsListItem(newsItemDetails: item.item);
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                ],
              ),
            };
          },
        ),
      ],
    );
  }
}

class _StickyDateHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  const _StickyDateHeaderDelegate({required this.child});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return child;
  }

  @override
  double get maxExtent => 40.0;

  @override
  double get minExtent => 40.0;

  @override
  bool shouldRebuild(covariant _StickyDateHeaderDelegate oldDelegate) {
    return true; // Rebuild when text changes
  }
}
