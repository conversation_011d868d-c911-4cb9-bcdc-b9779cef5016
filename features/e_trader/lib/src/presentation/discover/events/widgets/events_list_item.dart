import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/events_response_model.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/tag_list.dart';
import 'package:e_trader/src/presentation/duplo/affected_markets_indicator.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/event_details_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class EventsListItem extends StatelessWidget {
  final EventDetails eventDetails;

  const EventsListItem({required this.eventDetails});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = DuploTextStyles.of(context);
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context).toString();
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.background.bgPrimary,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              DuploCachedNetworkImage(
                imageUrl: eventDetails.id,
                imageHeight: 29,
                imageWidth: 29,
                errorWidget: CircleAvatar(
                  radius: 15,
                  backgroundColor: theme.border.borderSecondary,
                ),
              ),
              SizedBox(width: 8),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: "AAPL",
                    style: duploTextStyles.textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  DuploText(
                    text: "Apple Inc.",
                    style: duploTextStyles.textXs,
                    fontWeight: DuploFontWeight.regular,
                    color: theme.text.textTertiary,
                  ),
                ],
              ),
              Spacer(),
              _AddToCalendarWidget(
                title: eventDetails.name,
                subtitle: eventDetails.description ?? '',
                formattedDate: eventDetails.formattedDate(l10n),
                eventId: eventDetails.id,
                date: DateTime.parse(eventDetails.date),
              ),
            ],
          ),

          const SizedBox(height: 12),
          DuploText(
            text: EquitiFormatter.formatDateTimeToTimeString(
              eventDetails.parsedDate,
            ),
            style: duploTextStyles.textXs,
            fontWeight: DuploFontWeight.medium,
            color: theme.text.textQuaternary,
          ),
          const SizedBox(height: 8),

          Container(
            child: DuploText(
              text: eventDetails.name,
              style: duploTextStyles.textMd,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          TagList(tags: [eventDetails.eventType] + eventDetails.tags),
          if (eventDetails.eventDetails.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  EventDetailsView(
                    dividerColor: theme.foreground.fgPrimary,
                    title: l10n.trader_previous,
                    value:
                        eventDetails.eventDetails.firstOrNull!.previous != null
                            ? EquitiFormatter.formatNumber(
                              value:
                                  eventDetails
                                      .eventDetails
                                      .firstOrNull!
                                      .previous!,
                              locale: locale,
                            )
                            : '-',
                  ),
                  EventDetailsView(
                    dividerColor: theme.utility.utilitySuccess700,
                    title: l10n.trader_forecast,
                    value:
                        eventDetails.eventDetails.firstOrNull!.expected != null
                            ? EquitiFormatter.formatNumber(
                              value:
                                  eventDetails
                                      .eventDetails
                                      .firstOrNull!
                                      .expected!,
                              locale: locale,
                            )
                            : '-',
                  ),
                  EventDetailsView(
                    dividerColor: theme.foreground.fgPrimary,
                    title: l10n.trader_actual,
                    value:
                        eventDetails.eventDetails.firstOrNull!.actual != null
                            ? EquitiFormatter.formatNumber(
                              value:
                                  eventDetails
                                      .eventDetails
                                      .firstOrNull!
                                      .actual!,
                              locale: locale,
                            )
                            : '-',
                  ),
                ],
              ),
            ),
          if (eventDetails.symbols.isNotEmpty &&
              eventDetails.symbols.length > 1)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DuploText(
                    text: l10n.trader_affectedMarkets,
                    style: duploTextStyles.textXs,
                    color: theme.text.textQuaternary,
                  ),
                  AffectedMarketsIndicator(
                    marketSymbols: eventDetails.symbols,
                    maxCircles: 5,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}

class _AddToCalendarWidget extends StatelessWidget {
  const _AddToCalendarWidget({
    required this.title,
    required this.subtitle,
    required this.formattedDate,
    required this.eventId,
    required this.date,
  });

  final String title;
  final String subtitle;
  final String formattedDate;
  final String eventId;
  final DateTime date;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocListener<EventsBloc, EventsState>(
      listenWhen: (previous, current) {
        if (current.processState case EventsCalendarMessage calendarMessage) {
          return calendarMessage.eventId == eventId;
        }
        if (current.processState case CalendarError calendarMessage) {
          return calendarMessage.eventId == eventId;
        } else
          return false;
      },
      listener: (listenerContext, state) {
        if (state.processState case EventsCalendarMessage calendarMessage) {
          final message = calendarMessage.message;
          if (message.isNotEmpty) {
            final toast = DuploToast();
            toast.hidesToastMessage();
            toast.showToastMessage(
              context: context,
              widget: DuploToastDecoratorWidget(
                messageType: ToastMessageType.success,
                statusColor: context.duploTheme.utility.utilitySuccess600,
                contentWidget: const SizedBox.shrink(),
                titleMessage: message,
                onLeadingAction: toast.hidesToastMessage,
                onTap: toast.hidesToastMessage,
              ),
            );
          }
        }
        if (state.processState case CalendarError error) {
          if (error.message.isNotEmpty) {
            final toast = DuploToast();
            toast.hidesToastMessage();
            toast.showToastMessage(
              context: context,
              widget: DuploToastMessage(
                messageType: ToastMessageType.error,
                descriptionMessage: error.message,
                titleMessage: error.title,
                onLeadingAction: toast.hidesToastMessage,
                onTap: toast.hidesToastMessage,
              ),
            );
          }
        }
      },
      child: InkWell(
        onTap: () {
          final l10n = EquitiLocalization.of(context);

          context.read<EventsBloc>().add(
            EventsEvent.addEventToCalendar(
              title: title,
              subtitle: subtitle,
              date: date,
              eventId: eventId,
              successMessage: l10n.trader_eventAddedToCalendar,
              errorMessage: l10n.trader_failedToAddEvent,
              permissionDeniedMessage: l10n.trader_permissionDenied,
              noCalendarFoundMessage: l10n.trader_noCalendarFound,
              operationFailedMessage: l10n.trader_failedToAddEvent,
            ),
          );
        },
        child: Container(
          height: 48,
          width: 48,
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            border: Border.all(color: theme.button.buttonSecondaryBorder),
            color: theme.button.buttonSecondaryBg,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: theme.shadow.shadowOverlayLg.withOpacity(0.3),
                spreadRadius: -2,
                blurRadius: 2,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: trader.Assets.images.calendarAdd.svg(
            height: 20,
            width: 20,
            colorFilter: ColorFilter.mode(
              theme.button.buttonSecondaryFg,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}
