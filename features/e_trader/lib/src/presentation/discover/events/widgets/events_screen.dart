import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/events_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_list_item.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_search_view.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({this.isInMarketDetails = false});

  final bool isInMarketDetails;

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> {
  late final TextEditingController textController;

  @override
  void initState() {
    super.initState();
    textController = TextEditingController();
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);

    return BlocBuilder<EventsBloc, EventsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return CustomScrollView(
          slivers: [
            switch (state.processState) {
              EventsLoading() => SliverFillRemaining(
                hasScrollBody: false,
                child: EventsNewsLoadingView(),
              ),
              EventsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  blocBuilderContext,
                  () {
                    blocBuilderContext.read<EventsBloc>().add(
                      EventsEvent.fetchEvents(),
                    );
                  },
                ),
              ),
              _ => SliverMainAxisGroup(
                slivers: [
                  if (!widget.isInMarketDetails)
                    SliverToBoxAdapter(
                      child: ColoredBox(
                        color: theme.background.bgSecondary,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child:
                              !widget.isInMarketDetails
                                  ? DuploSearchInputField(
                                    controller: textController,
                                    hintText: l10n.trader_search,
                                    isDisabled: true,
                                    onTap:
                                        () => showEventsSearchView(
                                          parentContext: context,
                                          textController: textController,
                                          title: l10n.trader_search,
                                        ),
                                  )
                                  : const SizedBox.shrink(),
                        ),
                      ),
                    ),

                  SliverPersistentHeader(
                    pinned: true,
                    delegate: _StickyDateHeaderDelegate(
                      child: Container(
                        color: theme.background.bgSecondary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        alignment: Alignment.centerLeft,
                        width: double.infinity,
                        child: DuploText(
                          textAlign: TextAlign.start,
                          text: state.dateLabel,
                          style: context.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ),
                  ),

                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: PagedView.sliver(
                      padding: EdgeInsets.zero,
                      itemCount: state.groupedEvents.length,
                      centerError: true,
                      centerLoading: true,
                      centerEmpty: true,
                      hasReachedMax: state.hasReachedMax,
                      isLoading: switch (state.processState) {
                        EventsLoading() => true,
                        _ => false,
                      },
                      loadingBuilder:
                          (ctx) => Center(
                            child:
                                textController.text.length < 1
                                    ? const SizedBox()
                                    : EventsNewsLoadingView(),
                          ),
                      errorBuilder:
                          (ctx) => EmptyOrErrorStateComponent.error(
                            description:
                                l10n.trader_somethingWentWrongDescription,
                            title: l10n.trader_somethingWentWrong,
                            svgImage: trader.Assets.images.bug.svg(),
                            retryButtonText: l10n.trader_reload,
                            onTapRetry:
                                () => context.read<EventsBloc>().add(
                                  EventsEvent.fetchEvents(),
                                ),
                          ),
                      emptyBuilder:
                          (ctx) => EmptyOrErrorStateComponent.empty(
                            title: l10n.trader_nothingToShow,
                            description: l10n.trader_noResultsDescription,
                            svgImage: trader.Assets.images.emptySearch.svg(
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                      separatorBuilder: (ctx, index) => SizedBox(height: 16),
                      onFetchData: () {
                        if (!state.hasReachedMax &&
                            state.processState !=
                                const EventsProcessState.loadingMore()) {
                          blocBuilderContext.read<EventsBloc>().add(
                            EventsEvent.fetchEvents(loadMore: true),
                          );
                        }
                      },
                      itemBuilder: (ctx, index) {
                        final item = state.groupedEvents[index];

                        if (item is DayPart || item is DateTime) {
                          if (item is DayPart) {
                            blocBuilderContext.read<EventsBloc>().add(
                              EventsEvent.updateDateLabel(
                                dateLabel: DateFormatter.localizedDay(
                                  item,
                                  l10n,
                                ),
                              ),
                            );
                          } else if (item is DateTime) {
                            // Format the specific date as "Jul 7, 2025"
                            blocBuilderContext.read<EventsBloc>().add(
                              EventsEvent.updateDateLabel(
                                dateLabel: DateFormat(
                                  'MMM d, yyyy',
                                ).format(item),
                              ),
                            );
                          }

                          return const SizedBox.shrink();
                        } else if (item is EventItem) {
                          return EventsListItem(eventDetails: item.item);
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                ],
              ),
            },
          ],
        );
      },
    );
  }
}

class _StickyDateHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  const _StickyDateHeaderDelegate({required this.child});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return child;
  }

  @override
  double get maxExtent => 40.0;

  @override
  double get minExtent => 40.0;

  @override
  bool shouldRebuild(covariant _StickyDateHeaderDelegate oldDelegate) {
    return true; // Rebuild when text changes
  }
}
