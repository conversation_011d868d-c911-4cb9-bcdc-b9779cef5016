import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'news_details_event.dart';
part 'news_details_state.dart';
part 'news_details_bloc.freezed.dart';

class NewsDetailsBloc extends Bloc<NewsDetailsEvent, NewsDetailsState> {
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;

  NewsDetailsBloc(this._subscribeToSymbolQuotesUseCase)
    : super(NewsDetailsLoading()) {
    on<_FetchSymbolInfo>(_onGetProductDetailOverview);
  }

  FutureOr<void> _onGetProductDetailOverview(
    _FetchSymbolInfo event,
    Emitter<NewsDetailsState> emit,
  ) async {
    emit(NewsDetailsLoading());
    final result =
        await _subscribeToSymbolQuotesUseCase(
          symbol: event.symbol,
          subscriberId: '${NewsDetailsBloc}_$hashCode',
        ).run();
    return await result.fold(
      (left) {
        addError(left);
        if (!isClosed) emit(NewsDetailsError());
      },
      (subscribeResultStream) {
        return emit.forEach(
          subscribeResultStream,
          onData: (symbolQuoteModel) => NewsDetailsSuccess(symbolQuoteModel),
        );
      },
    );
  }
}
