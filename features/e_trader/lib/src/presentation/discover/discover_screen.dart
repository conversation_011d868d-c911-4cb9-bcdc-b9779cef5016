import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_screen.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/news_tab.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DiscoverScreen extends StatefulWidget {
  const DiscoverScreen({super.key});

  @override
  State<DiscoverScreen> createState() => _DiscoverScreenState();
}

class _DiscoverScreenState extends State<DiscoverScreen>
    with TickerProviderStateMixin {
  late final TabController tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();

    tabController = TabController(
      length: 2,
      vsync: this,
      animationDuration: Duration.zero,
    );
    tabController.addListener(() {
      if (!tabController.indexIsChanging &&
          _currentIndex != tabController.index) {
        setState(() {
          _currentIndex = tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: context.duploTheme.background.bgSecondary,
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (_) => diContainer<NewsBloc>()..add(NewsEvent.fetchNews()),
          ),
          BlocProvider(
            create:
                (_) =>
                    diContainer<EventsBloc>()..add(EventsEvent.fetchEvents()),
          ),
        ],

        /// TODO: Sagar no DuploTabbar here
        child: Column(
          children: [
            TabBar(
              controller: tabController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              dividerColor: context.duploTheme.border.borderSecondary,
              indicatorColor: context.duploTheme.foreground.fgBrandPrimaryAlt,
              labelColor: context.duploTheme.text.textBrandSecondary,
              labelStyle: TextStyle(
                fontSize: context.duploTextStyles.textSm.fontSize,
                fontWeight: DuploFontWeight.semiBold.value,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              unselectedLabelColor: context.duploTheme.text.textQuaternary,
              splashFactory: null,
              overlayColor: WidgetStateProperty.all(Colors.transparent),
              tabAlignment: TabAlignment.fill,
              unselectedLabelStyle: TextStyle(
                fontSize: context.duploTextStyles.textSm.fontSize,
                fontWeight: DuploFontWeight.medium.value,
              ),
              tabs: [
                Semantics(
                  identifier: 'discover_news_tab',
                  child: Tab(text: l10n.trader_news),
                ),
                Semantics(
                  identifier: 'discover_events_tab',
                  child: Tab(text: l10n.trader_events),
                ),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: tabController,
                children: [const NewsTab(), const EventsScreen()],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
