import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/modify_trade_bottom_sheet.dart';
import 'package:e_trader/src/presentation/partial_close/partial_close.dart';
import 'package:e_trader/src/presentation/partial_close/partial_close_toast.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:e_trader/src/presentation/trade_options_view/bloc/trade_options_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class TradeOptionView extends StatelessWidget {
  const TradeOptionView({
    super.key,
    required this.positionId,
    this.currency,
    required this.symbolName,
  });
  final String positionId;
  final String? currency;
  final String symbolName;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    DuploToast? toast;

    return BlocProvider(
      create:
          (createContext) =>
              diContainer<TradeOptionsBloc>(param1: positionId)
                ..add(TradeOptionsEvent.connectToPositionSocket(symbolName)),
      child: BlocConsumer<TradeOptionsBloc, TradeOptionsState>(
        listenWhen:
            (previous, current) =>
                current.quickCloseProcessState !=
                previous.quickCloseProcessState,
        listener: (listenerContext, state) {
          switch (state.quickCloseProcessState) {
            case QuickCloseSuccessState():
              {
                toast?.hidesToastMessage();
                toast = DuploToast();
                showCloseTradeToast(
                  toast: toast!,
                  context: context,
                  lots: state.lotSize,
                  productIconUrl: state.position!.productLogoUrl,
                  productName: state.position!.productName,
                  profit: state.position!.profit ?? 0,
                  tradeType: state.position!.positionType,
                  titleMessage: localization.trader_tradeClosed,
                  currency: currency,
                );
                Navigator.pop(context);
              }
            case QuickCloseMarketClosedState():
              {
                toast?.hidesToastMessage();
                toast = DuploToast();
                toast?.showToastMessage(
                  context: context,
                  widget: DuploToastMessage(
                    titleMessage: localization.trader_marketIsClosed,
                    descriptionMessage:
                        localization
                            .trader_closeTrade_marketIsClosedDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast?.hidesToastMessage(),
                  ),
                );
              }
            case QuickCloseLoadingState():
              {
                toast?.hidesToastMessage();
                toast = DuploToast();
                toast!.showToastMessage(
                  context: context,
                  widget: DuploToastMessage(
                    titleMessage: localization.trader_quickClose,
                    descriptionMessage: localization.trader_quickCloseLoading,
                    messageType: ToastMessageType.success,
                    onLeadingAction: () {
                      toast?.hidesToastMessage();
                    },
                  ),
                );
              }
            case QuickCloseErrorState(:final counter):
              {
                toast?.hidesToastMessage();
                toast = DuploToast();
                toast!.showToastMessage(
                  context: context,
                  autoCloseDuration: Duration.zero,
                  widget: DuploToastMessage(
                    titleMessage:
                        counter == 1
                            ? localization.trader_tradeNotClosed
                            : localization.trader_contactSupportTeam,
                    descriptionMessage:
                        counter == 1
                            ? localization.trader_placeholderText
                            : localization.trader_contactSupportDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () {
                      toast?.hidesToastMessage();
                    },
                    primaryButtonTitle:
                        counter == 1
                            ? null
                            : localization.trader_raiseSupportTicket,
                    onTap: () {
                      toast?.hidesToastMessage(); // TODO: Raise support ticket
                    },
                  ),
                );
              }
            case QuickCloseInitialState():
              {
                debugPrint('init state');
              }
          }
        },
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          return switch (state.processState) {
            TradeOptionsLoading() => Center(child: CircularProgressIndicator()),
            TradeOptionsError() => Center(
              child: DuploText(
                text: localization.trader_somethingWentWrong,
                style: textStyles.textSm,
                color: theme.text.textPrimary,
              ),
            ),
            TradeOptionsSuccess() => () {
              final position = state.position!;

              return CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: TradeTile(
                      digits: position.digits,
                      lots: state.lotSize,
                      profit: position.profit!,
                      tradeType: position.positionType,
                      tpValue: position.takeProfit,
                      slValue: position.stopLoss,
                      currentPrice: position.currentPrice,
                      priceChange:
                          position.positionType == TradeType.buy
                              ? position.buyPercentage
                              : position.sellPercentage,
                      productIcon: position.productLogoUrl,
                      productName: position.productName,
                      currency: currency,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Divider(
                      color: theme.border.borderSecondary,
                      height: 0,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color:
                                position.positionType == TradeType.buy
                                    ? theme.foreground.fgSuccessPrimary
                                    : theme.border.borderError,
                            width: 4.0,
                          ),
                        ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: DuploKeyValueDisplay(
                          hideLastDivider: false,
                          addBorder: false,
                          contentSpacing: 2,
                          keyTextStyle: textStyles.textXs,
                          valueTextStyle: textStyles.textXs,
                          keyValuePairs: [
                            KeyValuePair(
                              label: localization.trader_openPrice,
                              value: EquitiFormatter.decimalPatternDigits(
                                value: position.openPrice,
                                digits: position.digits,
                                locale:
                                    Localizations.localeOf(context).toString(),
                              ),
                            ),
                            KeyValuePair(
                              label: localization.trader_grossProfit,
                              value:
                                  EquitiFormatter.formatNumberWithZeroDefault(
                                    value: position.grossProfit,
                                    digits: 2,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                            ),
                            KeyValuePair(
                              label: localization.trader_swaps,
                              value:
                                  EquitiFormatter.formatNumberWithZeroDefault(
                                    value: position.swap,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                            ),
                            KeyValuePair(
                              label: localization.trader_takeProfit,
                              value: EquitiFormatter.formatDynamicDigits(
                                value: position.takeProfit,
                                digits: position.digits,
                                locale:
                                    Localizations.localeOf(context).toString(),
                              ),
                            ),
                            KeyValuePair(
                              label: localization.trader_netProfit,
                              value:
                                  EquitiFormatter.formatNumberWithZeroDefault(
                                    digits: 2,
                                    value:
                                        (position.commission) +
                                        position.swap +
                                        position.grossProfit,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                            ),
                            KeyValuePair(
                              label: localization.trader_commision,
                              value:
                                  EquitiFormatter.formatNumberWithZeroDefault(
                                    value: position.commission,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                            ),
                            KeyValuePair(
                              label: localization.trader_stopLoss,
                              value: EquitiFormatter.formatDynamicDigits(
                                value: position.stopLoss,
                                digits: position.digits,
                                locale:
                                    Localizations.localeOf(context).toString(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Container(
                      color: theme.background.bgSecondary,
                      padding: EdgeInsets.symmetric(horizontal: 2),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 12),
                          TextChevronWidget(
                            title: localization.trader_modifiyTrade,
                            titleStyle: textStyles.textSm,
                            onPressed: () {
                              modifyTradeBottomSheet(context, state.position!);
                            },
                          ),
                          Divider(
                            indent: 12,
                            endIndent: 12,
                            color: theme.border.borderSecondary,
                            height: 0,
                          ),
                          TextChevronWidget(
                            title: localization.trader_partialClose,
                            titleStyle: textStyles.textSm,
                            onPressed: () {
                              showPartialCloseSheet(
                                context: context,
                                positionId: int.parse(position.positionId),
                                onSuccess: ({
                                  required double lots,
                                  required String productIconUrl,
                                  required String productName,
                                  required double profit,
                                  required TradeType? tradeType,
                                  String? titleMessage,
                                }) {
                                  toast?.hidesToastMessage();
                                  toast = DuploToast();
                                  showCloseTradeToast(
                                    toast: toast!,
                                    context: context,
                                    lots: lots,
                                    productIconUrl: productIconUrl,
                                    productName: productName,
                                    profit: profit,
                                    tradeType: tradeType,
                                    titleMessage: titleMessage,
                                    currency: currency,
                                  );
                                },
                              );
                            },
                          ),
                          Divider(
                            indent: 12,
                            endIndent: 12,
                            color: theme.border.borderSecondary,
                            height: 0,
                          ),
                          const SizedBox(height: 16),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12.0,
                            ),
                            child: DuploButton.secondary(
                              useFullWidth: true,
                              isLoading:
                                  state.quickCloseProcessState
                                      is QuickCloseLoadingState,
                              title: localization.trader_quickClose,
                              onTap: () {
                                if (state.quickCloseProcessState !=
                                    QuickCloseProcessState.loading())
                                  blocBuilderContext.read<TradeOptionsBloc>().add(
                                    const TradeOptionsEvent.quickCloseTrade(),
                                  );
                              },
                            ),
                          ),
                          const SizedBox(height: 40),
                          _TradeDetailsWidget(accountCurrency: currency),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }(),
          };
        },
      ),
    );
  }
}

class _TradeDetailsWidget extends StatelessWidget {
  const _TradeDetailsWidget({this.accountCurrency});

  final String? accountCurrency;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context).toString();

    return BlocBuilder<TradeOptionsBloc, TradeOptionsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (ctx, state) {
        final position = state.position!;
        final displayData = [
          KeyValuePair(
            label: l10n.trader_tradeIDNumber,
            value: (position.positionId).toString(),
          ),
          KeyValuePair(label: l10n.trader_product, value: position.symbol),
          KeyValuePair(
            label: l10n.trader_baseCurrency,
            value: position.baseCurrency,
          ),
          KeyValuePair(
            label: l10n.trader_marketClass,
            value: position.productCategory,
          ),
          KeyValuePair(
            label: l10n.trader_marketType,
            value: position.assetType,
          ),
          KeyValuePair(
            label: l10n.trader_account_currency_type,
            value: accountCurrency ?? "",
          ),
        ];

        final displayData2 = [
          KeyValuePair(label: l10n.trader_opened, value: position.openedAt),
          KeyValuePair(
            label: l10n.trader_openPrice,
            value: EquitiFormatter.decimalPatternDigits(
              value: position.openPrice,
              digits: position.digits,
              locale: locale,
            ),
          ),
          KeyValuePair(
            label: l10n.trader_direction,
            value:
                (position.positionType == TradeType.buy
                    ? l10n.trader_buy
                    : l10n.trader_sell),
          ),
          KeyValuePair(
            label: l10n.trader_lotSize,
            value: EquitiFormatter.formatNumberWithZeroDefault(
              value: position.lotSize,
              locale: locale,
            ),
          ),
          KeyValuePair(
            label: l10n.trader_notionalValue,
            value:
                EquitiFormatter.getCurrencySymbol(accountCurrency) +
                EquitiFormatter.formatNumberWithZeroDefault(
                  value: position.notionalValue,
                  locale: locale,
                ),
          ),
          KeyValuePair(
            label: l10n.trader_pipValue,
            value:
                EquitiFormatter.getCurrencySymbol(accountCurrency) +
                EquitiFormatter.formatNumberWithZeroDefault(
                  value: position.pipValue,
                  locale: locale,
                ),
          ),
          KeyValuePair(
            label: l10n.trader_leverage,
            value:
                "1:${EquitiFormatter.formatNumber(value: position.leverage, locale: locale)}",
          ),
        ];

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              color: theme.background.bgSecondary,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  DuploKeyValueDisplay(
                    title: l10n.trader_general,
                    keyValuePairs: displayData,
                    keyTextStyle: duploTextStyles.textSm,
                    valueTextStyle: duploTextStyles.textSm,
                    addBorder: true,
                  ),
                  SizedBox(height: 24),
                  DuploKeyValueDisplay(
                    title: l10n.trader_execution,
                    keyValuePairs: displayData2,
                    keyTextStyle: duploTextStyles.textSm,
                    addBorder: true,
                  ),
                  SizedBox(height: 24),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
