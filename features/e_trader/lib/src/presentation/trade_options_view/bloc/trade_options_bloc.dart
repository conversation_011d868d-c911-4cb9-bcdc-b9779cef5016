import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/usecase/close_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trade_size_from_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'trade_options_bloc.freezed.dart';
part 'trade_options_event.dart';
part 'trade_options_state.dart';

class TradeOptionsBloc extends Bloc<TradeOptionsEvent, TradeOptionsState>
    with DisposableMixin {
  final String _positionId;
  final SubscribeToPositionsUseCase _subscribeToPositionsUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final GetTradeSizeFromVolumeUseCase _getTradeSizeFromVolumeUseCase;
  final CloseTradeUseCase _closeTradeUseCase;

  TradeOptionsBloc(
    this._positionId,
    this._subscribeToPositionsUseCase,
    this._getAccountNumberUseCase,
    this._getTradeSizeFromVolumeUseCase,
    this._closeTradeUseCase,
  ) : super(const TradeOptionsState()) {
    on<_ConnectToPositionSocket>(
      (event, emit) => _connectToPositionSocket(emit, event),
    );
    on<_QuickCloseTrade>((event, emit) => _quickCloseTrade(emit));
  }

  Future<void> _connectToPositionSocket(
    Emitter<TradeOptionsState> emit,
    _ConnectToPositionSocket event,
  ) async {
    emit(
      state.copyWith(processState: const TradeOptionsProcessState.loading()),
    );

    final result =
        await _subscribeToPositionsUseCase(
          positionId: int.parse(_positionId),
          subscriberId: '${TradeOptionsBloc}_$hashCode',
          eventType: TradingSocketEvent.positions.subscribe,
          symbolName: event.symbolName,
        ).run();

    return result.fold(
      (error) {
        addError(error);
        emit(
          state.copyWith(processState: const TradeOptionsProcessState.error()),
        );
      },
      (positionResponseStream) async {
        await emit.forEach(
          positionResponseStream,
          onData: (positionResponse) {
            if (positionResponse == null) {
              return state;
            }
            return state.copyWith(
              processState: const TradeOptionsProcessState.success(),
              position: positionResponse.position,
              lotSize: _getTradeSizeFromVolumeUseCase(
                positionResponse.position.volume,
              ),
            );
          },
          onError: (error, stackTrace) {
            addError(error, stackTrace);
            return state.copyWith(
              processState: const TradeOptionsProcessState.error(),
            );
          },
        );
      },
    );
  }

  Future<void> _quickCloseTrade(Emitter<TradeOptionsState> emit) async {
    emit(
      state.copyWith(
        quickCloseProcessState: const QuickCloseProcessState.loading(),
      ),
    );
    final result =
        await _getAccountNumberUseCase().toTaskEither().flatMap((
          accountNumber,
        ) {
          final request = CloseTradeRequestModel(
            accountNumber: accountNumber,
            positions: [
              ClosePositionItemModel(
                id: state.position!.positionId,
                volume: state.position!.volume,
              ),
            ],
          );
          return _closeTradeUseCase(request);
        }).run();
    result.fold(
      (exception) async {
        if (!emit.isDone) {
          if (exception is PositionsAndOrdersException &&
              exception.errors.containsKey("IsMarketOpen")) {
            final isMarketOpenError =
                exception.errors["IsMarketOpen"]?.firstOrNull;
            if (isMarketOpenError?.toLowerCase() == "false") {
              emit(
                state.copyWith(
                  quickCloseProcessState: QuickCloseProcessState.marketClosed(),
                ),
              );
              return;
            }
          } else
            emit(
              state.copyWith(
                quickCloseProcessState: QuickCloseProcessState.error(
                  state.errorCounter + 1,
                ),
                errorCounter: state.errorCounter + 1,
              ),
            );
        }
        addError(exception);
      },
      (success) async {
        if (!emit.isDone) {
          (success?.success ?? false)
              ? emit(
                state.copyWith(
                  quickCloseProcessState: QuickCloseProcessState.success(),
                  errorCounter: 0,
                ),
              )
              : emit(
                state.copyWith(
                  quickCloseProcessState: QuickCloseProcessState.error(
                    state.errorCounter + 1,
                  ),
                  errorCounter: state.errorCounter + 1,
                ),
              );
        }
      },
    );
  }
}
