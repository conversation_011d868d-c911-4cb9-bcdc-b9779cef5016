import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/more/trading_settings/bloc/trading_settings_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TradingSettingsListItem extends StatelessWidget {
  const TradingSettingsListItem();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (_) =>
              diContainer<TradingSettingsBloc>()
                ..add(TradingSettingsEvent.fetchPrefrences()),
      child: BlocBuilder<TradingSettingsBloc, TradingSettingsState>(
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          final l10n = EquitiLocalization.of(blocBuilderContext);
          return switch (state) {
            TradingSettingsInitial() => Container(),
            TradingSettingsSuccess() => GestureDetector(
              onTap: () {
                diContainer<EquitiTraderNavigation>()
                    .navigateToTradingPrefrences();
              },
              child: DuploLabelInfoChevronWidget(
                title: l10n.trader_tradingAccountSettings,
                description: l10n.trader_accountNickName,
                infoText: l10n.trader_someInformation,
              ),
            ),
          };
        },
      ),
    );
  }
}
