import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/change_leverage/change_leverage_screen.dart';
import 'package:e_trader/src/presentation/more/trading_settings/bloc/trading_settings_bloc.dart';
import 'package:e_trader/src/presentation/more/widgets/settings_composable_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TradingSettingsScreen extends StatelessWidget {
  const TradingSettingsScreen();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (_) =>
              diContainer<TradingSettingsBloc>()
                ..add(TradingSettingsEvent.fetchPrefrences()),
      child: CustomScrollView(
        slivers: [
          BlocBuilder<TradingSettingsBloc, TradingSettingsState>(
            buildWhen: (previous, current) => previous != current,
            builder: (blocBuilderContext, state) {
              return switch (state) {
                TradingSettingsInitial() => const SliverToBoxAdapter(
                  child: SizedBox.shrink(),
                ),
                TradingSettingsSuccess(:final leverage, :final accountNumber) =>
                  () {
                    final l10n = EquitiLocalization.of(blocBuilderContext);
                    SettingScreenSection accountSection = SettingScreenSection(
                      children: [
                        GestureDetector(
                          onTap: () {
                            DuploSheet.showNonScrollableModalSheet<void>(
                              context: context,
                              content:
                                  (duploSheetContext) => ChangeLeverageScreen(
                                    accountNumber: accountNumber,
                                  ),
                            ).then((ccc) {
                              blocBuilderContext
                                  .read<TradingSettingsBloc>()
                                  .add(TradingSettingsEvent.fetchPrefrences());
                            });
                          },
                          child: DuploLabelInfoChevronWidget(
                            title: l10n.trader_changeLeverage,
                            valueText: leverage,
                            infoText: l10n.trader_someInformation,
                          ),
                        ),
                      ],
                    );

                    return SettingsComposableScreen(
                      sections: [accountSection],
                      title: l10n.trader_tradingPreferences,
                    );
                  }(),
              };
            },
          ),
        ],
      ),
    );
  }
}
