import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'product_details_event.dart';
part 'product_details_state.dart';
part 'product_details_bloc.freezed.dart';

class ProductDetailsBloc
    extends Bloc<ProductDetailsEvent, ProductDetailsState> {
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;

  ProductDetailsBloc(this._subscribeToSymbolQuotesUseCase)
    : super(ProductDetailsLoading()) {
    on<_FetchSymbolInfo>(_onGetProductDetailOverview);
  }

  FutureOr<void> _onGetProductDetailOverview(
    _FetchSymbolInfo event,
    Emitter<ProductDetailsState> emit,
  ) async {
    emit(ProductDetailsLoading());
    final result =
        await _subscribeToSymbolQuotesUseCase(
          symbol: event.symbol,
          subscriberId: '${ProductDetailsBloc}_$hashCode',
        ).run();
    return await result.fold(
      (left) {
        addError(left);
        emit(ProductDetailsError());
      },
      (subscribeResultStream) {
        return emit.forEach(
          subscribeResultStream,
          onData: (symbolQuoteModel) => ProductDetailsSuccess(symbolQuoteModel),
        );
      },
    );
  }
}
