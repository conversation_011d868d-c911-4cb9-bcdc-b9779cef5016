import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/server_details_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

Future<Object?> showServerDetailsBottomSheet(
  BuildContext context,
  TradingAccountModel tradingAccountModel,
) {
  return DuploSheet.showNonScrollableModalSheet(
    context: context,
    hasTrailingIc: false,
    hasTopBarLayer: false,
    applyLeadingPadding: false,
    leadingNavBarWidget: Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: DuploAppBar(
        leading: IconButton(
          icon: Assets.images.arrowLeftDirectional(context).svg(),
          onPressed: () => Navigator.pop(context),
        ),
        titleWidget: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DuploText(
              text:
                  EquitiLocalization.of(context).trader_metaTraderServerDetails,
              style: context.duploTextStyles.textSm,
              color: context.duploTheme.text.textPrimary,
              fontWeight: DuploFontWeight.semiBold,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            DuploText(
              text:
                  tradingAccountModel.name ?? tradingAccountModel.accountNumber,
              style: context.duploTextStyles.textXxs,
              fontWeight: DuploFontWeight.medium,
              color: context.duploTheme.text.textSecondary,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        title: '',
      ),
    ),
    backgroundColor: context.duploTheme.background.bgPrimary,
    content: (contentContext) {
      return Column(
        children: [
          const SizedBox(height: 64),
          Expanded(
            child: ServerDetailsWidget(
              tradingAccountModel: tradingAccountModel,
            ),
          ),
        ],
      );
    },
  );
}
