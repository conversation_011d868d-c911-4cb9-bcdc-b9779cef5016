// ignore_for_file: prefer-number-format

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/cupertino.dart';

class AccountToggleWidget extends StatefulWidget {
  final void Function(AccountCategory selected) onChanged;
  final AccountCategory initialValue;
  final AccountCategory? selectedValue;
  final int accountsCount;
  final int walletsCount;

  const AccountToggleWidget({
    super.key,
    required this.onChanged,
    this.initialValue = AccountCategory.accounts,
    this.selectedValue,
    required this.accountsCount,
    required this.walletsCount,
  });

  @override
  State<AccountToggleWidget> createState() => _AccountToggleWidgetState();
}

class _AccountToggleWidgetState extends State<AccountToggleWidget> {
  final List<AccountCategory> _options = [
    AccountCategory.accounts,
    AccountCategory.wallets,
  ];
  late AccountCategory _selected;

  @override
  void initState() {
    super.initState();
    _selected = widget.selectedValue ?? widget.initialValue;
  }

  @override
  void didUpdateWidget(AccountToggleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedValue != null &&
        widget.selectedValue != _selected &&
        widget.selectedValue != oldWidget.selectedValue) {
      _selected = widget.selectedValue!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return DuploHorizontalTabs<AccountCategory>.buttonBorder(
      options: _options,
      selectedValue: _selected,
      onChanged: (value) {
        setState(() => _selected = value);
        widget.onChanged(value);
      },
      textBuilder:
          (option) => switch (option) {
            AccountCategory.accounts =>
              EquitiLocalization.of(context).trader_accounts,
            AccountCategory.wallets =>
              EquitiLocalization.of(context).trader_wallets,
          },
      badgeBuilder: (option) {
        final count =
            option == AccountCategory.accounts
                ? widget.accountsCount
                : widget.walletsCount;
        return count != 0 ? count.toString() : null;
      },
    );
  }
}
