import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/live_demo_account_toggle_widget.dart';
import 'package:flutter/material.dart';

class SwitchAccountAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final FutureOr<void> Function(TradingEnvironment selected)
  onAccountTypeChanged;
  final TradingEnvironment initialAccountType;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const SwitchAccountAppBar({
    Key? key,
    required this.onAccountTypeChanged,
    this.initialAccountType = TradingEnvironment.demo,
    this.onBackPressed,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return AppBar(
      backgroundColor: theme.background.bgSecondary,
      elevation: 0,
      leading:
          onBackPressed != null
              ? IconButton(
                color: theme.text.textPrimary,
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.text.textPrimary,
                  blendMode: BlendMode.srcIn,
                ),
                onPressed: onBackPressed,
              )
              : null,
      actions: actions,
      centerTitle: true,
      iconTheme: IconThemeData(color: theme.foreground.fgSecondary),
      title: LiveDemoAccountToggleWidget(
        onChanged: onAccountTypeChanged,
        initialValue: initialAccountType,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
