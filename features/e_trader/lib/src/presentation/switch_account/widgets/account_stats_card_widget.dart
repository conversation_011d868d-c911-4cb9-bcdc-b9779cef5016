import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class AccountStatsCardWidget extends StatelessWidget {
  const AccountStatsCardWidget({
    super.key,
    required this.label,
    required this.value,
    required this.currency,
    this.isLoading = false,
  });

  final String label;
  final double value;
  final bool isLoading;
  final String currency;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final formattedValue = EquitiFormatter.decimalPatternDigits(
      value: value,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );
    final whole = formattedValue.split('.').firstOrNull!;
    final fraction = formattedValue.split('.').elementAtOrNull(1);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.background.bgTertiary,
        border: Border.all(color: theme.border.borderSecondary, width: 1.0),
      ),
      child:
          isLoading
              ? DuploShimmerListItem(
                hasLeading: false,
                hasTrailing: false,
                height: 96,
              )
              : Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      DuploText(
                        text: label,
                        style: context.duploTextStyles.textXs,
                        fontWeight: DuploFontWeight.medium,
                        color: theme.text.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Assets.images.help.svg(width: 12, height: 12),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Flexible(
                    child: DuploText.rich(
                      spans: [
                        DuploTextSpan(
                          text: whole,
                          style: context.duploTextStyles.textXl,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        DuploTextSpan(
                          text: ".",
                          style: context.duploTextStyles.textXs,
                          color: theme.text.textSecondary,
                        ),
                        DuploTextSpan(
                          text: fraction!,
                          style: context.duploTextStyles.textXs,
                          color: theme.text.textSecondary,
                        ),
                        DuploTextSpan(
                          text: " ",
                          style: context.duploTextStyles.textXs,
                          color: theme.text.textSecondary,
                        ),
                        DuploTextSpan(
                          text: currency,
                          style: context.duploTextStyles.textXs,
                          color: theme.text.textSecondary,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }
}
