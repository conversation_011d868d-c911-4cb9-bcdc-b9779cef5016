// ignore_for_file: prefer-match-file-name
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/domain/usecase/get_trading_preferences_use_case.dart';
import 'package:e_trader/src/presentation/change_account_password/change_account_password_screen.dart';
import 'package:e_trader/src/presentation/reset_balance/reset_balance_screen.dart';
import 'package:e_trader/src/presentation/switch_account/rename_account/show_rename_account_bottom_sheet.dart';
import 'package:e_trader/src/presentation/change_leverage/change_leverage_screen.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/show_server_details_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

Future<Object?> showAccountSettingsBottomSheet(
  BuildContext context,
  TradingAccountModel account,
  TradingEnvironment tradingEnvironment, {
  VoidCallback? onDepositPressed,
  VoidCallback? onWithdrawPressed,
  VoidCallback? onTransferPressed,
}) {
  return DuploSheet.showModalSheet<Object?>(
    title: '',
    hideCloseButton: true,
    hideTitle: true,
    hasTopBarLayer: false,
    context: context,
    content:
        (contentContext) => Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 16),
              if (tradingEnvironment == TradingEnvironment.live) ...[
                _AccountSummaryWidget(account),
                SizedBox(height: 16),
                DuploFundingButtons(
                  onDepositPressed: onDepositPressed,
                  onWithdrawPressed: onWithdrawPressed,
                  onTransferPressed: onTransferPressed,
                ),
                SizedBox(height: 12),
              ],
              _ChangeMaxLeverageWidget(account),
              if (account.platformType != PlatformType.dulcimer)
                _ServerDetailsWidget(account),
              if (tradingEnvironment == TradingEnvironment.demo)
                _ResetBalanceWidget(account),
              _RenameAccountWidget(account),
              _ChangeAccountPasswordWidget(account),
            ],
          ),
        ),
  );
}

class _ChangeMaxLeverageWidget extends StatefulWidget {
  const _ChangeMaxLeverageWidget(this.account);

  final TradingAccountModel account;

  @override
  State<_ChangeMaxLeverageWidget> createState() =>
      _ChangeMaxLeverageWidgetState();
}

class _ChangeMaxLeverageWidgetState extends State<_ChangeMaxLeverageWidget> {
  int? selectedLeverage;

  @override
  initState() {
    super.initState();
    final savedLeverage =
        diContainer<GetTradingPreferencesUseCase>().getLeverage();
    selectedLeverage =
        savedLeverage.isEmpty
            ? widget.account.leverage
            : int.tryParse(savedLeverage);
  }

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_changeAccountLeverage,
    trailingText:
        // ignore: prefer-number-format
        selectedLeverage != null ? "1:${selectedLeverage!.toString()}" : null,
    onPressed: () {
      final l10n = EquitiLocalization.of(context);
      Navigator.of(context).pop();
      DuploSheet.showModalSheetV2<void>(
        context,
        appBar: DuploAppBar(title: l10n.trader_changeAccountLeverage),
        content: ChangeLeverageScreen(
          accountNumber: widget.account.accountNumber,
          leverage: widget.account.leverage,
        ),
      );
    },
  );
}

class _RenameAccountWidget extends StatelessWidget {
  const _RenameAccountWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_renameAccount,
    onPressed: () {
      showRenameAccountBottomSheet<bool?>(context, account).then((value) {
        if (value == true) {
          Navigator.of(context).pop(true);
        }
      });
    },
  );
}

class _ServerDetailsWidget extends StatelessWidget {
  const _ServerDetailsWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_metaTraderServerDetails,
    trailingText: account.platformType.displayName,
    onPressed: () {
      Navigator.of(context).pop();
      showServerDetailsBottomSheet(context, account);
    },
  );
}

class _AccountSummaryWidget extends StatelessWidget {
  const _AccountSummaryWidget(this.accountModel);

  final TradingAccountModel accountModel;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: accountModel.nickName,
          style: duploTextStyles.textMd,
          color: theme.text.textPrimary,
          fontWeight: DuploFontWeight.semiBold,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        //TODO(sagar): update the value once you get from the designer
        DuploText(
          text: "${accountModel.platformAccountType.displayName}",
          style: duploTextStyles.textXs,
          color: theme.text.textTertiary,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}

class _ChangeAccountPasswordWidget extends StatelessWidget {
  const _ChangeAccountPasswordWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return TextChevronWidget(
      title: localization.trader_changeAccountPasswordTitle,
      onPressed: () {
        Navigator.of(context).pop();
        DuploSheet.showModalSheetV2<void>(
          context,
          appBar: DuploAppBar(
            title: localization.trader_changeAccountPasswordTitle,
          ),
          content: ChangeAccountPasswordScreen(
            accountNumber: account.accountNumber,
          ),
        );
      },
    );
  }
}

class _ResetBalanceWidget extends StatelessWidget {
  const _ResetBalanceWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) {
    return TextChevronWidget(
      title: EquitiLocalization.of(context).trader_resetBalance,
      onPressed: () {
        DuploSheet.showModalSheet<void>(
          context: context,
          title: "",
          useSafeArea: false,
          hasTopBarLayer: false,
          hideCloseButton: true,
          navBarHeight: 0,
          content:
              (duploSheetContext) => ResetBalanceScreen(
                accountNumber: account.accountNumber,
                accountCurrency: account.homeCurrency,
              ),
        );
      },
    );
  }
}
