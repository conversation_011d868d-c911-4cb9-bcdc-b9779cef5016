import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/presentation/duplo/company_logo_widget.dart';
import 'package:e_trader/src/presentation/duplo/server_connection_details_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

Future<void> serverNotSupportedBottomSheet(
  BuildContext context,
  TradingAccountModel tradingAccountModel,
) {
  final theme = context.duploTheme;
  final textStyles = context.duploTextStyles;

  return DuploSheet.showNonScrollableModalSheet(
    context: context,
    hasTrailingIc: false,
    applyLeadingPadding: false,
    leadingNavBarWidget: AppBar(
      backgroundColor: theme.background.bgPrimary,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: theme.foreground.fgSecondary),
        onPressed: () => Navigator.pop(context),
      ),
      title: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (tradingAccountModel.name case final name?)
            DuploText(
              text: name,
              style: textStyles.textSm,
              fontWeight: DuploFontWeight.semiBold,
              maxLines: 1,
              color: theme.text.textPrimary,
              overflow: TextOverflow.ellipsis,
            ),
          DuploText(
            text:
                "${tradingAccountModel.homeCurrency}-${tradingAccountModel.accountNumber}",
            style: textStyles.textXxs,
            maxLines: 1,
            color: theme.text.textSecondary,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    ),
    content: (contentContext) {
      return ServerNotSupportedWidget(tradingAccountModel: tradingAccountModel);
    },
  );
}

class ServerNotSupportedWidget extends StatelessWidget {
  const ServerNotSupportedWidget({
    super.key,
    required this.tradingAccountModel,
  });

  final TradingAccountModel tradingAccountModel;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: ListView(
                children: [
                  trader.Assets.images.mt4NotSupported.svg(),
                  const SizedBox(height: 16),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      DuploText(
                        text:
                            'Trade ${tradingAccountModel.platformType.displayName} outside the app',
                        style: textStyles.textLg,
                        fontWeight: DuploFontWeight.semiBold,
                        maxLines: 1,
                        color: theme.text.textPrimary,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      DuploText(
                        textAlign: TextAlign.center,
                        text:
                            '${tradingAccountModel.platformType.displayName} accounts are not supported for trading within the Equiti Trader app.',
                        style: textStyles.textSm,
                        color: theme.text.textTertiary,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _ServerDetailsWidget(
                    title:
                        '${tradingAccountModel.platformType.displayName} Server details',
                    subtitle: 'View login info and download links',
                    trailing: Assets.images.chevronDown.svg(),
                    tradingAccountModel: tradingAccountModel,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Divider(color: theme.border.borderSecondary),
                      ),
                      const SizedBox(width: 8),
                      DuploText(
                        text: 'or',
                        style: textStyles.textSm,
                        fontWeight: DuploFontWeight.medium,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Divider(color: theme.border.borderSecondary),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _ServerDetailsWidget(
                    title: 'MetaTrader Web Trader',
                    subtitle: 'Open MetaTrader 4 web platform',
                    trailing: Assets.images.arrowUpRight.svg(),
                    tradingAccountModel: tradingAccountModel,
                    onTap: () {
                      final url =
                          Platform.isAndroid
                              ? "https://play.google.com/store/apps/details?id=net.metaquotes.metatrader4&hl=en"
                              : "https://apps.apple.com/ae/app/metatrader-4/id496212596";
                      launchUrl(
                        Uri.parse(url),
                        mode: LaunchMode.externalApplication,
                      );
                    },
                  ),
                ],
              ),
            ),
            DuploButton.secondary(
              title: 'Go back',
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }
}

class _ServerDetailsWidget extends StatefulWidget {
  const _ServerDetailsWidget({
    required this.title,
    required this.subtitle,
    required this.trailing,
    required this.tradingAccountModel,
    this.onTap,
  });

  final String title;
  final String subtitle;
  final Widget trailing;
  final VoidCallback? onTap;
  final TradingAccountModel tradingAccountModel;

  @override
  State<_ServerDetailsWidget> createState() => _ServerDetailsWidgetState();
}

class _ServerDetailsWidgetState extends State<_ServerDetailsWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _iconTurns;
  late Animation<double> _heightFactor;
  static final Animatable<double> _easeInTween = CurveTween(
    curve: Curves.easeIn,
  );
  static final Animatable<double> _halfTween = Tween<double>(
    begin: 0.0,
    end: 0.5,
  );
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _heightFactor = _controller.drive(_easeInTween);
    _iconTurns = _controller.drive(_halfTween.chain(_easeInTween));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return DuploTap(
      onTap: widget.onTap ?? _handleTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.background.bgSecondary,
          borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
          border: Border.all(color: theme.border.borderSecondary, width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                CompanyLogoWidget(
                  platformType: widget.tradingAccountModel.platformType,
                  size: 48,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      DuploText(
                        text: widget.title,
                        style: textStyles.textMd,
                        fontWeight: DuploFontWeight.semiBold,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        color: theme.text.textSecondary,
                      ),
                      DuploText(
                        text: 'View login info and download links',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: textStyles.textXs,
                        color: theme.text.textTertiary,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 4),
                RotationTransition(
                  turns: _iconTurns,
                  child: SizedBox(
                    height: 24,
                    width: 24,
                    child: widget.trailing,
                  ),
                ),
              ],
            ),
            ClipRect(
              child: AnimatedBuilder(
                animation: _controller,
                builder: (animatedContext, child) {
                  return Align(
                    alignment: Alignment.centerLeft,
                    heightFactor: _heightFactor.value,
                    child: child,
                  );
                },
                child: ServerConnectionDetailsWidget(
                  tradingAccountModel: widget.tradingAccountModel,
                  title: DuploText(
                    style: context.duploTextStyles.textSm,
                    fontWeight: DuploFontWeight.medium,
                    text: EquitiLocalization.of(context).trader_howToConnect,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    color: context.duploTheme.text.textSecondary,
                  ),
                  subtitle: DuploText(
                    text:
                        'Copy the server name and MT login, then enter your password.',
                    style: context.duploTextStyles.textXs,
                    color: context.duploTheme.text.textTertiary,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 36),
            DuploButton.secondary(
              title:
                  '${widget.tradingAccountModel.platformType.displayName} WebTerminal',
              onTap:
                  () => _launchWebTerminal(
                    context,
                    widget.tradingAccountModel.platformType,
                  ),
            ),
            const SizedBox(height: 16),
            DuploButton.secondary(
              title:
                  '${widget.tradingAccountModel.platformType.displayName} on App Store',
              onTap:
                  () => _launchAppStore(
                    context,
                    widget.tradingAccountModel.platformType,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  void _launchWebTerminal(BuildContext context, PlatformType platformType) {
    String? url;

    // For now, MT5 web terminal URL is used for both MT4 and MT5
    switch (platformType) {
      case PlatformType.mt4:
      case PlatformType.mt5:
        final localization = EquitiLocalization.of(context);
        url = localization.trader_mt5_web_terminal_url;
        break;
      default:
        break;
    }

    if (url != null) {
      try {
        launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        debugPrint('Error launching web terminal URL: $e');
      }
    }
  }

  void _launchAppStore(BuildContext context, PlatformType platformType) {
    final localization = EquitiLocalization.of(context);
    String? url;

    if (Platform.isIOS) {
      switch (platformType) {
        case PlatformType.mt4:
          url = localization.trader_mt4_app_store_url;
          break;
        case PlatformType.mt5:
          url = localization.trader_mt5_app_store_url;
          break;
        default:
          break;
      }
    } else if (Platform.isAndroid) {
      switch (platformType) {
        case PlatformType.mt4:
          url = localization.trader_mt4_play_store_url;
          break;
        case PlatformType.mt5:
          url = localization.trader_mt5_play_store_url;
          break;
        default:
          break;
      }
    }

    if (url != null) {
      try {
        launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        debugPrint('Error launching app store URL: $e');
      }
    }
  }
}
