import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

class SwitchAccountPortfolioSection extends StatelessWidget {
  const SwitchAccountPortfolioSection({
    super.key,
    required this.stats,
    required this.onStatPressed,
  });

  final List<Widget> stats;
  final void Function(int) onStatPressed;

  @override
  Widget build(BuildContext context) => _StatsCarouselWithIndicatorWidget(
    stats: stats,
    onStatPressed: onStatPressed,
  );
}

class _StatsCarouselWithIndicatorWidget extends StatefulWidget {
  const _StatsCarouselWithIndicatorWidget({
    required this.stats,
    required this.onStatPressed,
  });

  final List<Widget> stats;
  final void Function(int) onStatPressed;

  @override
  State<_StatsCarouselWithIndicatorWidget> createState() =>
      _StatsCarouselWithIndicatorWidgetState();
}

class _StatsCarouselWithIndicatorWidgetState
    extends State<_StatsCarouselWithIndicatorWidget>
    with TickerProviderStateMixin {
  late CarouselController _carouselController;
  late AnimationController _animationController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _carouselController = CarouselController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Listen to scroll changes to update current index
    _carouselController.addListener(_onScrollChanged);
  }

  void _onScrollChanged() {
    if (_carouselController.hasClients) {
      final screenWidth = MediaQuery.sizeOf(context).width * 0.6;
      final offset = _carouselController.offset;
      final newIndex = (offset / screenWidth).round().clamp(
        0,
        widget.stats.length -
            2, // Max index is length - 2 since we show one less dot
      );

      if (newIndex != _currentIndex) {
        setState(() => _currentIndex = newIndex);
        _animationController.forward(from: 0);
      }
    }
  }

  @override
  void dispose() {
    _carouselController.removeListener(_onScrollChanged);
    _carouselController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.sizeOf(context).width * 0.6;

    return SizedBox(
      height: 138,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 110,
            child: Row(
              children: [
                const SizedBox(width: 8),
                Expanded(
                  child: CarouselView(
                    controller: _carouselController,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: context.duploTheme.border.borderSecondary,
                        width: 1,
                      ),
                    ),
                    padding: const EdgeInsets.all(8.0),
                    itemSnapping: true,
                    scrollDirection: Axis.horizontal,
                    itemExtent: screenWidth,
                    shrinkExtent:
                        screenWidth *
                        0.9, // 50% of itemExtent for smooth transitions
                    children: widget.stats,
                    onTap: widget.onStatPressed,
                  ),
                ),
              ],
            ),
          ),
          if (widget.stats.length > 1) ...[
            const SizedBox(height: 20),
            _DuploCarouselDotIndicator(
              currentIndex: _currentIndex,
              totalDots:
                  widget.stats.length -
                  1, // One less because 60% of next item is visible
              animationController: _animationController,
              onDotTapped: (index) => _carouselController.animateToItem(index),
              dotSize: 8.0,
              spacing: 8.0,
            ),
          ],
        ],
      ),
    );
  }
}

class _DuploCarouselDotIndicator extends StatelessWidget {
  const _DuploCarouselDotIndicator({
    required this.currentIndex,
    required this.totalDots,
    required this.animationController,
    this.onDotTapped,
    this.dotSize = 8.0,
    this.spacing = 8.0,
  });

  final int currentIndex;
  final int totalDots;
  final AnimationController animationController;
  final void Function(int index)? onDotTapped;
  final double dotSize;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(totalDots, (index) {
        final isActive = index == currentIndex;

        return GestureDetector(
          onTap: onDotTapped != null ? () => onDotTapped!(index) : null,
          child: AnimatedBuilder(
            animation: animationController,
            builder: (buildContext, _) {
              final scale =
                  isActive ? 1.0 + (0.2 * animationController.value) : 1.0;

              return Container(
                margin: EdgeInsets.symmetric(horizontal: spacing / 2),
                child: Transform.scale(
                  scale: scale,
                  child: Container(
                    width: dotSize,
                    height: dotSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          isActive
                              ? theme.foreground.fgBrandPrimaryAlt
                              : theme.background.bgQuaternary,
                    ),
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }
}
