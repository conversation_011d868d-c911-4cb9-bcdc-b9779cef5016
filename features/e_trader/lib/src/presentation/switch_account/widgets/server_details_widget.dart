import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/presentation/duplo/company_logo_widget.dart';
import 'package:e_trader/src/presentation/duplo/server_connection_details_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class ServerDetailsWidget extends StatelessWidget {
  const ServerDetailsWidget({super.key, required this.tradingAccountModel});

  final TradingAccountModel tradingAccountModel;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyle = context.duploTextStyles;

    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        DuploTopChartNumbersWidget(
          title: EquitiLocalization.of(context).trader_accountEquity,
          amount: tradingAccountModel.equity ?? 0,
          profitLoss: tradingAccountModel.profit ?? 0,
          currency: tradingAccountModel.homeCurrency,
        ),
        const SizedBox(height: 16),
        Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.background.bgSecondary,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: theme.border.borderSecondary),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.background.bgSecondary,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: theme.border.borderSecondary),
                ),
                child: CompanyLogoWidget(
                  platformType: tradingAccountModel.platformType,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  DuploText(
                    text: tradingAccountModel.platformType.displayName,
                    style: duploTextStyle.textSm,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 6),
                  DuploTagContainer.xs(
                    text: EquitiLocalization.of(context).trader_accountDetails,
                    type: DuploTagType.neutral,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: theme.background.bgSecondary,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: theme.border.borderSecondary),
          ),
          child: ServerConnectionDetailsWidget(
            tradingAccountModel: tradingAccountModel,
            title: DuploText(
              style: duploTextStyle.textLg,
              fontWeight: DuploFontWeight.semiBold,
              text: EquitiLocalization.of(context).trader_howToConnect,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              color: theme.text.textSecondary,
            ),
            subtitle: DuploText(
              text:
                  EquitiLocalization.of(context).trader_howToConnectDescription,
              style: duploTextStyle.textXs,
              color: theme.text.textSecondary,
            ),
          ),
        ),
        const SizedBox(height: 36),
        DuploButton.secondary(
          title: '${tradingAccountModel.platformType.displayName} WebTerminal',
          onTap:
              () =>
                  _launchWebTerminal(context, tradingAccountModel.platformType),
        ),
        const SizedBox(height: 16),
        DuploButton.secondary(
          title: '${tradingAccountModel.platformType.displayName} on App Store',
          onTap:
              () => _launchAppStore(context, tradingAccountModel.platformType),
        ),
      ],
    );
  }

  void _launchWebTerminal(BuildContext context, PlatformType platformType) {
    String? url;

    // For now, MT5 web terminal URL is used for both MT4 and MT5
    switch (platformType) {
      case PlatformType.mt4:
      case PlatformType.mt5:
        final localization = EquitiLocalization.of(context);
        url = localization.trader_mt5_web_terminal_url;
        break;
      default:
        break;
    }

    if (url != null) {
      try {
        launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        debugPrint('Error launching web terminal URL: $e');
      }
    }
  }

  void _launchAppStore(BuildContext context, PlatformType platformType) {
    final localization = EquitiLocalization.of(context);
    String? url;

    if (Platform.isIOS) {
      switch (platformType) {
        case PlatformType.mt4:
          url = localization.trader_mt4_app_store_url;
          break;
        case PlatformType.mt5:
          url = localization.trader_mt5_app_store_url;
          break;
        default:
          break;
      }
    } else if (Platform.isAndroid) {
      switch (platformType) {
        case PlatformType.mt4:
          url = localization.trader_mt4_play_store_url;
          break;
        case PlatformType.mt5:
          url = localization.trader_mt5_play_store_url;
          break;
        default:
          break;
      }
    }

    if (url != null) {
      try {
        launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        debugPrint('Error launching app store URL: $e');
      }
    }
  }
}
