// ignore_for_file: prefer-match-file-name

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class WalletCard extends StatelessWidget {
  final TradingAccountModel account;

  const WalletCard(this.account);

  @override
  Widget build(BuildContext context) {
    final loc = EquitiLocalization.of(context);
    final formattedBalance = EquitiFormatter.decimalPatternDigits(
      value: account.balance!,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );
    final String balanceWhole = formattedBalance.split('.').firstOrNull!;
    final String balanceFraction =
        formattedBalance.split('.').elementAtOrNull(1)!;
    return Align(
      alignment: AlignmentDirectional.centerStart,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 80),
                DuploText(
                  text: loc.trader_walletBalance,
                  style: context.duploTextStyles.textXs,
                  color: context.duploTheme.text.textSecondary,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    DuploText(
                      text: balanceWhole,
                      style: context.duploTextStyles.displaySm,
                      color: context.duploTheme.text.textPrimary,
                      fontWeight: DuploFontWeight.semiBold,
                    ),
                    DuploText(
                      text: ".$balanceFraction",
                      style: context.duploTextStyles.textLg,
                      color: context.duploTheme.text.textPrimary,
                    ),
                    const SizedBox(width: 5),
                    DuploText(
                      text: account.homeCurrency,
                      style: context.duploTextStyles.textLg,
                      color: context.duploTheme.text.textTertiary,
                    ),
                  ],
                ),
                Row(
                  children: [
                    DuploText(
                      text: loc.trader_walletId,
                      style: context.duploTextStyles.textSm,
                      color: context.duploTheme.text.textSecondary,
                      fontWeight: DuploFontWeight.medium,
                    ),
                    const SizedBox(width: 8),
                    DuploText(
                      text: account.accountNumber,
                      style: context.duploTextStyles.textSm,
                      color: context.duploTheme.text.textPrimary,
                      fontWeight: DuploFontWeight.medium,
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Divider(color: context.duploTheme.border.borderSecondary, height: 1),
        ],
      ),
    );
  }
}
