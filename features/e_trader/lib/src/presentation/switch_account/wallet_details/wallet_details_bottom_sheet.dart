import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/widgets/funding_tile.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/bloc/wallet_details_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/widgets/wallet_card.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/widgets/wallet_details_loading_widget.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/widgets/wallet_details_header.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

Future<Object?> walletDetailsBottomSheet(
  BuildContext context,
  TradingAccountModel account,
) {
  bool areDatesOnSameDay(DateTime? isoDate1, DateTime? isoDate2) {
    if (isoDate1 == null || isoDate2 == null) {
      return false;
    }
    return DateUtils.isSameDay(isoDate1, isoDate2);
  }

  return DuploSheet.showNonScrollableModalSheet(
    context: context,
    hasTrailingIc: false,
    hasTopBarLayer: false,
    applyLeadingPadding: false,
    leadingNavBarWidget: WalletDetailsHeader(
      accountNumber: account.accountNumber,
      currency: account.homeCurrency,
    ),
    useSafeArea: false,
    backgroundColor: context.duploTheme.background.bgPrimary,
    content: (contentContext) {
      final loc = EquitiLocalization.of(contentContext);
      return Container(
        color: context.duploTheme.background.bgPrimary,
        child: Column(
          children: [
            WalletCard(account),
            Expanded(
              child: BlocProvider(
                create:
                    (blocProviderContext) =>
                        diContainer<WalletDetailsBloc>()..add(
                          WalletDetailsEvent.fetchFunding(account: account),
                        ),
                child: BlocBuilder<WalletDetailsBloc, WalletDetailsState>(
                  buildWhen: (previous, current) => previous != current,
                  builder: (builderContext, state) {
                    return PagedView.list(
                      physics: switch (state.processState) {
                        WalletDetailsError() =>
                          const NeverScrollableScrollPhysics(),
                        WalletDetailsSuccess() =>
                          state.fundingItems.isEmpty
                              ? const NeverScrollableScrollPhysics()
                              : const AlwaysScrollableScrollPhysics(),
                        _ => const AlwaysScrollableScrollPhysics(),
                      },
                      padding: EdgeInsets.zero,
                      itemCount: state.fundingItems.length + 1,
                      centerError: true,
                      centerLoading: true,
                      centerEmpty: true,
                      hasError: state.processState is WalletDetailsError,
                      isLoading:
                          state.processState is WalletDetailsLoading ||
                          state.processState is ButtonLoadingOnly,
                      hasReachedMax:
                          state.fundingItemsCount == state.fundingItems.length,
                      emptyBuilder:
                          (ctx) => Column(
                            children: [
                              Expanded(
                                child: EmptyOrErrorStateComponent.empty(
                                  backgroundColor:
                                      builderContext
                                          .duploTheme
                                          .background
                                          .bgPrimary,
                                  svgImage:
                                      trader.Assets.images.walletDetailsEmpty
                                          .svg(),
                                  title: loc.trader_noTransactionsYet,
                                  description:
                                      loc.trader_walletActivityDescription,
                                ),
                              ),
                            ],
                          ),
                      loadingBuilder:
                          (ctx) => Column(
                            children: [
                              if (state.processState is ButtonLoadingOnly)
                                Expanded(
                                  child: EmptyOrErrorStateComponent.empty(
                                    backgroundColor:
                                        ctx.duploTheme.background.bgPrimary,
                                    svgImage:
                                        trader.Assets.images.walletDetailsError
                                            .svg(),
                                    title: loc.trader_unableToLoadTransactions,
                                    description:
                                        loc.trader_transactionLoadError,
                                  ),
                                ),
                              if (state.processState is ButtonLoadingOnly)
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: DuploButton.secondary(
                                    useFullWidth: true,
                                    title: loc.trader_reloadScreen,
                                    isLoading: true,
                                    loadingText: loc.trader_reloadScreen,
                                    onTap: () {
                                      ctx.read<WalletDetailsBloc>().add(
                                        WalletDetailsEvent.fetchFunding(
                                          account: account,
                                          isErrorLoadingButton: true,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              if (!(state.processState is ButtonLoadingOnly))
                                WalletDetailsLoadingWidget(),
                            ],
                          ),
                      errorBuilder:
                          (ctx) => Column(
                            children: [
                              Expanded(
                                child: EmptyOrErrorStateComponent.empty(
                                  backgroundColor:
                                      ctx.duploTheme.background.bgPrimary,
                                  svgImage:
                                      trader.Assets.images.walletDetailsError
                                          .svg(),
                                  title: loc.trader_unableToLoadTransactions,
                                  description: loc.trader_transactionLoadError,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: DuploButton.secondary(
                                  useFullWidth: true,
                                  title: loc.trader_reloadScreen,
                                  onTap: () {
                                    ctx.read<WalletDetailsBloc>().add(
                                      WalletDetailsEvent.fetchFunding(
                                        account: account,
                                        isErrorLoadingButton: true,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                      separatorBuilder: (ctx, index) => const SizedBox(),
                      onFetchData: () {
                        builderContext.read<WalletDetailsBloc>().add(
                          WalletDetailsEvent.fetchFunding(account: account),
                        );
                      },
                      itemBuilder: (ctx, index) {
                        if (index == 0) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: DuploFundingButtons(
                                  onDepositPressed: () {
                                    builderContext.read<WalletDetailsBloc>().add(
                                      WalletDetailsEvent.goToDepositPaymentOptions(),
                                    );
                                  },
                                  onWithdrawPressed: () {
                                    builderContext.read<WalletDetailsBloc>().add(
                                      WalletDetailsEvent.goToWithdrawPaymentOptions(),
                                    );
                                  },
                                  onTransferPressed: () {
                                    builderContext.read<WalletDetailsBloc>().add(
                                      WalletDetailsEvent.goToTransferOptions(),
                                    );
                                  },
                                ),
                              ),
                              if (state.fundingItems.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0,
                                    ),
                                    child: DuploText(
                                      text: loc.trader_fundingActivity,
                                      style: context.duploTextStyles.textMd,
                                      fontWeight: DuploFontWeight.semiBold,
                                      color:
                                          context.duploTheme.text.textPrimary,
                                    ),
                                  ),
                                ),
                            ],
                          );
                        }
                        final itemIndex = index - 1;
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32.0),
                          child: FundingTile(
                            showDate:
                                (itemIndex == 0 ||
                                    !areDatesOnSameDay(
                                      state.fundingItems
                                          .elementAtOrNull(itemIndex)!
                                          .dateTime,
                                      state.fundingItems
                                          .elementAtOrNull(itemIndex - 1)!
                                          .dateTime,
                                    )),
                            fundingItem:
                                state.fundingItems.elementAtOrNull(itemIndex)!,
                            useCurrencyName: true,
                            // ignore: no-empty-block
                            onTap: () {},
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 30),
          ],
        ),
      );
    },
  );
}
