import 'package:clock/clock.dart';
import 'package:domain/domain.dart';
import 'package:e_trader/src/data/api/funding_activity_response.dart';
import 'package:e_trader/src/data/api/funding_request_body.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/domain/usecase/get_funding_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'wallet_details_bloc.freezed.dart';
part 'wallet_details_event.dart';
part 'wallet_details_state.dart';

class WalletDetailsBloc extends Bloc<WalletDetailsEvent, WalletDetailsState> {
  final GetFundingUseCase _getFundingUseCase;
  final Clock _clock;
  final EquitiTraderNavigation _equitiTraderNavigation;

  WalletDetailsBloc(
    this._getFundingUseCase,
    this._clock,
    this._equitiTraderNavigation,
  ) : super(WalletDetailsState()) {
    on<_FetchFunding>(_fetchFunding);
    on<_GoToDepositPaymentOptions>(_goToDepositPaymentOptions);
    on<_GoToWithdrawPaymentOptions>(_goToWithdrawPaymentOptions);
    on<_GoToTransferOptions>(_goToTransferOptions);
  }

  Future<void> _fetchFunding(
    _FetchFunding event,
    Emitter<WalletDetailsState> emit,
  ) async {
    if (event.isStartFromPageOne ?? false) {
      emit(
        state.copyWith(
          processState:
              event.isErrorLoadingButton == true
                  ? WalletDetailsProcessState.buttonLoadingOnly()
                  : WalletDetailsProcessState.loading(),
          fundingItems: [],
          currentPage: 1,
          fromDate: event.fromDate,
          toDate: event.toDate,
        ),
      );
    } else {
      emit(
        state.copyWith(
          processState:
              event.isErrorLoadingButton == true
                  ? WalletDetailsProcessState.buttonLoadingOnly()
                  : WalletDetailsProcessState.loading(),
        ),
      );
    }

    String? accountNumber = event.account.accountNumber;
    String? creationDate = event.account.dateCreated;

    final DateTime? fromDate = event.fromDate ?? state.fromDate;
    final DateTime? toDate = event.toDate ?? state.toDate;

    final String fromDateStr =
        fromDate == null
            ? DateTime.parse(creationDate!).toIso8601String()
            : fromDate.toIso8601String();
    final String toDateStr =
        toDate == null
            ? _clock.now().toIso8601String()
            : toDate.toIso8601String();

    final body = FundingRequestBody(
      isFunding: true,
      accountNumber: accountNumber,
      fromDate: fromDateStr,
      toDate: toDateStr,
      pageNumber: state.currentPage,
      sortingOrder: "Descending",
    );

    final result = await _getFundingUseCase(body).run();
    result.fold(
      (exception) {
        emit(
          state.copyWith(
            processState: WalletDetailsProcessState.error(exception),
          ),
        );
      },
      (fundingResponse) {
        final List<FundingItem> newFundingItems = List<FundingItem>.of(
          state.fundingItems,
        );

        if (fundingResponse.list != null) {
          newFundingItems.addAll(fundingResponse.list!);
        }

        emit(
          state.copyWith(
            processState: WalletDetailsProcessState.success(),
            fundingItems: newFundingItems,
            currentPage: state.currentPage + 1,
            fundingItemsCount: fundingResponse.totalCount,
            fromDate: fromDate,
            toDate: toDate,
          ),
        );
      },
    );
  }

  void _goToDepositPaymentOptions(
    _GoToDepositPaymentOptions event,
    Emitter<WalletDetailsState> emit,
  ) {
    _equitiTraderNavigation.navigateToDepositOptions(
      depositFlowConfig: DepositFlowConfig(
        origin: EquitiTraderRouteSchema.switchAccountRoute.url,
        depositType: DepositType.additional,
      ),
    );
  }

  void _goToWithdrawPaymentOptions(
    _GoToWithdrawPaymentOptions event,
    Emitter<WalletDetailsState> emit,
  ) {
    _equitiTraderNavigation.navigateToWithdrawOptions();
  }

  void _goToTransferOptions(
    _GoToTransferOptions event,
    Emitter<WalletDetailsState> emit,
  ) {
    _equitiTraderNavigation.goToTransferFundsScreen(
      EquitiTraderRouteSchema.switchAccountRoute.url,
    );
  }
}
