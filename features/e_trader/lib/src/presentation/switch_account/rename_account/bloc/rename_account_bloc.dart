import 'dart:async';

import 'package:e_trader/src/domain/usecase/update_account_details_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'rename_account_event.dart';
part 'rename_account_state.dart';
part 'rename_account_bloc.freezed.dart';

class RenameAccountBloc extends Bloc<RenameAccountEvent, RenameAccountState> {
  final UpdateAccountDetailsUseCase _updateAccountDetailsUseCase;

  RenameAccountBloc(this._updateAccountDetailsUseCase)
    : super(_RenameAccountState()) {
    on<RenameAccountEvent>(
      (event, emit) => switch (event) {
        _Submit() => _onSubmit(event, emit),
        _ValueChanged() => _onValueChanged(event, emit),
      },
    );
  }

  FutureOr<void> _onSubmit(
    _Submit event,
    Emitter<RenameAccountState> emit,
  ) async {
    if (state.fieldState case RenameAccountFieldValidState fieldState) {
      emit(
        state.copyWith(processState: const RenameAccountProcessState.loading()),
      );

      // Call the update account details use case
      final result =
          await _updateAccountDetailsUseCase(
            tradingAccountId: event.accountNumber,
            accountNickname: fieldState.value,
          ).run();

      result.fold(
        (error) {
          emit(
            state.copyWith(
              processState: const RenameAccountProcessState.error(),
            ),
          );
        },
        (success) {
          if (success) {
            emit(
              state.copyWith(
                processState: const RenameAccountProcessState.success(),
              ),
            );
          } else {
            emit(
              state.copyWith(
                processState: const RenameAccountProcessState.error(),
              ),
            );
          }
        },
      );
    }
  }

  FutureOr<void> _onValueChanged(
    _ValueChanged event,
    Emitter<RenameAccountState> emit,
  ) {
    final value = event.value.trim();

    if (value.isEmpty) {
      emit(state.copyWith(fieldState: const RenameAccountFieldState.initial()));
    } else if (value.length >= 1 && value.length <= 36) {
      emit(state.copyWith(fieldState: RenameAccountFieldState.valid(value)));
    } else {
      emit(
        state.copyWith(
          fieldState: const RenameAccountFieldState.invalid(
            'Please enter a name between 1-36 characters',
          ),
        ),
      );
    }
  }
}
