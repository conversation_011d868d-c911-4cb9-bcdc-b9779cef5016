import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/switch_account/rename_account/bloc/rename_account_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/rename_account/widgets/rename_account_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

Future<T?> showRenameAccountBottomSheet<T>(
  BuildContext context,
  TradingAccountModel tradingAccountModel,
) {
  final duploTextStyle = context.duploTextStyles;
  final theme = context.duploTheme;
  final localization = EquitiLocalization.of(context);

  return DuploSheet.showModalSheetV2<T?>(
    context,
    settings: const RouteSettings(name: 'rename_account_bottom_sheet'),
    builder:
        (child) => BlocProvider(
          create: (_) => diContainer<RenameAccountBloc>(),
          child: BlocListener<RenameAccountBloc, RenameAccountState>(
            listenWhen:
                (previous, current) =>
                    previous.processState != current.processState,
            listener: (listenerContext, state) {
              if (state.processState is RenameAccountErrorProcessState) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: DuploText(
                      text: localization.trader_somethingWentWrong,
                      style: duploTextStyle.textSm,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.foreground.fgWhite,
                    ),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              } else if (state.processState
                  is RenameAccountSuccessProcessState) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: DuploText(
                      text: localization.trader_accountRenamedSuccessfully,
                      style: duploTextStyle.textSm,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.foreground.fgWhite,
                    ),
                    showCloseIcon: true,
                    duration: const Duration(days: 365), // Indefinite duration
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                Navigator.of(listenerContext).pop(true);
              }
            },
            child: child,
          ),
        ),
    appBar: DuploAppBar(
      leading: IconButton(
        icon: Assets.images.arrowLeftDirectional(context).svg(),
        onPressed: () => Navigator.pop(context),
      ),
      titleWidget: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DuploText(
            text: localization.trader_renameYourAccount,
            style: duploTextStyle.textSm,
            fontWeight: DuploFontWeight.semiBold,
            color: theme.text.textPrimary,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (tradingAccountModel.nickName.isNotNullOrEmpty)
            DuploText(
              text: tradingAccountModel.nickName,
              style: duploTextStyle.textXxs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      title: '',
      automaticallyImplyLeading: false,
    ),
    content: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: RenameAccountWidget(),
    ),
    bottomBar: BlocBuilder<RenameAccountBloc, RenameAccountState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        final isValid = state.fieldState is RenameAccountFieldValidState;
        final isLoading =
            state.processState is RenameAccountLoadingProcessState;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              DuploButton.defaultPrimary(
                title: localization.trader_confirm,
                isDisabled: !isValid || isLoading,
                isLoading: isLoading,
                onTap: () {
                  if (isValid && !isLoading) {
                    builderContext.read<RenameAccountBloc>().add(
                      RenameAccountEvent.submit(
                        accountNumber: tradingAccountModel.accountNumber,
                      ),
                    );
                  }
                },
              ),
              const SizedBox(height: 12),
              DuploButton.secondary(
                title: localization.trader_cancel,
                onTap: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    ),
  );
}
