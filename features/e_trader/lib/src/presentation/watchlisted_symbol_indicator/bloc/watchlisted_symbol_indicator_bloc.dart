import 'package:e_trader/src/domain/usecase/add_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/check_symbol_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/remove_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/watchlist_local_cache_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

part 'watchlisted_symbol_indicator_bloc.freezed.dart';
part 'watchlisted_symbol_indicator_event.dart';
part 'watchlisted_symbol_indicator_state.dart';

class WatchlistedSymbolIndicatorBloc
    extends
        Bloc<WatchlistedSymbolIndicatorEvent, WatchlistedSymbolIndicatorState> {
  final CheckSymbolWatchlistUseCase _checkSymbolWatchlistUseCase;
  final AddWatchlistUseCase _addWatchlistUseCase;
  final RemoveWatchlistUseCase _removeWatchlistUseCase;
  final WatchlistLocalCacheUseCase _watchlistLocalCacheUseCase;
  final LoggerBase _logger;

  WatchlistedSymbolIndicatorBloc(
    this._addWatchlistUseCase,
    this._removeWatchlistUseCase,
    this._checkSymbolWatchlistUseCase,
    this._watchlistLocalCacheUseCase,
    this._logger,
  ) : super(_WatchlistedSymbolIndicatorState()) {
    on<_CheckSymbolWatchlist>(_checkSymbolWatchlist);
    on<_ToggleSymbolWatchlist>(_toggleSymbolWatchlist);
  }

  Future<void> _checkSymbolWatchlist(
    _CheckSymbolWatchlist event,
    Emitter<WatchlistedSymbolIndicatorState> emit,
  ) async {
    final isWatchlisted = _watchlistLocalCacheUseCase.isWatchlisted(
      event.symbolCode,
    );
    emit(
      state.copyWith(
        status:
            isWatchlisted
                ? WatchlistedSymbolIndicatorStatus.filled
                : WatchlistedSymbolIndicatorStatus.empty,
        watchlistedSymbolIndicatorProcessState:
            WatchlistedSymbolIndicatorProcessState.success(),
      ),
    );

    final result =
        await _checkSymbolWatchlistUseCase(symbolCode: event.symbolCode).run();

    result.fold(
      (l) {
        addError(l);
      },
      (response) {
        if (response == isWatchlisted) {
          return;
        }
        if (response) {
          _watchlistLocalCacheUseCase.addWatchlist(event.symbolCode);
        } else {
          _watchlistLocalCacheUseCase.removeWatchlist(event.symbolCode);
        }
        emit(
          state.copyWith(
            status:
                response
                    ? WatchlistedSymbolIndicatorStatus.filled
                    : WatchlistedSymbolIndicatorStatus.empty,
            watchlistedSymbolIndicatorProcessState:
                WatchlistedSymbolIndicatorProcessState.success(),
          ),
        );
      },
    );
  }

  void _toggleSymbolWatchlist(
    _ToggleSymbolWatchlist event,
    Emitter<WatchlistedSymbolIndicatorState> emit,
  ) async {
    /// Ignore any action if the process is still loading
    if (state.watchlistedSymbolIndicatorProcessState
        is WatchlistedSymbolIndicatorProcessStateLoading) {
      return;
    }
    emit(
      state.copyWith(
        watchlistedSymbolIndicatorProcessState:
            WatchlistedSymbolIndicatorProcessState.loading(),
      ),
    );
    TaskEither<Exception, Object?> result;
    if (state.status == WatchlistedSymbolIndicatorStatus.empty) {
      result = _addWatchlistUseCase(symbolCode: event.symbolCode);
    } else {
      result = _removeWatchlistUseCase(symbolCode: event.symbolCode);
    }
    final result2 = await result.run();
    result2.fold(
      (error) {
        emit(
          state.copyWith(
            watchlistedSymbolIndicatorProcessState:
                WatchlistedSymbolIndicatorProcessState.error(),
          ),
        );
      },
      (right) {
        final addToWatchList =
            state.status == WatchlistedSymbolIndicatorStatus.empty;
        if (addToWatchList) {
          _watchlistLocalCacheUseCase.addWatchlist(event.symbolCode);
        } else {
          _watchlistLocalCacheUseCase.removeWatchlist(event.symbolCode);
        }

        emit(
          state.copyWith(
            status:
                addToWatchList
                    ? WatchlistedSymbolIndicatorStatus.filled
                    : WatchlistedSymbolIndicatorStatus.empty,
            watchlistedSymbolIndicatorProcessState:
                WatchlistedSymbolIndicatorProcessState.success(),
          ),
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error);
    super.addError(error, stackTrace);
  }
}
