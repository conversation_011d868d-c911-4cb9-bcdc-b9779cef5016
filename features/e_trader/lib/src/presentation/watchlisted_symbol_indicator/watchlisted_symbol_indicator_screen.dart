import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/watchlisted_symbol_indicator/bloc/watchlisted_symbol_indicator_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WatchlistedSymbolIndicatorScreen extends StatelessWidget {
  final String symbolCode;
  final ColorMode colorMode;

  const WatchlistedSymbolIndicatorScreen({
    super.key,
    required this.symbolCode,
    this.colorMode = ColorMode.dynamicMode,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<WatchlistedSymbolIndicatorBloc>(
      create:
          (createContext) =>
              diContainer<WatchlistedSymbolIndicatorBloc>()..add(
                WatchlistedSymbolIndicatorEvent.checkSymbolWatchlist(
                  symbolCode: symbolCode,
                ),
              ),
      child: BlocConsumer<
        WatchlistedSymbolIndicatorBloc,
        WatchlistedSymbolIndicatorState
      >(
        listener: (listenerContext, state) {
          if (state.watchlistedSymbolIndicatorProcessState
              is WatchlistedSymbolIndicatorProcessStateError) {
            final toast = DuploToast();
            toast.showToastMessage(
              context: listenerContext,
              widget: DuploToastMessage(
                messageType: ToastMessageType.error,
                titleMessage:
                    EquitiLocalization.of(
                      listenerContext,
                    ).trader_somethingWentWrong,
                descriptionMessage:
                    EquitiLocalization.of(
                      listenerContext,
                    ).trader_unableToUpdateWatchlistPleaseTryAgain,
                onLeadingAction: () => toast.hidesToastMessage(),
                onTap: () => toast.hidesToastMessage(),
                actionButtonTitle: null,
              ),
            );
          }
        },
        buildWhen: (previous, current) {
          if ((previous.status != current.status) ||
              (previous.watchlistedSymbolIndicatorProcessState !=
                  current.watchlistedSymbolIndicatorProcessState)) {
            return true;
          }
          return false;
        },
        builder:
            (builderContext, state) => switch (state
                .watchlistedSymbolIndicatorProcessState) {
              WatchlistedSymbolIndicatorProcessStateLoading() =>
                DuploIconButton.favorite(
                  onTap: () => null,
                  isLoading: true,
                  colorMode: colorMode,
                ),
              WatchlistedSymbolIndicatorProcessStateSuccess() =>
                DuploIconButton.favorite(
                  onTap: () {
                    builderContext.read<WatchlistedSymbolIndicatorBloc>().add(
                      WatchlistedSymbolIndicatorEvent.toggleSymbolWatchlist(
                        symbolCode: symbolCode,
                      ),
                    );
                  },
                  isSelected:
                      state.status == WatchlistedSymbolIndicatorStatus.filled,
                  colorMode: colorMode,
                ),
              WatchlistedSymbolIndicatorProcessStateError() =>
                DuploIconButton.favorite(
                  onTap: () {
                    builderContext.read<WatchlistedSymbolIndicatorBloc>().add(
                      WatchlistedSymbolIndicatorEvent.toggleSymbolWatchlist(
                        symbolCode: symbolCode,
                      ),
                    );
                  },
                  isSelected: false,
                  colorMode: colorMode,
                ),
            },
      ),
    );
  }
}
