import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/reset_balance/bloc/reset_balance_bloc.dart';
import 'package:e_trader/src/presentation/reset_balance/widgets/reset_balance_header.dart';
import 'package:e_trader/src/presentation/reset_balance/widgets/reset_balance_amount_field.dart';
import 'package:e_trader/src/presentation/reset_balance/widgets/reset_balance_actions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ResetBalanceScreen extends StatefulWidget {
  const ResetBalanceScreen({
    super.key,
    required this.accountNumber,
    required this.accountCurrency,
  });
  final String accountNumber;
  final String accountCurrency;

  @override
  State<ResetBalanceScreen> createState() => _ResetBalanceScreenState();
}

class _ResetBalanceScreenState extends State<ResetBalanceScreen> {
  final TextEditingController _amountFieldController = TextEditingController();

  @override
  void dispose() {
    _amountFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ResetBalanceBloc>(
      create: (_) => diContainer<ResetBalanceBloc>(),
      child: BlocListener<ResetBalanceBloc, ResetBalanceState>(
        listenWhen:
            (previous, current) =>
                current.currentState ==
                const ResetBalanceProcessState.resetBalanceSuccess(),
        listener: (listenerContext, state) {
          Navigator.of(context).pop();
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: BlocBuilder<ResetBalanceBloc, ResetBalanceState>(
            buildWhen: (previous, current) => false,
            builder: (builderContext, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 30),
                  ResetBalanceHeader(
                    accountNumber: widget.accountNumber,
                    homeCurrency: widget.accountCurrency,
                  ),
                  const SizedBox(height: 10),

                  ResetBalanceAmountField(
                    controller: _amountFieldController,
                    homeCurrency: widget.accountCurrency,
                    onChanged: (value) {
                      final amount = parseFormattedNumber(value);
                      _amountFieldController
                          .text = EquitiFormatter.formatNumber(
                        value: amount,
                        locale: "en",
                      );
                      builderContext.read<ResetBalanceBloc>().add(
                        ResetBalanceEvent.changeButtonStatus(amount),
                      );
                    },
                  ),
                  const SizedBox(height: 30),
                  ResetBalanceActions(
                    onResetBalance: () {
                      final amount =
                          parseFormattedNumber(
                            _amountFieldController.text,
                          ).toDouble();
                      builderContext.read<ResetBalanceBloc>().add(
                        ResetBalanceEvent.resetBalance(
                          amount,
                          widget.accountNumber,
                        ),
                      );
                    },
                    onCancel: () {
                      Navigator.pop(context);
                    },
                  ),
                  const SizedBox(height: 30),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  num parseFormattedNumber(String formattedNumber) {
    final unformattedNumber = formattedNumber.replaceAll(',', '');
    return num.tryParse(unformattedNumber) ?? 0;
  }
}
