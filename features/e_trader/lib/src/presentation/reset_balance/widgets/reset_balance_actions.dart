import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/reset_balance/bloc/reset_balance_bloc.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResetBalanceActions extends StatelessWidget {
  const ResetBalanceActions({
    super.key,
    required this.onResetBalance,
    required this.onCancel,
  });

  final VoidCallback onResetBalance;
  final VoidCallback onCancel;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: BlocBuilder<ResetBalanceBloc, ResetBalanceState>(
            buildWhen:
                (previous, current) =>
                    current.isButtonEnabled != previous.isButtonEnabled ||
                    current.isButtonLoading != previous.isButtonLoading,
            builder: (builderContext, state) {
              return DuploButton.defaultPrimary(
                semanticsIdentifier: 'reset_balance_button',
                isDisabled: !state.isButtonEnabled,
                isLoading: state.isButtonLoading,
                title:
                    EquitiLocalization.of(builderContext).trader_reset_balance,
                onTap: onResetBalance,
              );
            },
          ),
        ),
        const SizedBox(height: 13),
        SizedBox(
          width: double.infinity,
          child: DuploButton.secondary(
            title: EquitiLocalization.of(context).trader_cancel,
            onTap: onCancel,
          ),
        ),
      ],
    );
  }
}
