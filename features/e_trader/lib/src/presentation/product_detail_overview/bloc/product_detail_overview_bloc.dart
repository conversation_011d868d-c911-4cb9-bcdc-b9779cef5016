import 'dart:async';

import 'package:e_trader/src/domain/model/product_detail_info.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/product_detail_info_usecase.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'product_detail_overview_bloc.freezed.dart';
part 'product_detail_overview_event.dart';
part 'product_detail_overview_state.dart';

class ProductDetailOverviewBloc
    extends Bloc<ProductDetailOverviewEvent, ProductDetailOverviewState> {
  final ProductDetailInfoUseCase _productDetailOverviewUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;

  ProductDetailOverviewBloc(
    this._productDetailOverviewUseCase,
    this._getSelectedAccountUseCase,
  ) : super(ProductDetailOverviewLoading()) {
    on<_FetchSymbolInfo>(_onGetProductDetailOverview);
  }

  FutureOr<void> _onGetProductDetailOverview(
    _FetchSymbolInfo event,
    Emitter<ProductDetailOverviewState> emit,
  ) async {
    final account = await _getSelectedAccountUseCase();
    if (account == null) {
      addError(Exception('No selected account'));
      emit(ProductDetailOverviewError());
      return;
    }

    try {
      final result =
          await TaskEither.sequenceList([
            _productDetailOverviewUseCase(
              accountNumber: account.accountNumber,
              symbolCode: event.symbol,
            ),
          ]).run();
      result.fold(
        (left) {
          addError(left);
          emit(ProductDetailOverviewError());
        },
        (onRight) {
          final productDetailResult =
              onRight.elementAtOrNull(0) as ProductDetailInfo;
          return emit(
            ProductDetailOverviewSuccess(
              productDetailResult,
              account.isSwapFree == false,
            ),
          );
        },
      );
    } catch (e) {
      addError(e);
      if (!isClosed) emit(ProductDetailOverviewError());
    }
  }
}
