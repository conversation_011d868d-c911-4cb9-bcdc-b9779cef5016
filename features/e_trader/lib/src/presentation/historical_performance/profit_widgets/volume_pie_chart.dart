import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class VolumePieChart extends StatelessWidget {
  final List<Color> chartColors;
  final Map<String, double> categoryVolumes;
  const VolumePieChart({
    super.key,
    required this.chartColors,
    required this.categoryVolumes,
  });

  @override
  Widget build(BuildContext context) {
    List<PieChartSectionData> generateSections(
      Map<String, double> volumes,
      BuildContext ctx,
    ) {
      final totalVolume = volumes.values.fold(
        0.0,
        (sum, volume) => sum + volume,
      );

      if (totalVolume == 0) return [];

      final categoryList = volumes.keys.toList()..sort();

      return categoryList.asMap().entries.map((entry) {
        final index = entry.key;
        final category = entry.value;
        final volume = volumes[category]!;
        final percentage = (volume / totalVolume) * 100;
        final color =
            chartColors.elementAtOrNull(index) ??
            ctx.duploTheme.utility.utilityBrand800;

        return PieChartSectionData(
          color: color,
          value: volume,
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 25,
          badgePositionPercentageOffset: 2,
          showTitle: false,
          badgeWidget: DuploText(
            text: "${percentage.toStringAsFixed(1)}%",
            style: ctx.duploTextStyles.textSm,
            fontWeight: DuploFontWeight.bold,
            color: ctx.duploTheme.text.textSecondary,
          ),
          titlePositionPercentageOffset: 0.6,
        );
      }).toList();
    }

    final pieChartSections = generateSections(categoryVolumes, context);

    if (categoryVolumes.isEmpty) {
      return Container(
        height: 250,
        width: double.infinity,
        child: Center(
          child: DuploText(
            text: EquitiLocalization.of(context).trader_noInformationAvailable,
            style: context.duploTextStyles.textSm,
            color: context.duploTheme.text.textTertiary,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (layoutBuilderContext, constraints) {
        return Container(
          height: 220,
          child: PieChart(
            PieChartData(
              sectionsSpace: 3,
              sections: pieChartSections,
              borderData: FlBorderData(show: false),
              centerSpaceRadius:
                  constraints.maxWidth <= 310
                      ? constraints.maxWidth * 0.17
                      : constraints.maxWidth * 0.19,
              startDegreeOffset: -90,
            ),
          ),
        );
      },
    );
  }
}
