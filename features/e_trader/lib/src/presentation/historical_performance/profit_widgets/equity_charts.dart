import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/chart_header.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/equity_profit_bar_chart.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/equity_profit_linear_chart.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EquityCharts extends StatelessWidget {
  const EquityCharts({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);

    return BlocBuilder<HistoricalPerformanceBloc, HistoricalPerformanceState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocContext, state) {
        return Column(
          children: [
            ChartHeader(
              title: l10n.trader_equity,
              isFirstHighlighted: state.equityChartType == ChartType.linear,
              onSelectFirstType:
                  () => context.read<HistoricalPerformanceBloc>().add(
                    HistoricalPerformanceEvent.changeEquityChartType(
                      ChartType.linear,
                    ),
                  ),
              isSecondHighlighted: state.equityChartType == ChartType.bar,
              onSelectSecondType:
                  () => context.read<HistoricalPerformanceBloc>().add(
                    HistoricalPerformanceEvent.changeEquityChartType(
                      ChartType.bar,
                    ),
                  ),
            ),
            state.equityChartType == ChartType.bar
                ? EquityProfitBarChart(data: state.profitModel ?? [])
                : EquityProfitLinearChart(data: state.profitModel ?? []),
          ],
        );
      },
    );
  }
}
