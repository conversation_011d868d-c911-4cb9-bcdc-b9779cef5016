import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/historical_equity_profit_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:prelude/prelude.dart';

class EquityProfitBarChart extends StatelessWidget {
  final List<HistoricalEquityProfitModel> data;
  final ChartDataType dataType;

  const EquityProfitBarChart({
    super.key,
    required this.data,
    this.dataType = ChartDataType.equity,
  });

  @override
  Widget build(BuildContext context) {
    List<BarChartGroupData> generateMonthlyBarData(
      List<HistoricalEquityProfitModel> history,
    ) {
      final now = diContainer<Clock>().now();
      final last6Months = List.generate(6, (i) {
        final month = DateTime(now.year, now.month - (5 - i));
        return DateTime(month.year, month.month);
      });

      final Map<String, List<HistoricalEquityProfitModel>> groupedByMonth = {};

      for (var item in history) {
        final value = _getValueFromItem(item);
        if (value == 0 && dataType == ChartDataType.equity) continue;

        final key =
            '${EquitiFormatter.formatNumber(value: item.endOfDayDate.year, locale: 'en')}-${EquitiFormatter.formatNumber(value: item.endOfDayDate.month, locale: 'en').padLeft(2, '0')}';
        groupedByMonth.putIfAbsent(key, () => []).add(item);
      }

      List<BarChartGroupData> barGroups = [];
      for (int i = 0; i < last6Months.length; i++) {
        final month = last6Months[i];
        final key =
            '${EquitiFormatter.formatNumber(value: month.year, locale: "en")}-${EquitiFormatter.formatNumber(value: month.month, locale: "en").padLeft(2, '0')}';
        final groupedData = groupedByMonth[key];

        final value =
            groupedData != null && groupedData.isNotEmpty
                ? _calculateAverageValue(groupedData)
                : 0.0;

        barGroups.add(
          BarChartGroupData(
            x: i,
            barRods: [
              BarChartRodData(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(4),
                  bottom: value < 0 ? Radius.circular(4) : Radius.zero,
                ),
                toY: value,
                fromY: dataType == ChartDataType.realizedPL ? 0 : null,
                color: _getBarColor(context, value),
                width: 32,
              ),
            ],
          ),
        );
      }

      return barGroups;
    }

    final barGroups = generateMonthlyBarData(data);
    final now = diContainer<Clock>().now();
    final labels = List.generate(6, (i) {
      final date = DateTime(now.year, now.month - (5 - i));
      return DateFormat.MMM().format(date);
    });

    return Container(
      height: 220,
      width: double.infinity,
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.start,
          barGroups: barGroups,
          borderData: FlBorderData(show: false),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color:
                    value == 0 && dataType == ChartDataType.realizedPL
                        ? context.duploTheme.utility.utilityBrand600
                        : context.duploTheme.border.borderSecondary,
                strokeWidth:
                    value == 0 && dataType == ChartDataType.realizedPL ? 2 : 1,
                dashArray:
                    value == 0 && dataType == ChartDataType.realizedPL
                        ? [5, 5]
                        : null,
              );
            },
          ),

          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, _) {
                  final index = value.toInt();
                  if (index < 0 || index >= labels.length)
                    return const SizedBox();
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: DuploText(
                      text: labels.elementAtOrNull(index),
                      style: context.duploTextStyles.textXs,
                      color: context.duploTheme.text.textTertiary,
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                maxIncluded: false,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return DuploText(
                    textAlign: TextAlign.center,
                    text: _formatValue(value),
                    style: context.duploTextStyles.textXs,
                    color: context.duploTheme.text.textTertiary,
                  );
                },
              ),
            ),
          ),
          maxY: _calculateMaxValue(barGroups),
          minY: _calculateMinValue(barGroups),
        ),
      ),
    );
  }

  double _getValueFromItem(HistoricalEquityProfitModel item) {
    switch (dataType) {
      case ChartDataType.equity:
        return item.equity;
      case ChartDataType.realizedPL:
        return item.realizedPL;
    }
  }

  double _calculateAverageValue(List<HistoricalEquityProfitModel> monthData) {
    if (monthData.isEmpty) return 0.0;

    final sum = monthData.fold<double>(
      0.0,
      (acc, item) => acc + _getValueFromItem(item),
    );
    return sum / monthData.length;
  }

  Color _getBarColor(BuildContext context, double value) {
    if (dataType == ChartDataType.realizedPL) {
      if (value > 0) {
        return context.duploTheme.utility.utilityBrand500;
      } else if (value < 0) {
        return context.duploTheme.utility.utilityGray100;
      }
    }
    return context.duploTheme.utility.utilityBrand500;
  }

  String _formatValue(double value) {
    if (dataType == ChartDataType.realizedPL && value < 0) {
      return '-${EquitiFormatter.formatCompactCurrency(value: value.abs().toInt(), digits: 0)}';
    }
    return EquitiFormatter.formatCompactCurrency(
      value: value.toInt(),
      digits: 0,
    );
  }

  double _calculateMaxValue(List<BarChartGroupData> barGroups) {
    final values =
        barGroups
            .map((e) => e.barRods.firstOrNull?.toY)
            .whereType<double>()
            .toList();
    if (values.isEmpty) return 1000;
    final max = values.reduce((a, b) => a > b ? a : b);

    if (dataType == ChartDataType.realizedPL) {
      final min = values.reduce((a, b) => a < b ? a : b);
      if (min < 0) {
        return (min.abs() * 1.5).ceilToDouble();
      }
      return max > 0 ? (max * 1.5).ceilToDouble() : 100;
    }

    return max > 0 ? (max * 1.5).ceilToDouble() : (max * 0.8).ceilToDouble();
  }

  double _calculateMinValue(List<BarChartGroupData> barGroups) {
    if (dataType == ChartDataType.equity) return 0;

    final values =
        barGroups
            .map((e) => e.barRods.firstOrNull?.toY)
            .whereType<double>()
            .toList();
    if (values.isEmpty) return 0;
    final min = values.reduce((a, b) => a < b ? a : b);

    return min < 0 ? (min * 1.1).floorToDouble() : 0;
  }
}
