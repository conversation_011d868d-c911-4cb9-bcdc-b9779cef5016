import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/historical_equity_profit_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:prelude/prelude.dart';

class EquityProfitLinearChart extends StatelessWidget {
  final List<HistoricalEquityProfitModel> data;
  final ChartDataType dataType;

  const EquityProfitLinearChart({
    super.key,
    required this.data,
    this.dataType = ChartDataType.equity,
  });

  @override
  Widget build(BuildContext context) {
    List<FlSpot> generateMonthlyLineData(
      List<HistoricalEquityProfitModel> history,
    ) {
      final now = diContainer<Clock>().now();
      final last6Months = List.generate(6, (i) {
        final month = DateTime(now.year, now.month - (5 - i));
        return DateTime(month.year, month.month);
      });

      final Map<String, List<HistoricalEquityProfitModel>> groupedByMonth = {};

      for (var item in history) {
        final value = _getValueFromItem(item);
        if (value == 0 && dataType == ChartDataType.equity) continue;

        final key =
            '${EquitiFormatter.formatNumber(value: item.endOfDayDate.year, locale: 'en')}-${EquitiFormatter.formatNumber(value: item.endOfDayDate.month, locale: 'en').padLeft(2, '0')}';
        groupedByMonth.putIfAbsent(key, () => []).add(item);
      }

      List<FlSpot> spots = [];
      for (int i = 0; i < last6Months.length; i++) {
        final month = last6Months[i];
        final key =
            '${EquitiFormatter.formatNumber(value: month.year, locale: "en")}-${EquitiFormatter.formatNumber(value: month.month, locale: "en").padLeft(2, '0')}';
        final groupedData = groupedByMonth[key];

        final value =
            groupedData != null && groupedData.isNotEmpty
                ? _calculateAverageValue(groupedData)
                : 0.0;

        spots.add(FlSpot(i.toDouble(), value));
      }

      return spots;
    }

    final spots = generateMonthlyLineData(data);
    final now = diContainer<Clock>().now();
    final labels = List.generate(6, (i) {
      final date = DateTime(now.year, now.month - (5 - i));
      return DateFormat.MMM().format(date); // Jan, Feb, etc.
    });

    return Container(
      height: 220,
      width: double.infinity,
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color:
                    value == 0 && dataType == ChartDataType.realizedPL
                        ? context.duploTheme.border.borderPrimary
                        : context.duploTheme.border.borderSecondary,
                strokeWidth:
                    value == 0 && dataType == ChartDataType.realizedPL ? 0 : 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                interval: 1,
                showTitles: true,
                maxIncluded: false,
                minIncluded: false,
                getTitlesWidget: (value, _) {
                  final index = value.toInt();
                  if (index < 0 || index >= labels.length)
                    return const SizedBox();
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),

                    child: DuploText(
                      text: labels.elementAtOrNull(index),
                      style: context.duploTextStyles.textXs,
                      color: context.duploTheme.text.textTertiary,
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                maxIncluded: false,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return DuploText(
                    textAlign: TextAlign.center,
                    text: _formatValue(value),
                    style: context.duploTextStyles.textXs,
                    color: context.duploTheme.text.textTertiary,
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(show: false),
          minX: -0.2,
          maxX: 5.8,
          maxY: _calculateMaxValue(spots),
          minY: _calculateMinValue(spots),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              shadow: Shadow(color: context.duploTheme.utility.utilityBrand600),
              isCurved: false,
              color: context.duploTheme.utility.utilityBrand600,
              barWidth: 2,
              isStrokeCapRound: false,
              dotData: FlDotData(show: false),
            ),
          ],
        ),
      ),
    );
  }

  double _getValueFromItem(HistoricalEquityProfitModel item) {
    switch (dataType) {
      case ChartDataType.equity:
        return item.equity;
      case ChartDataType.realizedPL:
        return item.realizedPL;
    }
  }

  double _calculateAverageValue(List<HistoricalEquityProfitModel> monthData) {
    if (monthData.isEmpty) return 0.0;

    final total = monthData.fold<double>(
      0.0,
      (acc, item) => acc + _getValueFromItem(item),
    );
    return total / monthData.length;
  }

  String _formatValue(double value) {
    if (dataType == ChartDataType.realizedPL && value < 0) {
      return '-${EquitiFormatter.formatCompactCurrency(value: value.abs().toInt(), digits: 0)}';
    }
    return EquitiFormatter.formatCompactCurrency(
      value: value.toInt(),
      digits: 0,
    );
  }

  double _calculateMaxValue(List<FlSpot> spots) {
    final values = spots.map((e) => e.y).toList();
    if (values.isEmpty) return 1000;
    final max = values.reduce((a, b) => a > b ? a : b);

    if (dataType == ChartDataType.realizedPL) {
      final min = values.reduce((a, b) => a < b ? a : b);
      if (min < 0) {
        return (min.abs() * 1.5).ceilToDouble();
      }
      return max > 0 ? (max * 1.5).ceilToDouble() : 100;
    }
    return (max * 1.5).ceilToDouble();
  }

  double _calculateMinValue(List<FlSpot> spots) {
    if (dataType == ChartDataType.equity) return 0;

    final values = spots.map((e) => e.y).toList();
    if (values.isEmpty) return 0;
    final min = values.reduce((a, b) => a < b ? a : b);

    return min < 0 ? (min * 1.1).floorToDouble() : 0;
  }
}
