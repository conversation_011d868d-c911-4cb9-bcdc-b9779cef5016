import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/historical_volume_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:prelude/prelude.dart';

class VolumeStackedBarChart extends StatelessWidget {
  final List<HistoricalVolumeModel> volumeData;

  const VolumeStackedBarChart({super.key, required this.volumeData});

  @override
  Widget build(BuildContext context) {
    List<DateTime> generateLast6Months() {
      final now = diContainer<Clock>().now();
      final months = <DateTime>[];
      for (int i = 0; i < 6; i++) {
        final monthDate = DateTime(now.year, now.month - (5 - i), 1);
        months.add(monthDate);
      }

      return months;
    }

    List<GroupedVolumeData> groupDataByMonth(List<HistoricalVolumeModel> data) {
      final Map<DateTime, Map<String, double>> groupedMap = {};
      final last6Months = generateLast6Months();
      for (final month in last6Months) {
        groupedMap[month] = {};
      }
      final now = diContainer<Clock>().now();
      final sixMonthsAgo = DateTime(now.year, now.month - 5, now.day);
      final currentMonthEnd = DateTime(now.year, now.month + 1, 1);

      for (final item in data) {
        if (item.eodDate.isAfter(sixMonthsAgo) &&
            item.eodDate.isBefore(currentMonthEnd)) {
          final monthKey = DateTime(item.eodDate.year, item.eodDate.month, 1);

          if (groupedMap.containsKey(monthKey)) {
            groupedMap[monthKey]![item.productCategory] =
                (groupedMap[monthKey]![item.productCategory] ?? 0) +
                item.volume;
          }
        }
      }

      final groupedList =
          groupedMap.entries.map((entry) {
            final totalVolume = entry.value.values.fold(
              0.0,
              (sum, volume) => sum + volume,
            );
            return GroupedVolumeData(
              date: entry.key,
              categoryVolumes: entry.value,
              totalVolume: totalVolume,
            );
          }).toList();

      groupedList.sort((a, b) => a.date.compareTo(b.date));
      return groupedList;
    }

    List<Color> getCategoriesColors() {
      List<Color> marginAllocationColors = [
        context.duploTheme.utility.utilityBrand800,
        context.duploTheme.utility.utilityBrand700,
        context.duploTheme.utility.utilityBrand600,
        context.duploTheme.utility.utilityBrand500,
        context.duploTheme.utility.utilityBrand400,
        context.duploTheme.utility.utilityBrand300,
        context.duploTheme.utility.utilityBrand200,
        context.duploTheme.utility.utilityBrand100,
      ];
      return marginAllocationColors;
    }

    List<BarChartGroupData> generateStackedBarData(
      List<GroupedVolumeData> data,
    ) {
      final allCategories = <String>{};
      for (final item in data) {
        allCategories.addAll(item.categoryVolumes.keys);
      }
      final categoryList = allCategories.toList()..sort();
      final colors = getCategoriesColors();
      Map<String, Color> categoryColorMap = {};

      return data.asMap().entries.map((entry) {
        final index = entry.key;
        final groupData = entry.value;

        final rodStackItems = <BarChartRodStackItem>[];
        double currentY = 0;

        for (final category in categoryList) {
          final volume = groupData.categoryVolumes[category] ?? 0;
          if (volume > 0) {
            for (int i = 0; i < categoryList.length; i++) {
              categoryColorMap[categoryList[i]] =
                  colors.elementAtOrNull(i) ??
                  context.duploTheme.utility.utilityGray700;

              rodStackItems.add(
                BarChartRodStackItem(
                  currentY,
                  currentY + volume,
                  categoryColorMap[categoryList[i]] ??
                      context.duploTheme.utility.utilityBrand800,
                ),
              );
            }
            currentY += volume;
          }
        }

        return BarChartGroupData(
          x: index,
          barRods: [
            BarChartRodData(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(4),
              ),
              toY: groupData.totalVolume > 0 ? groupData.totalVolume : 0,
              width: 32,
              rodStackItems: rodStackItems,
            ),
          ],
        );
      }).toList();
    }

    final groupedData = groupDataByMonth(volumeData);
    final barGroups = generateStackedBarData(groupedData);

    final dateLabels =
        groupedData.map((d) => DateFormat.MMM().format(d.date)).toList();

    return Container(
      height: 220,
      width: double.infinity,
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.start,
          barGroups: barGroups,
          borderData: FlBorderData(show: false),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: context.duploTheme.border.borderSecondary,
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, _) {
                  final index = value.toInt();
                  if (index < 0 || index >= dateLabels.length)
                    return const SizedBox();
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: DuploText(
                      text: dateLabels.elementAtOrNull(index),
                      style: context.duploTextStyles.textXs,
                      color: context.duploTheme.text.textTertiary,
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                maxIncluded: false,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return DuploText(
                    textAlign: TextAlign.center,
                    text: EquitiFormatter.formatCompactCurrency(
                      value: value.toInt(),
                      digits: 0,
                    ),
                    style: context.duploTextStyles.textXs,
                    color: context.duploTheme.text.textTertiary,
                  );
                },
              ),
            ),
          ),
          maxY: calculateMaxY(groupedData),
        ),
      ),
    );
  }

  double calculateMaxY(List<GroupedVolumeData> data) {
    if (data.isEmpty) return 100;

    final maxVolume = data
        .map((e) => e.totalVolume)
        .fold(0.0, (prev, next) => prev > next ? prev : next);

    return maxVolume > 0 ? maxVolume * 1.5 : 100;
  }
}
