import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/historical_volume_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/chart_header.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/volume_pie_chart.dart';

import 'package:e_trader/src/presentation/historical_performance/profit_widgets/volume_stacked_bar_chart.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as assetsTrader;

class VolumeCharts extends StatelessWidget {
  const VolumeCharts({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    return BlocBuilder<HistoricalPerformanceBloc, HistoricalPerformanceState>(
      buildWhen:
          (previous, current) =>
              previous != current && current.volumeModel != null,
      builder: (blocContext, state) {
        List<Color> getCategoriesColors() {
          List<Color> chartColors = [
            context.duploTheme.utility.utilityBrand800,
            context.duploTheme.utility.utilityBrand700,
            context.duploTheme.utility.utilityBrand600,
            context.duploTheme.utility.utilityBrand500,
            context.duploTheme.utility.utilityBrand400,
            context.duploTheme.utility.utilityBrand300,
            context.duploTheme.utility.utilityBrand200,
            context.duploTheme.utility.utilityBrand100,
          ];
          return chartColors;
        }

        List<Widget> generateLegend(Map<String, double> categoryVolumes) {
          final colors = getCategoriesColors();
          final categoryList = categoryVolumes.keys.toList()..sort();

          return categoryList.asMap().entries.map((entry) {
            final index = entry.key;
            final category = entry.value;

            final color =
                colors.elementAtOrNull(index) ??
                context.duploTheme.utility.utilityGray700;

            return Padding(
              padding: const EdgeInsets.only(top: 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  DuploText(
                    text: category,
                    style: context.duploTextStyles.textSm,
                    color: context.duploTheme.text.textPlaceholder,
                  ),
                  const SizedBox(width: 8),
                ],
              ),
            );
          }).toList();
        }

        // Aggregate volume by category
        Map<String, double> aggregateVolumeByCategory(
          List<HistoricalVolumeModel> data,
        ) {
          final Map<String, double> categoryVolumes = {};

          for (final item in data) {
            categoryVolumes[item.productCategory] =
                (categoryVolumes[item.productCategory] ?? 0) + item.volume;
          }

          return categoryVolumes;
        }

        List<HistoricalVolumeModel> filterLast6Months(
          List<HistoricalVolumeModel> data,
        ) {
          final now = diContainer<Clock>().now();
          final sixMonthsAgo = DateTime(now.year, now.month - 5, now.day);

          return data
              .where((item) => item.eodDate.isAfter(sixMonthsAgo))
              .toList();
        }

        final filteredData = filterLast6Months(state.volumeModel ?? []);

        final categoryVolumes = aggregateVolumeByCategory(filteredData);

        final legendItems = generateLegend(categoryVolumes);
        return Column(
          children: [
            ChartHeader(
              title: l10n.trader_volume,
              onSelectFirstType:
                  () => context.read<HistoricalPerformanceBloc>().add(
                    HistoricalPerformanceEvent.changeVolumeChartType(
                      ChartType.pie,
                    ),
                  ),
              isFirstHighlighted: state.volumeChartType == ChartType.pie,
              firstButtonIcon: assetsTrader.Assets.images.pieChart.svg(
                height: 20,
                width: 20,
                colorFilter: ColorFilter.mode(
                  context.duploTheme.text.textPrimary,
                  BlendMode.srcIn,
                ),
              ),
              onSelectSecondType:
                  () => context.read<HistoricalPerformanceBloc>().add(
                    HistoricalPerformanceEvent.changeVolumeChartType(
                      ChartType.stackedBar,
                    ),
                  ),
              isSecondHighlighted:
                  state.volumeChartType == ChartType.stackedBar,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Container(
                height: 300,
                width: double.infinity,
                child: Column(
                  children: [
                    Container(
                      child:
                          state.volumeChartType == ChartType.pie
                              ? VolumePieChart(
                                categoryVolumes: categoryVolumes,
                                chartColors: getCategoriesColors(),
                              )
                              : VolumeStackedBarChart(
                                volumeData: state.volumeModel ?? [],
                              ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [...legendItems],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
