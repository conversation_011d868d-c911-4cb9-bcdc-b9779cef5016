import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';

import 'package:e_trader/src/presentation/historical_performance/profit_widgets/equity_charts.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/performance_loading.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/profit_charts.dart';
import 'package:e_trader/src/presentation/historical_performance/profit_widgets/volume_charts.dart';

import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as assetsTrader;

class HistoricalPerformanceScreen extends StatelessWidget {
  const HistoricalPerformanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: context.duploTheme.background.bgPrimary,
      appBar: DuploAppBar(title: l10n.trader_performance),
      body: BlocProvider(
        create:
            (ctx) =>
                diContainer<HistoricalPerformanceBloc>()
                  ..add(HistoricalPerformanceEvent.fetchEquityAndProfit()),
        child: BlocBuilder<
          HistoricalPerformanceBloc,
          HistoricalPerformanceState
        >(
          buildWhen:
              (previous, current) =>
                  previous.performanceProcessState !=
                  current.performanceProcessState,
          builder:
              (builderContext, state) => switch (state
                  .performanceProcessState) {
                HistoricalPerformanceProcessStateSuccess() => ListView(
                  padding: EdgeInsets.all(16),
                  physics: ClampingScrollPhysics(),
                  children: [
                    DuploText(
                      text: l10n.trader_performanceTitle,
                      style: context.duploTextStyles.textLg,
                      fontWeight: DuploFontWeight.semiBold,
                      color: context.duploTheme.text.textPrimary,
                    ),
                    SizedBox(height: 8),
                    DuploText(
                      text: l10n.trader_performancesSubTitle,
                      style: context.duploTextStyles.textSm,
                      fontWeight: DuploFontWeight.regular,
                      color: context.duploTheme.text.textSecondary,
                    ),
                    SizedBox(height: 12),
                    EquityCharts(),
                    SizedBox(height: 24),
                    ProfitCharts(),
                    SizedBox(height: 24),
                    Divider(color: context.duploTheme.border.borderSecondary),
                    VolumeCharts(),
                  ],
                ),
                HistoricalPerformanceProcessStateLoading() =>
                  PerformanceLoading(),

                HistoricalPerformanceProcessStateError() => CustomScrollView(
                  physics: const ClampingScrollPhysics(),
                  slivers: [
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.empty(
                        svgImage:
                            assetsTrader.Assets.images.insightsError.svg(),
                        title:
                            EquitiLocalization.of(
                              builderContext,
                            ).trader_insightsSomethingWentWrong,
                        description:
                            EquitiLocalization.of(
                              builderContext,
                            ).trader_insightsErrorDescription,
                      ),
                    ),
                  ],
                ),
              },
        ),
      ),
    );
  }
}
