import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/positions_loading.dart';
import 'package:e_trader/src/presentation/position_option/widget/show_position_option_sheet.dart';
import 'package:e_trader/src/presentation/positions_and_trades/expandable_position_header.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:visibility_detector/visibility_detector.dart';

class PositionsTab extends StatefulWidget {
  const PositionsTab({super.key});

  @override
  State<PositionsTab> createState() => _PositionsTabState();
}

class _PositionsTabState extends State<PositionsTab>
    with PerformanceObserverMixin {
  bool _hasSubscribed = false;
  bool _hasRegistered = false;

  @override
  void onRoutePopped(Route<Object?> route) {
    _subscribe();
    super.onRoutePopped(route);
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    _unsubscribe();
    super.onRoutePushed(route);
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      context.read<PositionBloc>().add(
        PositionEvent.updatePositions(TradingSocketEvent.positions.subscribe),
      );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdatePositionsUseCase>().call(
          eventType: TradingSocketEvent.positions.unsubscribe,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return VisibilityDetector(
      key: const Key('positions_tab'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0) {
          if (!_hasRegistered) {
            context.read<PositionBloc>().add(PositionEvent.loadPositions());
            _hasRegistered = true;
          }
        } else {
          if (_hasRegistered) {
            _unsubscribe();
            _hasRegistered = false;
          }
        }
      },
      //Don't remove this SizedBox as it is required for the visibility detector to work
      child: SizedBox(
        child: CustomScrollView(
          slivers: [
            BlocConsumer<PositionBloc, PositionState>(
              listenWhen:
                  (previous, current) =>
                      previous.processState != current.processState &&
                      current.processState is PositionConnectedState,
              listener: (listenerContext, state) => _subscribe(),
              buildWhen: (previous, current) => previous != current,
              builder:
                  (blocBuilderContext, state) => switch (state.processState) {
                    PositionLoadingState() => const PositionsLoading(),
                    PositionSuccessState() || PositionConnectedState() => () {
                      if (state.groupedPositions.isEmpty) {
                        return SliverFillRemaining(
                          hasScrollBody: false,
                          child: Center(
                            child: EmptyOrErrorStateComponent.empty(
                              svgImage:
                                  trader
                                      .Assets
                                      .images
                                      .portfolioEmptyPositionList
                                      .svg(),
                              title: localization.trader_noOpenPositions,
                              description:
                                  localization
                                      .trader_noOpenPositionsDescription,
                            ),
                          ),
                        );
                      }
                      return SliverMainAxisGroup(
                        slivers: [
                          SliverList.builder(
                            itemCount: state.groupedPositions.length,
                            itemBuilder: (listViewContext, index) {
                              final groupedPosition =
                                  state.groupedPositions.values.elementAtOrNull(
                                    index,
                                  )!;

                              return ExpandablePositionHeader(
                                productName: groupedPosition.symbol,
                                productIcon: groupedPosition.url,
                                profit: groupedPosition.totalProfit,
                                margin: groupedPosition.totalMargin,
                                isHedging: groupedPosition.isHedged,
                                tradeType: groupedPosition.groupTradeType,
                                lots: groupedPosition.totalLotSize,
                                tradesData: groupedPosition.positions,
                                currency: state.selectedAccountCurrency,
                                onTap:
                                    () => showPositionOptionSheet(
                                      blocBuilderContext,
                                      SymbolDetailViewModel(
                                        symbolName: groupedPosition.symbol,
                                        minLot:
                                            groupedPosition
                                                .positions
                                                .firstOrNull
                                                ?.minLot ??
                                            0.0,
                                        maxLot:
                                            groupedPosition
                                                .positions
                                                .firstOrNull
                                                ?.maxLot ??
                                            0.0,
                                        imageURL: groupedPosition.url,
                                      ),
                                      state.selectedAccountCurrency,
                                    ),
                              );
                            },
                          ),
                        ],
                      );
                    }(),
                    PositionEmptyState() => SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.empty(
                        svgImage:
                            trader.Assets.images.portfolioEmptyPositionList
                                .svg(),
                        title: localization.trader_noOpenPositions,
                        description:
                            localization.trader_noOpenPositionsDescription,
                      ),
                    ),
                    PositionErrorState() => SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.error(
                        description:
                            localization.trader_insightsErrorDescription,
                        title: localization.trader_somethingWentWrong,
                        svgImage: trader.Assets.images.bug.svg(),
                        retryButtonText: localization.trader_reload,
                        onTapRetry: () {
                          context.read<PositionBloc>().add(
                            PositionEvent.loadPositions(),
                          );
                        },
                      ),
                    ),
                  },
            ),
          ],
        ),
      ),
    );
  }
}
