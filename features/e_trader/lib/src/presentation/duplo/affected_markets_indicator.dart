import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

/// A widget that displays overlapped circles to represent affected markets.
/// Each circle can display a symbol or an abbreviation of the market.
class AffectedMarketsIndicator extends StatelessWidget {
  /// The list of market symbols to display
  final List<String> marketSymbols;

  /// The maximum number of circles to display
  final int maxCircles;

  /// The size of each circle
  final double circleSize;

  /// The overlap factor between circles (0.0 to 1.0)
  final double overlapFactor;

  /// The color of the circles
  final Color circleColor;

  /// The border color of the circles
  final Color borderColor;

  /// The border width of the circles
  final double borderWidth;

  /// The text style for the symbols
  final TextStyle textStyle;

  /// Creates an AffectedMarketsIndicatorAdvanced widget
  const AffectedMarketsIndicator({
    Key? key,
    required this.marketSymbols,
    this.maxCircles = 3,
    this.circleSize = 24.0,
    this.overlapFactor = 0.66,
    this.circleColor = Colors.blue,
    this.borderColor = Colors.white,
    this.borderWidth = 1.5,
    this.textStyle = const TextStyle(
      color: Colors.white,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    ),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    // If no market symbols, return an empty container
    if (marketSymbols.isEmpty) {
      return const SizedBox.shrink();
    }

    // Determine how many circles to display
    final displayCount =
        marketSymbols.length > maxCircles ? maxCircles : marketSymbols.length;

    // Calculate the total width based on the number of circles and overlap factor
    final totalWidth =
        circleSize +
        (circleSize * (1 - overlapFactor) * (displayCount - 1)) +
        1;

    return Row(
      children: [
        SizedBox(
          width: totalWidth,
          height: 30,
          child: Stack(
            children: List.generate(displayCount, (index) {
              // Calculate the position of each circle
              final position = index * (circleSize * (1 - overlapFactor));

              // For the last circle, check if we need to show a "+X more" indicator
              final isLastCircle = index == displayCount - 1;
              final displayValue =
                  marketSymbols.length > 99 ? 99 : marketSymbols.length;

              return Positioned(
                left: position,
                child: Container(
                  width: 25,
                  height: 25,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        isLastCircle
                            ? theme.foreground.fgTertiary
                            : theme.background.bgQuaternary,
                    border: Border.all(
                      color: theme.border.borderTertiary,
                      width: .78,
                    ),
                  ),
                  child: Center(
                    child:
                        isLastCircle
                            ? DuploText(
                              color: context.duploTheme.text.textPrimaryOnBrand,
                              text:
                                  '+${EquitiFormatter.formatNumber(value: displayValue, locale: 'en')}',
                              style: context.duploTextStyles.textXxs,
                              fontWeight: DuploFontWeight.bold,
                            )
                            : SizedBox.shrink(),
                  ),
                ),
              );
            }),
          ),
        ),
        SizedBox(width: 4),
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: trader.Assets.images.chevron.svg(
            height: 20,
            width: 20,
            colorFilter: ColorFilter.mode(
              theme.button.buttonSecondaryFg,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }
}
