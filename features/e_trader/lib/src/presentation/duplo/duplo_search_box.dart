import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theme_manager/theme_manager.dart';

class DuploSearchBox extends StatelessWidget {
  const DuploSearchBox({
    super.key,
    required this.textController,
    this.isEnabled,
    this.onTap,
    required this.title,
    required this.onTextChanged,
  });
  final TextEditingController textController;
  final bool? isEnabled;
  final String title;
  final VoidCallback? onTap;
  final void Function(String) onTextChanged;
  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onTap,
          child: Focus(
            child: Builder(
              builder: (builderContext) {
                final hasFocus = Focus.of(builderContext).hasFocus;

                return Container(
                  decoration: BoxDecoration(
                    color: theme.background.bgPrimary,
                    border: Border.all(color: theme.border.borderPrimary),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: TextField(
                    key: Key('search_text_field'),
                    onTapOutside: (_) {
                      Focus.of(builderContext).unfocus();
                    },
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                        RegExp(r'[\u0621-\u064A\u0660-\u0669]'),
                      ),
                    ],
                    keyboardAppearance:
                        diContainer<ThemeManager>().isDarkMode
                            ? Brightness.dark
                            : Brightness.light,
                    style: TextStyle(color: theme.text.textPrimary),
                    enabled: isEnabled ?? true,
                    controller: textController,
                    cursorWidth: 2,
                    cursorColor: theme.text.textPrimary,
                    textAlign: TextAlign.justify,
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      suffixIcon:
                          textController.text.isNotEmpty
                              ? Padding(
                                padding: EdgeInsets.fromLTRB(
                                  Directionality.of(context) ==
                                          TextDirection.ltr
                                      ? 14
                                      : 8,
                                  12,
                                  Directionality.of(context) ==
                                          TextDirection.ltr
                                      ? 8
                                      : 14,
                                  12,
                                ),
                                child: InkWell(
                                  onTap: () {
                                    textController.clear();
                                    onTextChanged(textController.text);
                                  },
                                  child: trader.Assets.images.xCircle.svg(
                                    height: 20,
                                    width: 20,
                                    matchTextDirection: true,
                                  ),
                                ),
                              )
                              : null,
                      isCollapsed: true,
                      border: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: theme.border.borderBrand,
                          width: 1,
                        ),
                      ),
                      hintText: hasFocus ? "" : title,
                      hintStyle: TextStyle(
                        color: theme.text.textPlaceholder,
                        fontSize: duploTextStyles.textMd.fontSize,
                      ),
                      prefixIcon: Padding(
                        padding: EdgeInsets.fromLTRB(
                          Directionality.of(context) == TextDirection.ltr
                              ? 14
                              : 8,
                          14,
                          Directionality.of(context) == TextDirection.ltr
                              ? 8
                              : 14,
                          14,
                        ),
                        child: trader.Assets.images.searchLg.svg(
                          height: 10,
                          width: 10,
                          matchTextDirection: true,
                        ),
                      ),
                    ),
                    onChanged: onTextChanged,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
