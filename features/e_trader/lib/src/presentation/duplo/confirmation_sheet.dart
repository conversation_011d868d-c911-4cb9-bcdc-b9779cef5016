import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/confirmation_trade_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';

Future<void> showConfirmationSheet({
  required BuildContext context,
  required String productIconURL,
  required String productName,
  required String assetType,
  required double lots,
  required TradeType tradeType,
  required double orderPrice,
  required int digits,
  required String title,
  VoidCallback? onNavigate,
  VoidCallback? onDone,
  String? clickableText,
  Widget? sellSvg,
  Widget? buySvg,
}) {
  final l10n = EquitiLocalization.of(context);
  return DuploSheet.showModalSheetV2(
    context,
    appBar: DuploAppBar(
      title: '',
      automaticallyImplyLeading: false,
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    ),
    settings: RouteSettings(name: 'confirmation_sheet'),
    content: _ConfirmationSheet(
      title: title,
      productIconURL: productIconURL,
      productName: productName,
      assetType: assetType,
      lots: lots,
      tradeType: tradeType,
      orderPrice: orderPrice,
      digits: digits,
      onNavigate: onNavigate,
      onDone: onDone,
      clickableText: clickableText,
      sellSvg: sellSvg,
      buySvg: buySvg,
    ),
    bottomBar: Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DuploButton.defaultPrimary(
            useFullWidth: true,
            onTap: onNavigate ?? () => diContainer<EquitiNavigatorBase>().pop(),
            title: clickableText ?? l10n.trader_portfolio,
          ),
          SizedBox(height: 12),
          DuploButton.secondary(
            useFullWidth: true,
            title: l10n.trader_done,
            onTap: onDone ?? () => Navigator.pop(context),
          ),
        ],
      ),
    ),
  );
}

class _ConfirmationSheet extends StatelessWidget {
  final String title;
  final String productIconURL;
  final String productName;
  final String assetType;
  final double lots;
  final TradeType tradeType;
  final double orderPrice;
  final int digits;
  final String? clickableText;
  final VoidCallback? onNavigate;
  final VoidCallback? onDone;
  final Widget? sellSvg;
  final Widget? buySvg;

  const _ConfirmationSheet({
    required this.title,
    required this.productIconURL,
    required this.productName,
    required this.assetType,
    required this.lots,
    required this.tradeType,
    required this.orderPrice,
    required this.digits,
    this.onNavigate,
    this.onDone,
    this.clickableText,
    this.sellSvg,
    this.buySvg,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40),
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          colors:
              tradeType == TradeType.buy
                  ? [
                    theme.utility.utilitySuccess100,
                    theme.background.bgPrimary,
                  ]
                  : [theme.utility.utilityError100, theme.background.bgPrimary],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            DuploCachedNetworkImage(
              imageUrl: productIconURL,
              imageHeight: 86,
              imageWidth: 86,
            ),
            const SizedBox(height: 12),
            DuploText(
              text: productName,
              style: context.duploTextStyles.textXl,
              color: theme.text.textPrimary,
              fontWeight: DuploFontWeight.bold,
            ),
            DuploText(
              text: assetType,
              style: context.duploTextStyles.textSm,
              color: theme.text.textTertiary,
              fontWeight: DuploFontWeight.regular,
            ),
            SizedBox(height: 24),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.images.checkCircle.svg(height: 28, width: 28),
                    const SizedBox(width: 10),
                    DuploText.rich(
                      spans: [
                        DuploTextSpan(
                          text:
                              tradeType == TradeType.buy
                                  ? l10n.trader_buy
                                  : l10n.trader_sell,
                          style: context.duploTextStyles.displayXs,
                          color: theme.text.textPrimary,
                          fontWeight: DuploFontWeight.bold,
                        ),
                        DuploTextSpan(
                          text: " ",
                          style: context.duploTextStyles.displayXs,
                          color: theme.text.textPrimary,
                          fontWeight: DuploFontWeight.bold,
                        ),
                        DuploTextSpan(
                          text: title,
                          style: context.duploTextStyles.displayXs,
                          color: theme.text.textPrimary,
                          fontWeight: DuploFontWeight.bold,
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 12),
                ConfirmationTradeTile(
                  lots: lots,
                  tradeType: tradeType,
                  orderPrice: orderPrice,
                  digits: digits,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
