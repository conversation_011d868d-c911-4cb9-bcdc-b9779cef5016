import 'package:duplo/duplo.dart';

import 'package:flutter/material.dart';

class LoadingView extends StatelessWidget {
  const LoadingView({
    super.key,
    this.title,
    this.fullScreen = false,
    this.isColored = false,
    this.shouldFit = true,
    this.maxLines = 1,
  });
  final String? title;
  final bool fullScreen;
  final bool isColored;
  final bool shouldFit;
  final int maxLines;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return ColoredBox(
      //TODO(Danya): revisit the transparent colors
      color:
          isColored
              ? theme.background.bgPrimary
              : Colors.transparent.withOpacity(fullScreen ? 0.8 : 0),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: 70, width: 70, child: CircularProgressIndicator()),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: DuploText(
                text: title ?? '',
                style: duploTextStyles.textMd,
                color:
                    fullScreen
                        ? theme.text.textPlaceholderSubtle
                        : theme.text.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
