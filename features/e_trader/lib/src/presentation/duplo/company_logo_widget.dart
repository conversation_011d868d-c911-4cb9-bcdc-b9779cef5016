import 'package:e_trader/src/assets/assets.gen.dart';
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:flutter/material.dart';

class CompanyLogoWidget extends StatelessWidget {
  const CompanyLogoWidget({
    super.key,
    required this.platformType,
    this.useMono = false,
    this.size = 34,
  });

  final PlatformType platformType;
  final double size;
  final bool useMono;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return SizedBox(
      width: size,
      height: size,
      child: switch (platformType) {
        PlatformType.mt4 =>
          isDarkMode
              ? useMono
                  ? Assets.images.mt4MonoDark.svg()
                  : Assets.images.mt4ColoredDark.svg()
              : useMono
              ? Assets.images.mt4Mono.svg()
              : Assets.images.mt4Colored.svg(),
        PlatformType.mt5 =>
          isDarkMode
              ? useMono
                  ? Assets.images.mt5MonoDark.svg()
                  : Assets.images.mt5ColoredDark.svg()
              : useMono
              ? Assets.images.mt5Mono.svg()
              : Assets.images.mt5Colored.svg(),
        PlatformType.equiti || PlatformType.dulcimer || PlatformType.unknown =>
          isDarkMode
              ? Assets.images.equitiMonoDark.svg()
              : Assets.images.equitiMono.svg(),
      },
    );
  }
}
