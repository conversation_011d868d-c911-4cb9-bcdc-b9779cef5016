// ignore_for_file: prefer-number-format

import 'dart:async';
import 'dart:collection';

import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_quotes_by_symbols_use_case.dart';
import 'package:monitoring/monitoring.dart';

/// Service that batches symbol subscription and unsubscription requests
/// to reduce the number of individual calls to UpdateQuotesBySymbolsUseCase.
///
/// This service collects requests over a short time window and sends
/// them as batched operations to improve performance when multiple
/// PriceDisplay widgets are created/destroyed simultaneously.
class SymbolSubscriptionBatchService {
  SymbolSubscriptionBatchService({
    required LoggerBase logger,
    this.batchDelay = const Duration(milliseconds: 100),
  }) : _logger = logger;

  /// Logger for debugging and monitoring
  final LoggerBase _logger;

  /// Delay before sending batched requests
  final Duration batchDelay;

  /// Set of symbols pending subscription
  final Set<String> _pendingSubscriptions = <String>{};

  /// Set of symbols pending unsubscription
  final Set<String> _pendingUnsubscriptions = <String>{};

  /// Set of symbols currently subscribed (global state tracking)
  final Set<String> _currentlySubscribed = <String>{};

  /// Timer for batching operations
  Timer? _batchTimer;

  /// Request subscription to a symbol
  /// The actual subscription will be batched and sent after the batch delay
  void requestSubscription(String symbol) {
    _logger.logDebug(
      'SymbolSubscriptionBatchService: Requesting subscription for $symbol',
    );

    // Check if already subscribed
    if (_currentlySubscribed.contains(symbol)) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: $symbol is already subscribed, ignoring duplicate request',
      );
      return;
    }

    // Remove from unsubscriptions if it was pending (they cancel out)
    if (_pendingUnsubscriptions.remove(symbol)) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: Cancelled pending unsubscription for $symbol',
      );
    }

    // Add to pending subscriptions
    final wasAdded = _pendingSubscriptions.add(symbol);
    if (wasAdded) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: Added $symbol to pending subscriptions. Total pending: ${(_pendingSubscriptions.length)}',
      );
    } else {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: $symbol already in pending subscriptions',
      );
    }

    _scheduleBatch();
  }

  /// Request unsubscription from a symbol
  /// The actual unsubscription will be batched and sent after the batch delay
  void requestUnsubscription(String symbol) {
    _logger.logDebug(
      'SymbolSubscriptionBatchService: Requesting unsubscription for $symbol',
    );

    // Check if not currently subscribed
    if (!_currentlySubscribed.contains(symbol)) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: $symbol is not subscribed, ignoring unsubscription request',
      );
      return;
    }

    // Remove from subscriptions if it was pending (they cancel out)
    if (_pendingSubscriptions.remove(symbol)) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: Cancelled pending subscription for $symbol',
      );
    }

    // Add to pending unsubscriptions
    final wasAdded = _pendingUnsubscriptions.add(symbol);
    if (wasAdded) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: Added $symbol to pending unsubscriptions. Total pending: ${(_pendingUnsubscriptions.length)}',
      );
    } else {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: $symbol already in pending unsubscriptions',
      );
    }

    _scheduleBatch();
  }

  /// Schedule a batch operation if not already scheduled
  void _scheduleBatch() {
    if (_batchTimer?.isActive == true) {
      _logger.logDebug(
        'SymbolSubscriptionBatchService: Batch timer already active, resetting timer',
      );
      _batchTimer!.cancel();
    }

    _logger.logDebug(
      'SymbolSubscriptionBatchService: Scheduling batch in ${(batchDelay.inMilliseconds)}ms',
    );
    _batchTimer = Timer(batchDelay, _processBatch);
  }

  /// Process all pending subscription and unsubscription requests
  void _processBatch() {
    _logger.logDebug(
      'SymbolSubscriptionBatchService: Processing batch - Subscriptions: ${(_pendingSubscriptions.length)}, Unsubscriptions: ${(_pendingUnsubscriptions.length)}',
    );

    try {
      // Process subscriptions
      if (_pendingSubscriptions.isNotEmpty) {
        final subscriptions = List<String>.of(_pendingSubscriptions);
        _pendingSubscriptions.clear();

        _logger.logDebug(
          'SymbolSubscriptionBatchService: Batching ${subscriptions.length} subscriptions: $subscriptions',
        );

        diContainer<UpdateQuotesBySymbolsUseCase>().call(
          subscriptions,
          TradingSocketEvent.quotes.subscribe,
        );

        // Update global state - mark these symbols as subscribed
        _currentlySubscribed.addAll(subscriptions);

        _logger.logDebug(
          'SymbolSubscriptionBatchService: Successfully sent subscription batch. Total subscribed: ${_currentlySubscribed.length}',
        );
      }

      // Process unsubscriptions
      if (_pendingUnsubscriptions.isNotEmpty) {
        final unsubscriptions = List<String>.of(_pendingUnsubscriptions);
        _pendingUnsubscriptions.clear();

        _logger.logDebug(
          'SymbolSubscriptionBatchService: Batching ${unsubscriptions.length} unsubscriptions: $unsubscriptions',
        );

        diContainer<UpdateQuotesBySymbolsUseCase>().call(
          unsubscriptions,
          TradingSocketEvent.quotes.unsubscribe,
        );

        // Update global state - remove these symbols from subscribed set
        _currentlySubscribed.removeAll(unsubscriptions);

        _logger.logDebug(
          'SymbolSubscriptionBatchService: Successfully sent unsubscription batch. Total subscribed: ${_currentlySubscribed.length}',
        );
      }

      if (_pendingSubscriptions.isEmpty && _pendingUnsubscriptions.isEmpty) {
        _logger.logDebug(
          'SymbolSubscriptionBatchService: No pending operations to process',
        );
      }
    } catch (e, stackTrace) {
      _logger.logError(e, stackTrace: stackTrace);
    }
  }

  /// Force immediate processing of all pending requests
  /// Useful for cleanup or when immediate processing is required
  void flushPendingRequests() {
    _logger.logDebug(
      'SymbolSubscriptionBatchService: Flushing pending requests immediately',
    );
    _batchTimer?.cancel();
    _processBatch();
  }

  /// Clear all pending requests without processing them
  void clearPendingRequests() {
    _logger.logDebug(
      'SymbolSubscriptionBatchService: Clearing ${_pendingSubscriptions.length} pending subscriptions and ${_pendingUnsubscriptions.length} pending unsubscriptions',
    );
    _batchTimer?.cancel();
    _pendingSubscriptions.clear();
    _pendingUnsubscriptions.clear();
  }

  /// Clear all global state (useful for testing or reset scenarios)
  void clearGlobalState() {
    _logger.logDebug(
      'SymbolSubscriptionBatchService: Clearing global state. Currently subscribed: ${_currentlySubscribed.length} symbols',
    );
    _currentlySubscribed.clear();
  }

  /// Dispose of the service and clean up resources
  void dispose() {
    _logger.logDebug('SymbolSubscriptionBatchService: Disposing service');
    _batchTimer?.cancel();
    _pendingSubscriptions.clear();
    _pendingUnsubscriptions.clear();
    _currentlySubscribed.clear();
  }

  /// Get current pending subscriptions (for debugging)
  Set<String> get pendingSubscriptions =>
      UnmodifiableSetView(_pendingSubscriptions);

  /// Get current pending unsubscriptions (for debugging)
  Set<String> get pendingUnsubscriptions =>
      UnmodifiableSetView(_pendingUnsubscriptions);

  /// Get debug information about the current state
  String get debugInfo =>
      'SymbolSubscriptionBatchService: '
      'Currently subscribed: ${_currentlySubscribed.length} ${_currentlySubscribed.toList()}, '
      'Pending subscriptions: ${_pendingSubscriptions.length} ${_pendingSubscriptions.toList()}, '
      'Pending unsubscriptions: ${_pendingUnsubscriptions.length} ${_pendingUnsubscriptions.toList()}, '
      'Timer active: ${_batchTimer?.isActive ?? false}';

  /// Get currently subscribed symbols (for debugging)
  Set<String> get currentlySubscribed =>
      UnmodifiableSetView(_currentlySubscribed);
}
