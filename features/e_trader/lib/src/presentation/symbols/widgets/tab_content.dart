import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/search_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_list_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbols_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class TabContent extends StatefulWidget {
  const TabContent({super.key});
  @override
  State<TabContent> createState() => _TabContentState();
}

class _TabContentState extends State<TabContent>
    with TickerProviderStateMixin, PerformanceObserverMixin {
  late final TabController tabController;
  late final TextEditingController textController;

  // Variables to track animation progress for 60% threshold triggering
  double? _previousAnimationValue;
  double? _lastTriggeredValue;
  bool _isSwipeTriggered = false;

  @override
  void initState() {
    super.initState();
    textController = TextEditingController();
    final categoriesState = context.read<CategoriesBloc>().state;
    final categoryLength = categoriesState.categories.length;
    tabController = TabController(
      length: categoryLength,
      vsync: this,
      animationDuration: Duration.zero,
    );
    tabController.animation!.addListener(_handleTabAnimationChange);
    tabController.addListener(_handleTabChange);
    final initialCategory = categoriesState.categories.elementAtOrNull(0);
    if (initialCategory != null) {
      context.read<SymbolsBloc>().add(
        SymbolsEvent.onCategorySelected(categoryID: initialCategory.id),
      );
    }
  }

  @override
  void dispose() {
    tabController.animation!.removeListener(_handleTabAnimationChange);
    tabController.dispose();
    textController.dispose();
    super.dispose();
  }

  void _handleTabAnimationChange() {
    final currentValue = tabController.animation!.value;

    if (_previousAnimationValue != null) {
      // Check if we're crossing the threshold
      const forwardThreshold = 0.6;
      const backwardThreshold = 0.4;

      final currentFraction = currentValue - currentValue.floor();
      final previousFraction =
          _previousAnimationValue! - _previousAnimationValue!.floor();

      // Trigger when crossing threshold in forward direction or backward direction
      final shouldTrigger =
          (currentFraction >= forwardThreshold &&
              previousFraction < forwardThreshold) ||
          (currentFraction <= backwardThreshold &&
              previousFraction > backwardThreshold);

      if (shouldTrigger && _lastTriggeredValue != currentValue.round()) {
        _lastTriggeredValue = currentValue.round().toDouble();
        _isSwipeTriggered = true;
        _handleSwipeTabChange();
      }
    }

    _previousAnimationValue = currentValue;
  }

  void _handleSwipeTabChange() {
    final categoriesState = context.read<CategoriesBloc>().state;
    final targetIndex = tabController.animation!.value.round();
    final currentCategory = categoriesState.categories.elementAtOrNull(
      targetIndex,
    );
    final previousIndex = tabController.index;
    final previousCategory = categoriesState.categories.elementAtOrNull(
      previousIndex,
    );

    if (previousCategory != null && targetIndex != previousIndex) {
      context.read<SymbolsBloc>().add(
        SymbolsEvent.onClearSymbols(previousCategory.id),
      );
    }

    if (currentCategory != null) {
      context.read<SymbolsBloc>().add(
        SymbolsEvent.onCategorySelected(categoryID: currentCategory.id),
      );
    }
  }

  void _handleTabChange() {
    if (!tabController.indexIsChanging && !_isSwipeTriggered) {
      final categoriesState = context.read<CategoriesBloc>().state;
      final currentCategory = categoriesState.categories.elementAtOrNull(
        tabController.index,
      );
      final previousCategory = categoriesState.categories.elementAtOrNull(
        tabController.previousIndex,
      );

      if (previousCategory != null) {
        context.read<SymbolsBloc>().add(
          SymbolsEvent.onClearSymbols(previousCategory.id),
        );
      }

      if (currentCategory != null) {
        context.read<SymbolsBloc>().add(
          SymbolsEvent.onCategorySelected(categoryID: currentCategory.id),
        );
      }
    }

    // Reset the swipe flag after handling
    _isSwipeTriggered = false;
  }

  @override
  Widget build(BuildContext context) {
    final categoriesState = context.watch<CategoriesBloc>().state;
    final tabTitles =
        categoriesState.categories.map((category) {
          if (category.id == 'watchlist') {
            return EquitiLocalization.of(context).trader_watchlist;
          }
          return category.name;
        }).toList();
    final priceInfoViewType = context
        .select<SymbolsBloc, SymbolPriceInfoViewType>(
          (bloc) => bloc.state.priceInfoViewType,
        );
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return BlocListener<SymbolsBloc, SymbolsState>(
      listenWhen:
          (previous, current) =>
              previous.currentState != current.currentState &&
              current.currentState is SymbolsErrorState,
      listener: (listenerContext, state) {
        if (state.currentState is SymbolsErrorState) {
          if (!Platform.environment.containsKey('FLUTTER_TEST')) {
            final toast = DuploToast();
            toast.hidesToastMessage();
            toast.showToastMessage(
              autoCloseDuration: Duration.zero,
              context: listenerContext,
              widget: DuploToastMessage(
                titleMessage:
                    EquitiLocalization.of(listenerContext).trader_loadingError,
                descriptionMessage:
                    EquitiLocalization.of(
                      listenerContext,
                    ).trader_loadingErrorDescription,
                messageType: ToastMessageType.error,
                onLeadingAction: () => toast.hidesToastMessage(),
                actionButtonTitle:
                    EquitiLocalization.of(listenerContext).trader_reload,
                onTap: () {
                  toast.hidesToastMessage();
                  final symbolsBloc = listenerContext.read<SymbolsBloc>();
                  final currentCategoryID =
                      symbolsBloc.state.selectedCategoryID;

                  // Reset pagination and reload the current category
                  symbolsBloc.add(
                    SymbolsEvent.onCategorySelected(
                      categoryID: currentCategoryID,
                    ),
                  );
                },
              ),
            );
          }
        }
      },
      child: Scaffold(
        backgroundColor: theme.background.bgSecondary,
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: DuploSearchInputField(
                key: const ValueKey('symbols_search'),
                controller: textController,
                hintText: EquitiLocalization.of(context).trader_search,
                isDisabled: true,
                onTap:
                    () => showSearchView(
                      parentContext: context,
                      textController: textController,
                      title: EquitiLocalization.of(context).trader_search,
                    ),
              ),
            ),

            GroupedButtonsWidget(
              groupedButtonsItemList: [
                GroupedButtonsItem(
                  title: EquitiLocalization.of(context).trader_sort,
                  buttonIcon: trader.Assets.images.sort.svg(
                    height: 20,
                    width: 20,
                    colorFilter: ColorFilter.mode(
                      theme.text.textPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  isSelected: false,
                  // sort doesn't need an active indicator based on requirment
                  onTap: () => SymbolsBottomSheet.showSort(context),
                ),
                GroupedButtonsItem(
                  title: EquitiLocalization.of(context).trader_view,
                  buttonIcon: trader.Assets.images.view.svg(
                    height: 20,
                    width: 20,
                    colorFilter: ColorFilter.mode(
                      theme.text.textPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  isSelected:
                      (priceInfoViewType !=
                          SymbolPriceInfoViewTypeOptions.defaultOption),
                  onTap: () => SymbolsBottomSheet.showView(context),
                ),
              ],
            ),
            TabBar(
              controller: tabController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              dividerColor: theme.border.borderSecondary,
              indicatorColor: theme.foreground.fgBrandPrimaryAlt,
              labelColor: theme.text.textBrandSecondary,
              labelStyle: TextStyle(
                fontSize: duploTextStyles.textSm.fontSize,
                fontWeight: DuploFontWeight.semiBold.value,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              unselectedLabelColor: theme.text.textQuaternary,
              splashFactory: null,
              overlayColor: WidgetStateProperty.all(Colors.transparent),
              tabAlignment: TabAlignment.start,
              unselectedLabelStyle: TextStyle(
                fontSize: duploTextStyles.textSm.fontSize,
                fontWeight: DuploFontWeight.medium.value,
              ),
              isScrollable: true,
              tabs: tabTitles.map((title) => Tab(text: title)).toList(),
            ),
            switch (categoriesState.currentState) {
              CategoriesLoadingState() => Expanded(
                child: const Center(
                  child: CircularProgressIndicator.adaptive(),
                ),
              ),
              CategoriesSuccessState() => Expanded(
                child: TabBarView(
                  controller: tabController,
                  children: List.generate(
                    categoriesState.categories.length,
                    (index) => SymbolListView(
                      key: ValueKey(
                        categoriesState.categories.elementAtOrNull(index),
                      ),
                    ),
                  ),
                ),
              ),
              _ => const SizedBox(),
            },

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    if (route.settings.name == 'search_view') {
      textController.clear();
    }

    // Refresh watchlist when returning from any screen if currently on watchlist tab
    final symbolsState = context.read<SymbolsBloc>().state;
    if (symbolsState.selectedCategoryID == 'watchlist') {
      context.read<SymbolsBloc>().add(SymbolsEvent.onGetWtachlistSymbols());
    }
  }
}
