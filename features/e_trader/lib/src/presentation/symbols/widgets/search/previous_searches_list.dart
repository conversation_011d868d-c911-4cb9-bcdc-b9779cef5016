import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:equiti_localization/equiti_localization.dart';

import 'package:flutter/material.dart';

class PreviousSearchesList extends StatelessWidget {
  const PreviousSearchesList({
    super.key,
    required this.listOfPreviousSearches,
    required this.onTap,
  });
  final List<String> listOfPreviousSearches;
  final void Function(String) onTap;
  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.duploTheme.background.bgSecondary,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: DuploText(
              text: EquitiLocalization.of(context).trader_previousSearches,
              style: context.duploTextStyles.textMd,
              color: context.duploTheme.text.textPrimary,
              fontWeight: DuploFontWeight.semiBold,
            ),
          ),
          Container(
            color: context.duploTheme.border.borderPrimary,
            height: 1,
            width: double.infinity,
          ),
          if (listOfPreviousSearches.isNotEmpty)
            Expanded(
              child: ListView.separated(
                physics: NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                separatorBuilder:
                    (ctx, index) => Container(
                      color: context.duploTheme.border.borderPrimary,
                      height: 1,
                      width: double.infinity,
                    ),
                itemCount: listOfPreviousSearches.length,
                itemBuilder: (ctx, index) {
                  return InkWell(
                    onTap: () {
                      onTap(listOfPreviousSearches[index]);
                    },
                    child: Row(
                      children: [
                        trader.Assets.images.searchGreen.svg(),
                        SizedBox(width: 10),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: DuploText(
                            text: listOfPreviousSearches[index],
                            style: context.duploTextStyles.textMd,
                            color: context.duploTheme.text.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}
