import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/services/symbol_subscription_batch_service.dart';
import 'package:e_trader/src/presentation/symbols/widgets/buy_sell_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class PriceDisplay extends StatefulWidget {
  const PriceDisplay({super.key, required this.symbol});
  final String symbol;

  @override
  State<PriceDisplay> createState() => _PriceDisplayState();
}

class _PriceDisplayState extends State<PriceDisplay>
    with PerformanceObserverMixin {
  bool _hasSubscribed = false;

  @override
  void initState() {
    super.initState();
    _subscribe();
  }

  @override
  void dispose() {
    _unsubscribe();
    super.dispose();
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      diContainer<SymbolSubscriptionBatchService>().requestSubscription(
        widget.symbol,
      );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      diContainer<SymbolSubscriptionBatchService>().requestUnsubscription(
        widget.symbol,
      );
      _hasSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return BlocBuilder<SymbolsBloc, SymbolsState>(
      buildWhen:
          (previous, current) =>
              // Rebuild when price data is updated (state becomes SymbolsPriceSuccessState)
              current.currentState is SymbolsPriceSuccessState ||
              // Rebuild when view type changes
              previous.priceInfoViewType != current.priceInfoViewType ||
              // Rebuild when quote data reference changes (initial load)
              previous.symbolsQuote[widget.symbol] !=
                  current.symbolsQuote[widget.symbol],
      builder: (builderContext, state) {
        final symbolQuote = state.symbolsQuote[widget.symbol];
        final viewType = state.priceInfoViewType;

        if (symbolQuote == null)
          return Platform.environment.containsKey('FLUTTER_TEST')
              ? Container(
                height: 100,
                width: 200,
                color: theme.background.bgBrandSecondary,
                child: Text('Golden test shimmer'),
              )
              : Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                  color: theme.utility.utilityGray200,
                ),
                width: 105,
                height: 33,
              );

        if (viewType == SymbolPriceInfoViewType.buySellPrice) {
          final duploTextStyles = context.duploTextStyles;
          return Stack(
            clipBehavior: Clip.none,
            alignment: AlignmentDirectional.center,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: BuySellContainer(
                      price: symbolQuote.getFormattedBid(
                        Localizations.localeOf(context).toString(),
                      ),
                      type: TradeType.sell,
                    ),
                  ),
                  SizedBox(width: 4),
                  Expanded(
                    child: BuySellContainer(
                      type: TradeType.buy,
                      price: symbolQuote.getFormattedAsk(
                        Localizations.localeOf(context).toString(),
                      ),
                    ),
                  ),
                ],
              ),
              Positioned(
                top: -5,
                child: Container(
                  constraints: BoxConstraints(minWidth: 46, minHeight: 21),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 11,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.background.bgPrimary,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(4),
                    ),
                  ),
                  child: DuploText(
                    text: symbolQuote.getFormattedSpread(
                      Localizations.localeOf(context).toString(),
                    ),
                    style: duploTextStyles.textXxs,
                    color: theme.text.textPrimary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    fontWeight: DuploFontWeight.medium,
                  ),
                ),
              ),
            ],
          );
        }

        return Padding(
          padding: const EdgeInsets.only(top: 12),
          child: SymbolPriceAndPercentageWidget(
            price: symbolQuote.midPrice,
            percentage: symbolQuote.dailyChange,
            digits: symbolQuote.digits,
          ),
        );
      },
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    if (route.settings.name != null &&
        (route.settings.name == '/symbolsDetails' ||
            route.settings.name == 'search_view')) {
      diContainer<SymbolSubscriptionBatchService>().requestSubscription(
        widget.symbol,
      );
      _hasSubscribed = true;
    }
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    if (route.settings.name != null &&
        (route.settings.name == '/symbolsDetails' ||
            route.settings.name == 'search_view')) {
      diContainer<SymbolSubscriptionBatchService>().requestUnsubscription(
        widget.symbol,
      );
      _hasSubscribed = false;
    }
  }
}
