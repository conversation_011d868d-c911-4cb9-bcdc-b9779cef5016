import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

class ImageWidget extends StatelessWidget {
  const ImageWidget({super.key, required this.url});

  final String? url;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    if (Platform.environment.containsKey('FLUTTER_TEST'))
      return CircleAvatar(
        radius: 16,
        backgroundColor: theme.text.textErrorPrimary,
      );
    else
      return CachedNetworkImage(
        width: 40,
        height: 40,
        imageUrl: url ?? "",
        errorWidget:
            (ctx, errorURL, error) => const Icon(Icons.broken_image, size: 32),
      );
  }
}
