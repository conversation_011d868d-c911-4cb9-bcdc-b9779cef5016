import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class SymbolsMidPriceBanner extends StatelessWidget {
  const SymbolsMidPriceBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Container(
      color: theme.background.bgSecondary,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DuploText(
                  text: EquitiLocalization.of(context).trader_market,
                  style: duploTextStyles.textXs,
                  color: theme.text.textTertiary,
                  fontWeight: DuploFontWeight.medium,
                ),
                Row(
                  children: [
                    DuploText(
                      text:
                          EquitiLocalization.of(
                            context,
                          ).trader_midPrice1DayChange,
                      style: duploTextStyles.textXs,
                      color: theme.text.textTertiary,
                      fontWeight: DuploFontWeight.medium,
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            height: 1,
            color: theme.border.borderSecondary,
          ),
        ],
      ),
    );
  }
}
