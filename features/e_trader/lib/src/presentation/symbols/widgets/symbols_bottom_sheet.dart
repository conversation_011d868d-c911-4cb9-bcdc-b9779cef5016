import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SymbolsBottomSheet {
  static void showSort(BuildContext context) {
    final loc = EquitiLocalization.of(context);

    final existing = context.read<SymbolsBloc>().state.sortOrder;
    final allSortOptions = <SelectionOptionModel>[];

    SelectionOptionModel selected = SelectionOptionModel(
      displayText: SortOrderOptions.defaultOption.display(loc),
      identifier: SortOrderOptions.defaultOption.indentifier(),
    );
    SortOrder.values.forEach((element) {
      final model = SelectionOptionModel(
        displayText: element.display(loc),
        identifier: element.indentifier(),
      );
      if (model.identifier == existing.indentifier()) {
        selected = model;
      }
      allSortOptions.add(model);
    });

    final textSelection = TextSelectionComponentScreen(
      buttonTitle: loc.trader_sortMarkets,
      options: allSortOptions,
      pageTitle: loc.trader_sortListBy,
      selected: selected,
      onSelection: (selectedOption) {
        final order = selectedOption.identifier.toSortOrder();
        if (order != existing) {
          context.read<SymbolsBloc>().add(
            SymbolsEvent.onSortSymbols(sortOrder: order),
          );
        }

        Navigator.pop(context);
      },
    );

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: loc.trader_sortMarketsby,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      content: textSelection,
    );
  }

  static void showView(BuildContext context) {
    final existing = context.read<SymbolsBloc>().state.priceInfoViewType;

    final allViewOptions = <WidgetSelectionModel>[];

    WidgetSelectionModel selected = WidgetSelectionModel(
      displayWidget: SymbolPriceInfoViewTypeOptions.defaultOption.display(),
      identifier: SymbolPriceInfoViewTypeOptions.defaultOption.indentifier(),
    );
    SymbolPriceInfoViewType.values.forEach((element) {
      final model = WidgetSelectionModel(
        displayWidget: element.display(),
        identifier: element.indentifier(),
      );
      if (model.identifier == existing.indentifier()) {
        selected = model;
      }
      allViewOptions.add(model);
    });

    final textSelection = WidgetSelectionScreen(
      buttonTitle: EquitiLocalization.of(context).trader_apply,
      options: allViewOptions,
      pageTitle: EquitiLocalization.of(context).trader_selectView,
      selected: selected,
      topSpacer: 16,
      onSelection: (selectedOption) {
        final newSelection =
            selectedOption.identifier.toSymbolPriceInfoViewType();
        if (newSelection != existing) {
          context.read<SymbolsBloc>().add(
            SymbolsEvent.onPriceViewChange(viewType: newSelection),
          );
        }

        Navigator.pop(context);
      },
    );

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        title: EquitiLocalization.of(context).trader_selectView,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      content: textSelection,
    );
  }
}
