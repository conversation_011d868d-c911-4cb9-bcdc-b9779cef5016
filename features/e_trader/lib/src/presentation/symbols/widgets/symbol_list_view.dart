import 'dart:developer';
import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_list_item.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SymbolListView extends StatefulWidget {
  const SymbolListView({
    super.key,
    this.emptyBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.query,
    this.isSearchView = false,
  });
  final WidgetBuilder? emptyBuilder;
  final WidgetBuilder? errorBuilder;
  final WidgetBuilder? loadingBuilder;
  final String? query;
  final bool isSearchView;

  @override
  State<SymbolListView> createState() => _SymbolListViewState();
}

class _SymbolListViewState extends State<SymbolListView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    log("print ------ rebuilding list");
    super.build(context);
    return CustomScrollView(
      slivers: [
        BlocConsumer<SymbolsBloc, SymbolsState>(
          listenWhen:
              (previous, current) =>
                  previous.currentState != current.currentState ||
                  (current.currentState is SymbolsErrorState),
          listener: (listenerContext, state) {
            if (state.currentState is SymbolsErrorState) {
              if (!Platform.environment.containsKey('FLUTTER_TEST')) {
                final toast = DuploToast();
                toast.hidesToastMessage();
                toast.showToastMessage(
                  autoCloseDuration: Duration.zero,
                  context: listenerContext,
                  widget: DuploToastMessage(
                    titleMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingError,
                    descriptionMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingErrorDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                    actionButtonTitle:
                        EquitiLocalization.of(listenerContext).trader_reload,
                    onTap: () {
                      toast.hidesToastMessage();
                      listenerContext.read<SymbolsBloc>().add(
                        SymbolsEvent.onGetSymbols(),
                      );
                    },
                  ),
                );
              }
            }
          },
          buildWhen: (previous, current) {
            // Rebuild for structural changes but not pure price updates
            // Allow rebuilds for pagination and initial data loading

            // Only prevent rebuilds when it's a pure price update:
            // - Same number of symbols (no pagination)
            // - Same category
            // - Same view type
            // - Transition to SymbolsPriceSuccessState
            if (previous.currentState is! SymbolsPriceSuccessState &&
                current.currentState is SymbolsPriceSuccessState &&
                previous.selectedCategoryID == current.selectedCategoryID &&
                previous.symbolsDetail.length == current.symbolsDetail.length &&
                previous.priceInfoViewType == current.priceInfoViewType) {
              // This is a pure price update, don't rebuild
              return false;
            }

            return previous.selectedCategoryID != current.selectedCategoryID ||
                previous.symbolsDetail.length != current.symbolsDetail.length ||
                previous.currentState.runtimeType !=
                    current.currentState.runtimeType ||
                previous.priceInfoViewType != current.priceInfoViewType;
          },
          builder: (builderContext, state) {
            final isSuccess = switch (state.currentState) {
              SymbolsSuccessState() || SymbolsPriceSuccessState() => true,
              SymbolschangeTabState() => state.symbolsDetail.isNotEmpty,
              _ => false,
            };
            final isWatchList = state.selectedCategoryID == 'watchlist';
            final symbolsDetail =
                isSuccess
                    ? state.symbolsDetail
                    : <String, SymbolDetailViewModel>{};
            if (state.currentState is SymbolsLoadingState) {
              return SliverToBoxAdapter(
                child: Center(child: CircularProgressIndicator.adaptive()),
              );
            }
            return PagedView.sliver(
              physics:
                  (isSuccess && symbolsDetail.isEmpty) ||
                          state.currentState == SymbolsProcessState.error()
                      ? const NeverScrollableScrollPhysics()
                      : const ClampingScrollPhysics(),
              padding: EdgeInsets.zero,
              itemCount: isSuccess ? state.symbolsDetail.length : 0,
              centerError: true,
              centerLoading: true,
              centerEmpty: true,
              hasError: switch (state.currentState) {
                SymbolsErrorState() => true,
                _ => false,
              },
              isLoading: switch (state.currentState) {
                SymbolsLoadingState() => true,
                SymbolschangeTabState() => state.symbolsDetail.isEmpty,
                _ => false,
              },
              hasReachedMax: state.hasReachedMax,
              emptyBuilder:
                  isSuccess && symbolsDetail.isEmpty
                      ? (isWatchList
                          ? (ctx) => EmptyOrErrorSymbolsView(
                            message:
                                EquitiLocalization.of(
                                  context,
                                ).trader_emptyWatchlistDescription,
                            title:
                                EquitiLocalization.of(
                                  context,
                                ).trader_emptyWatchlist,
                            image: trader.Assets.images.emptyWatchlist.svg(),
                          )
                          : (widget.emptyBuilder ??
                              (ctx) => EmptyOrErrorSymbolsView(
                                message:
                                    EquitiLocalization.of(
                                      context,
                                    ).trader_noSymbolsFound,
                                title:
                                    EquitiLocalization.of(
                                      context,
                                    ).trader_noMarkets,
                                image: trader.Assets.images.emptySearch.svg(),
                              )))
                      : null,
              loadingBuilder:
                  widget.loadingBuilder ??
                  (ctx) =>
                      DuploShimmerList(hasLeading: true, hasTrailing: true),
              errorBuilder:
                  widget.errorBuilder ??
                  (ctx) => EmptyOrErrorSymbolsView(
                    message:
                        EquitiLocalization.of(context).trader_marketsLoadFailed,
                    title:
                        EquitiLocalization.of(
                          context,
                        ).trader_somethingWentWrong,
                    image: trader.Assets.images.searchError.svg(),
                  ),
              separatorBuilder:
                  (ctx, index) => Divider(
                    color: context.duploTheme.border.borderSecondary,
                    height: 1,
                  ),
              onFetchData: () {
                if (context.mounted)
                  context.read<SymbolsBloc>().add(
                    SymbolsEvent.onGetSymbols(query: widget.query),
                  );
              },
              itemBuilder: (ctx, index) {
                final symbolDetailMap = symbolsDetail.entries.elementAtOrNull(
                  index,
                );
                return SymbolListItem(
                  key: ValueKey(symbolDetailMap?.key),
                  symbol: symbolDetailMap!.value,
                  isSearchView: widget.isSearchView,
                  onTap:
                      () => context.read<SymbolsBloc>().add(
                        SymbolsEvent.gotoDetails(
                          symbolDetail: symbolDetailMap.value,
                        ),
                      ),
                );
              },
            );
          },
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
