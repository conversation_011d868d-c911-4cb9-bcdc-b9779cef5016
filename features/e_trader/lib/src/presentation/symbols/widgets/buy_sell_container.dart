import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class BuySellContainer extends StatelessWidget {
  final String price;
  final TradeType type;
  final int? highlightLength;
  final bool isPriceAText;

  const BuySellContainer({
    super.key,
    required this.price,
    required this.type,
    this.highlightLength,
    this.isPriceAText = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final priceColor =
        type == TradeType.buy
            ? theme.text.textSuccessPrimary
            : theme.text.textErrorPrimary;

    // Build price spans using similar logic to ParsedPriceText
    List<DuploTextSpan> priceSpans = [];
    if (isPriceAText) {
      priceSpans.add(
        DuploTextSpan(
          text: price,
          style: context.duploTextStyles.textSm,
          color: priceColor,
          fontWeight: DuploFontWeight.bold,
        ),
      );
    } else {
      // Count digits only (excluding decimal point)
      final digitsOnly = price.replaceAll('.', '');
      final totalDigits = digitsOnly.length;

      // Determine split point: last 3 digits should be enlarged
      final enlargedDigitCount = totalDigits > 3 ? 3 : totalDigits;
      final normalDigitCount = totalDigits - enlargedDigitCount;

      // Build spans by iterating through price string
      final buffer = StringBuffer();
      bool isCurrentlyEnlarged = false;
      int digitsSeen = 0;

      for (final char in price.split('')) {
        if (char == '.') {
          buffer.write(char);
        } else {
          // This is a digit
          final shouldBeEnlarged = digitsSeen >= normalDigitCount;

          // If style changes, flush current buffer and start new span
          if (shouldBeEnlarged != isCurrentlyEnlarged && buffer.isNotEmpty) {
            priceSpans.add(
              _createPriceSpan(
                buffer.toString(),
                isCurrentlyEnlarged,
                context,
                priceColor,
              ),
            );
            buffer.clear();
          }

          isCurrentlyEnlarged = shouldBeEnlarged;
          buffer.write(char);
          digitsSeen++;
        }
      }

      // Add final span
      if (buffer.isNotEmpty) {
        priceSpans.add(
          _createPriceSpan(
            buffer.toString(),
            isCurrentlyEnlarged,
            context,
            priceColor,
          ),
        );
      }
    }
    return Container(
      padding: EdgeInsetsDirectional.symmetric(horizontal: 6, vertical: 2),
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        color:
            type == TradeType.buy
                ? theme.utility.utilitySuccess100
                : theme.utility.utilityError100,
        borderRadius: BorderRadiusDirectional.only(
          topStart:
              type == TradeType.buy ? Radius.circular(0) : Radius.circular(8),
          bottomStart:
              type == TradeType.buy ? Radius.circular(0) : Radius.circular(8),
          topEnd:
              type == TradeType.buy ? Radius.circular(8) : Radius.circular(0),
          bottomEnd:
              type == TradeType.buy ? Radius.circular(8) : Radius.circular(0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment:
            type == TradeType.buy
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 6),
          DuploText(
            text:
                type == TradeType.buy
                    ? EquitiLocalization.of(context).trader_buy
                    : EquitiLocalization.of(context).trader_sell,
            style: context.duploTextStyles.textXs,
            color: priceColor,
            fontWeight: DuploFontWeight.semiBold,
          ),
          DuploText.rich(
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            spans: priceSpans,
          ),
          const SizedBox(height: 6),
        ],
      ),
    );
  }

  DuploTextSpan _createPriceSpan(
    String text,
    bool isEnlarged,
    BuildContext context,
    Color color,
  ) {
    return DuploTextSpan(
      text: text,
      style:
          isEnlarged
              ? context.duploTextStyles.textSm
              : context.duploTextStyles.textXs,
      color: color,
      fontWeight: isEnlarged ? DuploFontWeight.bold : DuploFontWeight.semiBold,
    );
  }
}
