import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_content.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SymbolsScreen extends StatelessWidget {
  const SymbolsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (ctx) =>
                  diContainer<CategoriesBloc>()
                    ..add(CategoriesEvent.onGetCategories()),
        ),
        BlocProvider(create: (ctx) => diContainer<SymbolsBloc>()),
      ],
      child: BlocConsumer<CategoriesBloc, CategoriesState>(
        buildWhen: (previous, current) => previous != current,
        listenWhen:
            (previous, current) =>
                previous.currentState != current.currentState,
        listener: (listenerContext, state) {
          if (state.currentState is CategoriesErrorState) {
            if (!Platform.environment.containsKey('FLUTTER_TEST')) {
              final toast = DuploToast();
              toast.hidesToastMessage();
              toast.showToastMessage(
                autoCloseDuration: Duration.zero,
                context: listenerContext,
                widget: DuploToastMessage(
                  titleMessage:
                      EquitiLocalization.of(
                        listenerContext,
                      ).trader_loadingError,
                  descriptionMessage:
                      EquitiLocalization.of(
                        listenerContext,
                      ).trader_loadingErrorDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () {
                    toast.hidesToastMessage();
                  },
                  actionButtonTitle:
                      EquitiLocalization.of(listenerContext).trader_reload,
                  onTap: () {
                    listenerContext.read<CategoriesBloc>().add(
                      CategoriesEvent.onGetCategories(),
                    );
                    toast.hidesToastMessage();
                  },
                ),
              );
            }
          }
        },
        builder:
            (ctx, categoriesState) => switch (categoriesState.currentState) {
              CategoriesLoadingState() => CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Center(child: CircularProgressIndicator.adaptive()),
                  ),
                ],
              ),
              CategoriesSuccessState() => TabContent(),
              CategoriesErrorState() => CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: EmptyOrErrorSymbolsView(
                      message:
                          EquitiLocalization.of(
                            context,
                          ).trader_marketsLoadFailed,
                      title:
                          EquitiLocalization.of(
                            context,
                          ).trader_somethingWentWrong,
                      image: trader.Assets.images.searchError.svg(),
                    ),
                  ),
                ],
              ),
              CategoriesEmptyState() => CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Center(
                      child: DuploText(
                        text:
                            EquitiLocalization.of(context).trader_nothingToShow,
                        style: duploTextStyles.textSm,
                        color: theme.text.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            },
      ),
    );
  }
}
