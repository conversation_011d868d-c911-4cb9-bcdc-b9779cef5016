// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symbols_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SymbolsEvent implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent()';
}


}

/// @nodoc
class $SymbolsEventCopyWith<$Res>  {
$SymbolsEventCopyWith(SymbolsEvent _, $Res Function(SymbolsEvent) __);
}


/// @nodoc


class _OnGetSymbols with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnGetSymbols({this.query});
  

 final  String? query;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGetSymbolsCopyWith<_OnGetSymbols> get copyWith => __$OnGetSymbolsCopyWithImpl<_OnGetSymbols>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onGetSymbols'))
    ..add(DiagnosticsProperty('query', query));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGetSymbols&&(identical(other.query, query) || other.query == query));
}


@override
int get hashCode => Object.hash(runtimeType,query);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onGetSymbols(query: $query)';
}


}

/// @nodoc
abstract mixin class _$OnGetSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnGetSymbolsCopyWith(_OnGetSymbols value, $Res Function(_OnGetSymbols) _then) = __$OnGetSymbolsCopyWithImpl;
@useResult
$Res call({
 String? query
});




}
/// @nodoc
class __$OnGetSymbolsCopyWithImpl<$Res>
    implements _$OnGetSymbolsCopyWith<$Res> {
  __$OnGetSymbolsCopyWithImpl(this._self, this._then);

  final _OnGetSymbols _self;
  final $Res Function(_OnGetSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = freezed,}) {
  return _then(_OnGetSymbols(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _OnGetWtachlistSymbols with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnGetWtachlistSymbols({this.query});
  

 final  String? query;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGetWtachlistSymbolsCopyWith<_OnGetWtachlistSymbols> get copyWith => __$OnGetWtachlistSymbolsCopyWithImpl<_OnGetWtachlistSymbols>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onGetWtachlistSymbols'))
    ..add(DiagnosticsProperty('query', query));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGetWtachlistSymbols&&(identical(other.query, query) || other.query == query));
}


@override
int get hashCode => Object.hash(runtimeType,query);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onGetWtachlistSymbols(query: $query)';
}


}

/// @nodoc
abstract mixin class _$OnGetWtachlistSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnGetWtachlistSymbolsCopyWith(_OnGetWtachlistSymbols value, $Res Function(_OnGetWtachlistSymbols) _then) = __$OnGetWtachlistSymbolsCopyWithImpl;
@useResult
$Res call({
 String? query
});




}
/// @nodoc
class __$OnGetWtachlistSymbolsCopyWithImpl<$Res>
    implements _$OnGetWtachlistSymbolsCopyWith<$Res> {
  __$OnGetWtachlistSymbolsCopyWithImpl(this._self, this._then);

  final _OnGetWtachlistSymbols _self;
  final $Res Function(_OnGetWtachlistSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = freezed,}) {
  return _then(_OnGetWtachlistSymbols(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _ProcessSymbolPriceResult with DiagnosticableTreeMixin implements SymbolsEvent {
  const _ProcessSymbolPriceResult({required this.result});
  

 final  SymbolQuoteModel result;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessSymbolPriceResultCopyWith<_ProcessSymbolPriceResult> get copyWith => __$ProcessSymbolPriceResultCopyWithImpl<_ProcessSymbolPriceResult>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.processSymbolPriceResult'))
    ..add(DiagnosticsProperty('result', result));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessSymbolPriceResult&&(identical(other.result, result) || other.result == result));
}


@override
int get hashCode => Object.hash(runtimeType,result);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.processSymbolPriceResult(result: $result)';
}


}

/// @nodoc
abstract mixin class _$ProcessSymbolPriceResultCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$ProcessSymbolPriceResultCopyWith(_ProcessSymbolPriceResult value, $Res Function(_ProcessSymbolPriceResult) _then) = __$ProcessSymbolPriceResultCopyWithImpl;
@useResult
$Res call({
 SymbolQuoteModel result
});


$SymbolQuoteModelCopyWith<$Res> get result;

}
/// @nodoc
class __$ProcessSymbolPriceResultCopyWithImpl<$Res>
    implements _$ProcessSymbolPriceResultCopyWith<$Res> {
  __$ProcessSymbolPriceResultCopyWithImpl(this._self, this._then);

  final _ProcessSymbolPriceResult _self;
  final $Res Function(_ProcessSymbolPriceResult) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? result = null,}) {
  return _then(_ProcessSymbolPriceResult(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,
  ));
}

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get result {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

/// @nodoc


class _OnSortSymbols with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnSortSymbols({this.sortOrder = SortOrderOptions.defaultOption});
  

@JsonKey() final  SortOrder sortOrder;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnSortSymbolsCopyWith<_OnSortSymbols> get copyWith => __$OnSortSymbolsCopyWithImpl<_OnSortSymbols>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onSortSymbols'))
    ..add(DiagnosticsProperty('sortOrder', sortOrder));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnSortSymbols&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder));
}


@override
int get hashCode => Object.hash(runtimeType,sortOrder);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onSortSymbols(sortOrder: $sortOrder)';
}


}

/// @nodoc
abstract mixin class _$OnSortSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnSortSymbolsCopyWith(_OnSortSymbols value, $Res Function(_OnSortSymbols) _then) = __$OnSortSymbolsCopyWithImpl;
@useResult
$Res call({
 SortOrder sortOrder
});




}
/// @nodoc
class __$OnSortSymbolsCopyWithImpl<$Res>
    implements _$OnSortSymbolsCopyWith<$Res> {
  __$OnSortSymbolsCopyWithImpl(this._self, this._then);

  final _OnSortSymbols _self;
  final $Res Function(_OnSortSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sortOrder = null,}) {
  return _then(_OnSortSymbols(
sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,
  ));
}


}

/// @nodoc


class _OnPriceViewChange with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnPriceViewChange({this.viewType = SymbolPriceInfoViewTypeOptions.defaultOption});
  

@JsonKey() final  SymbolPriceInfoViewType viewType;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnPriceViewChangeCopyWith<_OnPriceViewChange> get copyWith => __$OnPriceViewChangeCopyWithImpl<_OnPriceViewChange>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onPriceViewChange'))
    ..add(DiagnosticsProperty('viewType', viewType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnPriceViewChange&&(identical(other.viewType, viewType) || other.viewType == viewType));
}


@override
int get hashCode => Object.hash(runtimeType,viewType);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onPriceViewChange(viewType: $viewType)';
}


}

/// @nodoc
abstract mixin class _$OnPriceViewChangeCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnPriceViewChangeCopyWith(_OnPriceViewChange value, $Res Function(_OnPriceViewChange) _then) = __$OnPriceViewChangeCopyWithImpl;
@useResult
$Res call({
 SymbolPriceInfoViewType viewType
});




}
/// @nodoc
class __$OnPriceViewChangeCopyWithImpl<$Res>
    implements _$OnPriceViewChangeCopyWith<$Res> {
  __$OnPriceViewChangeCopyWithImpl(this._self, this._then);

  final _OnPriceViewChange _self;
  final $Res Function(_OnPriceViewChange) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? viewType = null,}) {
  return _then(_OnPriceViewChange(
viewType: null == viewType ? _self.viewType : viewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,
  ));
}


}

/// @nodoc


class _SubscribeToSymbol with DiagnosticableTreeMixin implements SymbolsEvent {
  const _SubscribeToSymbol(final  List<String> symbols): _symbols = symbols;
  

 final  List<String> _symbols;
 List<String> get symbols {
  if (_symbols is EqualUnmodifiableListView) return _symbols;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_symbols);
}


/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscribeToSymbolCopyWith<_SubscribeToSymbol> get copyWith => __$SubscribeToSymbolCopyWithImpl<_SubscribeToSymbol>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.subscribeToSymbol'))
    ..add(DiagnosticsProperty('symbols', symbols));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscribeToSymbol&&const DeepCollectionEquality().equals(other._symbols, _symbols));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_symbols));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.subscribeToSymbol(symbols: $symbols)';
}


}

/// @nodoc
abstract mixin class _$SubscribeToSymbolCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$SubscribeToSymbolCopyWith(_SubscribeToSymbol value, $Res Function(_SubscribeToSymbol) _then) = __$SubscribeToSymbolCopyWithImpl;
@useResult
$Res call({
 List<String> symbols
});




}
/// @nodoc
class __$SubscribeToSymbolCopyWithImpl<$Res>
    implements _$SubscribeToSymbolCopyWith<$Res> {
  __$SubscribeToSymbolCopyWithImpl(this._self, this._then);

  final _SubscribeToSymbol _self;
  final $Res Function(_SubscribeToSymbol) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbols = null,}) {
  return _then(_SubscribeToSymbol(
null == symbols ? _self._symbols : symbols // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

/// @nodoc


class _UnsubscribeToSymbol with DiagnosticableTreeMixin implements SymbolsEvent {
  const _UnsubscribeToSymbol(final  List<String> symbols): _symbols = symbols;
  

 final  List<String> _symbols;
 List<String> get symbols {
  if (_symbols is EqualUnmodifiableListView) return _symbols;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_symbols);
}


/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UnsubscribeToSymbolCopyWith<_UnsubscribeToSymbol> get copyWith => __$UnsubscribeToSymbolCopyWithImpl<_UnsubscribeToSymbol>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.unsubscribeToSymbol'))
    ..add(DiagnosticsProperty('symbols', symbols));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UnsubscribeToSymbol&&const DeepCollectionEquality().equals(other._symbols, _symbols));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_symbols));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.unsubscribeToSymbol(symbols: $symbols)';
}


}

/// @nodoc
abstract mixin class _$UnsubscribeToSymbolCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$UnsubscribeToSymbolCopyWith(_UnsubscribeToSymbol value, $Res Function(_UnsubscribeToSymbol) _then) = __$UnsubscribeToSymbolCopyWithImpl;
@useResult
$Res call({
 List<String> symbols
});




}
/// @nodoc
class __$UnsubscribeToSymbolCopyWithImpl<$Res>
    implements _$UnsubscribeToSymbolCopyWith<$Res> {
  __$UnsubscribeToSymbolCopyWithImpl(this._self, this._then);

  final _UnsubscribeToSymbol _self;
  final $Res Function(_UnsubscribeToSymbol) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbols = null,}) {
  return _then(_UnsubscribeToSymbol(
null == symbols ? _self._symbols : symbols // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

/// @nodoc


class _OnClearSymbols with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnClearSymbols(this.categoryId);
  

 final  String categoryId;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnClearSymbolsCopyWith<_OnClearSymbols> get copyWith => __$OnClearSymbolsCopyWithImpl<_OnClearSymbols>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onClearSymbols'))
    ..add(DiagnosticsProperty('categoryId', categoryId));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnClearSymbols&&(identical(other.categoryId, categoryId) || other.categoryId == categoryId));
}


@override
int get hashCode => Object.hash(runtimeType,categoryId);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onClearSymbols(categoryId: $categoryId)';
}


}

/// @nodoc
abstract mixin class _$OnClearSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnClearSymbolsCopyWith(_OnClearSymbols value, $Res Function(_OnClearSymbols) _then) = __$OnClearSymbolsCopyWithImpl;
@useResult
$Res call({
 String categoryId
});




}
/// @nodoc
class __$OnClearSymbolsCopyWithImpl<$Res>
    implements _$OnClearSymbolsCopyWith<$Res> {
  __$OnClearSymbolsCopyWithImpl(this._self, this._then);

  final _OnClearSymbols _self;
  final $Res Function(_OnClearSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? categoryId = null,}) {
  return _then(_OnClearSymbols(
null == categoryId ? _self.categoryId : categoryId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnSearchSymbols with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnSearchSymbols(this.query);
  

 final  String query;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnSearchSymbolsCopyWith<_OnSearchSymbols> get copyWith => __$OnSearchSymbolsCopyWithImpl<_OnSearchSymbols>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onSearchSymbols'))
    ..add(DiagnosticsProperty('query', query));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnSearchSymbols&&(identical(other.query, query) || other.query == query));
}


@override
int get hashCode => Object.hash(runtimeType,query);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onSearchSymbols(query: $query)';
}


}

/// @nodoc
abstract mixin class _$OnSearchSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnSearchSymbolsCopyWith(_OnSearchSymbols value, $Res Function(_OnSearchSymbols) _then) = __$OnSearchSymbolsCopyWithImpl;
@useResult
$Res call({
 String query
});




}
/// @nodoc
class __$OnSearchSymbolsCopyWithImpl<$Res>
    implements _$OnSearchSymbolsCopyWith<$Res> {
  __$OnSearchSymbolsCopyWithImpl(this._self, this._then);

  final _OnSearchSymbols _self;
  final $Res Function(_OnSearchSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = null,}) {
  return _then(_OnSearchSymbols(
null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnCategorySelected with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnCategorySelected({required this.categoryID});
  

 final  String categoryID;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnCategorySelectedCopyWith<_OnCategorySelected> get copyWith => __$OnCategorySelectedCopyWithImpl<_OnCategorySelected>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.onCategorySelected'))
    ..add(DiagnosticsProperty('categoryID', categoryID));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnCategorySelected&&(identical(other.categoryID, categoryID) || other.categoryID == categoryID));
}


@override
int get hashCode => Object.hash(runtimeType,categoryID);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.onCategorySelected(categoryID: $categoryID)';
}


}

/// @nodoc
abstract mixin class _$OnCategorySelectedCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnCategorySelectedCopyWith(_OnCategorySelected value, $Res Function(_OnCategorySelected) _then) = __$OnCategorySelectedCopyWithImpl;
@useResult
$Res call({
 String categoryID
});




}
/// @nodoc
class __$OnCategorySelectedCopyWithImpl<$Res>
    implements _$OnCategorySelectedCopyWith<$Res> {
  __$OnCategorySelectedCopyWithImpl(this._self, this._then);

  final _OnCategorySelected _self;
  final $Res Function(_OnCategorySelected) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? categoryID = null,}) {
  return _then(_OnCategorySelected(
categoryID: null == categoryID ? _self.categoryID : categoryID // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnGoToDetails with DiagnosticableTreeMixin implements SymbolsEvent {
  const _OnGoToDetails({required this.symbolDetail});
  

 final  SymbolDetailViewModel symbolDetail;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGoToDetailsCopyWith<_OnGoToDetails> get copyWith => __$OnGoToDetailsCopyWithImpl<_OnGoToDetails>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.gotoDetails'))
    ..add(DiagnosticsProperty('symbolDetail', symbolDetail));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGoToDetails&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail));
}


@override
int get hashCode => Object.hash(runtimeType,symbolDetail);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.gotoDetails(symbolDetail: $symbolDetail)';
}


}

/// @nodoc
abstract mixin class _$OnGoToDetailsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnGoToDetailsCopyWith(_OnGoToDetails value, $Res Function(_OnGoToDetails) _then) = __$OnGoToDetailsCopyWithImpl;
@useResult
$Res call({
 SymbolDetailViewModel symbolDetail
});


$SymbolDetailViewModelCopyWith<$Res> get symbolDetail;

}
/// @nodoc
class __$OnGoToDetailsCopyWithImpl<$Res>
    implements _$OnGoToDetailsCopyWith<$Res> {
  __$OnGoToDetailsCopyWithImpl(this._self, this._then);

  final _OnGoToDetails _self;
  final $Res Function(_OnGoToDetails) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolDetail = null,}) {
  return _then(_OnGoToDetails(
symbolDetail: null == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel,
  ));
}

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res> get symbolDetail {
  
  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}

/// @nodoc


class _ManageSubscription with DiagnosticableTreeMixin implements SymbolsEvent {
  const _ManageSubscription({required this.status});
  

 final  SubscriptionStatus status;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ManageSubscriptionCopyWith<_ManageSubscription> get copyWith => __$ManageSubscriptionCopyWithImpl<_ManageSubscription>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsEvent.manageSubscription'))
    ..add(DiagnosticsProperty('status', status));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ManageSubscription&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,status);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsEvent.manageSubscription(status: $status)';
}


}

/// @nodoc
abstract mixin class _$ManageSubscriptionCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$ManageSubscriptionCopyWith(_ManageSubscription value, $Res Function(_ManageSubscription) _then) = __$ManageSubscriptionCopyWithImpl;
@useResult
$Res call({
 SubscriptionStatus status
});




}
/// @nodoc
class __$ManageSubscriptionCopyWithImpl<$Res>
    implements _$ManageSubscriptionCopyWith<$Res> {
  __$ManageSubscriptionCopyWithImpl(this._self, this._then);

  final _ManageSubscription _self;
  final $Res Function(_ManageSubscription) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? status = null,}) {
  return _then(_ManageSubscription(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SubscriptionStatus,
  ));
}


}

/// @nodoc
mixin _$SymbolsState implements DiagnosticableTreeMixin {

 Map<String, Map<String, SymbolDetailViewModel>> get symbolDetailViewModel; Map<String, Map<String, SymbolQuoteViewModel>> get symbolQuoteViewModel; SortOrder get sortOrder; set sortOrder(SortOrder value); SymbolPriceInfoViewType get priceInfoViewType; set priceInfoViewType(SymbolPriceInfoViewType value); String get selectedCategoryID; set selectedCategoryID(String value); int get currentPage; set currentPage(int value); bool get hasReachedMax; set hasReachedMax(bool value); int get symbolsCount; set symbolsCount(int value); List<String> get previousSearches; set previousSearches(List<String> value); SymbolsProcessState get currentState; set currentState(SymbolsProcessState value);
/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SymbolsStateCopyWith<SymbolsState> get copyWith => _$SymbolsStateCopyWithImpl<SymbolsState>(this as SymbolsState, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsState'))
    ..add(DiagnosticsProperty('symbolDetailViewModel', symbolDetailViewModel))..add(DiagnosticsProperty('symbolQuoteViewModel', symbolQuoteViewModel))..add(DiagnosticsProperty('sortOrder', sortOrder))..add(DiagnosticsProperty('priceInfoViewType', priceInfoViewType))..add(DiagnosticsProperty('selectedCategoryID', selectedCategoryID))..add(DiagnosticsProperty('currentPage', currentPage))..add(DiagnosticsProperty('hasReachedMax', hasReachedMax))..add(DiagnosticsProperty('symbolsCount', symbolsCount))..add(DiagnosticsProperty('previousSearches', previousSearches))..add(DiagnosticsProperty('currentState', currentState));
}



@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsState(symbolDetailViewModel: $symbolDetailViewModel, symbolQuoteViewModel: $symbolQuoteViewModel, sortOrder: $sortOrder, priceInfoViewType: $priceInfoViewType, selectedCategoryID: $selectedCategoryID, currentPage: $currentPage, hasReachedMax: $hasReachedMax, symbolsCount: $symbolsCount, previousSearches: $previousSearches, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $SymbolsStateCopyWith<$Res>  {
  factory $SymbolsStateCopyWith(SymbolsState value, $Res Function(SymbolsState) _then) = _$SymbolsStateCopyWithImpl;
@useResult
$Res call({
 Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel, Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel, SortOrder sortOrder, SymbolPriceInfoViewType priceInfoViewType, String selectedCategoryID, int currentPage, bool hasReachedMax, int symbolsCount, List<String> previousSearches, SymbolsProcessState currentState
});


$SymbolsProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$SymbolsStateCopyWithImpl<$Res>
    implements $SymbolsStateCopyWith<$Res> {
  _$SymbolsStateCopyWithImpl(this._self, this._then);

  final SymbolsState _self;
  final $Res Function(SymbolsState) _then;

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolDetailViewModel = null,Object? symbolQuoteViewModel = null,Object? sortOrder = null,Object? priceInfoViewType = null,Object? selectedCategoryID = null,Object? currentPage = null,Object? hasReachedMax = null,Object? symbolsCount = null,Object? previousSearches = null,Object? currentState = null,}) {
  return _then(_self.copyWith(
symbolDetailViewModel: null == symbolDetailViewModel ? _self.symbolDetailViewModel : symbolDetailViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolDetailViewModel>>,symbolQuoteViewModel: null == symbolQuoteViewModel ? _self.symbolQuoteViewModel : symbolQuoteViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolQuoteViewModel>>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,selectedCategoryID: null == selectedCategoryID ? _self.selectedCategoryID : selectedCategoryID // ignore: cast_nullable_to_non_nullable
as String,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,symbolsCount: null == symbolsCount ? _self.symbolsCount : symbolsCount // ignore: cast_nullable_to_non_nullable
as int,previousSearches: null == previousSearches ? _self.previousSearches : previousSearches // ignore: cast_nullable_to_non_nullable
as List<String>,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as SymbolsProcessState,
  ));
}
/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolsProcessStateCopyWith<$Res> get currentState {
  
  return $SymbolsProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _SymbolsState extends SymbolsState with DiagnosticableTreeMixin {
   _SymbolsState({required this.symbolDetailViewModel, required this.symbolQuoteViewModel, this.sortOrder = SortOrderOptions.defaultOption, this.priceInfoViewType = SymbolPriceInfoViewTypeOptions.defaultOption, this.selectedCategoryID = '', this.currentPage = 1, this.hasReachedMax = false, this.symbolsCount = 0, this.previousSearches = const [], this.currentState = const SymbolsProcessState.loading()}): super._();
  

@override final  Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel;
@override final  Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel;
@override@JsonKey()  SortOrder sortOrder;
@override@JsonKey()  SymbolPriceInfoViewType priceInfoViewType;
@override@JsonKey()  String selectedCategoryID;
@override@JsonKey()  int currentPage;
@override@JsonKey()  bool hasReachedMax;
@override@JsonKey()  int symbolsCount;
@override@JsonKey()  List<String> previousSearches;
@override@JsonKey()  SymbolsProcessState currentState;

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SymbolsStateCopyWith<_SymbolsState> get copyWith => __$SymbolsStateCopyWithImpl<_SymbolsState>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsState'))
    ..add(DiagnosticsProperty('symbolDetailViewModel', symbolDetailViewModel))..add(DiagnosticsProperty('symbolQuoteViewModel', symbolQuoteViewModel))..add(DiagnosticsProperty('sortOrder', sortOrder))..add(DiagnosticsProperty('priceInfoViewType', priceInfoViewType))..add(DiagnosticsProperty('selectedCategoryID', selectedCategoryID))..add(DiagnosticsProperty('currentPage', currentPage))..add(DiagnosticsProperty('hasReachedMax', hasReachedMax))..add(DiagnosticsProperty('symbolsCount', symbolsCount))..add(DiagnosticsProperty('previousSearches', previousSearches))..add(DiagnosticsProperty('currentState', currentState));
}



@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsState(symbolDetailViewModel: $symbolDetailViewModel, symbolQuoteViewModel: $symbolQuoteViewModel, sortOrder: $sortOrder, priceInfoViewType: $priceInfoViewType, selectedCategoryID: $selectedCategoryID, currentPage: $currentPage, hasReachedMax: $hasReachedMax, symbolsCount: $symbolsCount, previousSearches: $previousSearches, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$SymbolsStateCopyWith<$Res> implements $SymbolsStateCopyWith<$Res> {
  factory _$SymbolsStateCopyWith(_SymbolsState value, $Res Function(_SymbolsState) _then) = __$SymbolsStateCopyWithImpl;
@override @useResult
$Res call({
 Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel, Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel, SortOrder sortOrder, SymbolPriceInfoViewType priceInfoViewType, String selectedCategoryID, int currentPage, bool hasReachedMax, int symbolsCount, List<String> previousSearches, SymbolsProcessState currentState
});


@override $SymbolsProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$SymbolsStateCopyWithImpl<$Res>
    implements _$SymbolsStateCopyWith<$Res> {
  __$SymbolsStateCopyWithImpl(this._self, this._then);

  final _SymbolsState _self;
  final $Res Function(_SymbolsState) _then;

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolDetailViewModel = null,Object? symbolQuoteViewModel = null,Object? sortOrder = null,Object? priceInfoViewType = null,Object? selectedCategoryID = null,Object? currentPage = null,Object? hasReachedMax = null,Object? symbolsCount = null,Object? previousSearches = null,Object? currentState = null,}) {
  return _then(_SymbolsState(
symbolDetailViewModel: null == symbolDetailViewModel ? _self.symbolDetailViewModel : symbolDetailViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolDetailViewModel>>,symbolQuoteViewModel: null == symbolQuoteViewModel ? _self.symbolQuoteViewModel : symbolQuoteViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolQuoteViewModel>>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,selectedCategoryID: null == selectedCategoryID ? _self.selectedCategoryID : selectedCategoryID // ignore: cast_nullable_to_non_nullable
as String,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,symbolsCount: null == symbolsCount ? _self.symbolsCount : symbolsCount // ignore: cast_nullable_to_non_nullable
as int,previousSearches: null == previousSearches ? _self.previousSearches : previousSearches // ignore: cast_nullable_to_non_nullable
as List<String>,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as SymbolsProcessState,
  ));
}

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolsProcessStateCopyWith<$Res> get currentState {
  
  return $SymbolsProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$SymbolsProcessState implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsProcessState'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsProcessState()';
}


}

/// @nodoc
class $SymbolsProcessStateCopyWith<$Res>  {
$SymbolsProcessStateCopyWith(SymbolsProcessState _, $Res Function(SymbolsProcessState) __);
}


/// @nodoc


class SymbolsLoadingState with DiagnosticableTreeMixin implements SymbolsProcessState {
  const SymbolsLoadingState();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsProcessState.loading'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsProcessState.loading()';
}


}




/// @nodoc


class SymbolschangeTabState with DiagnosticableTreeMixin implements SymbolsProcessState {
  const SymbolschangeTabState();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsProcessState.changeTab'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolschangeTabState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsProcessState.changeTab()';
}


}




/// @nodoc


class SymbolsSuccessState with DiagnosticableTreeMixin implements SymbolsProcessState {
  const SymbolsSuccessState();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsProcessState.success'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsProcessState.success()';
}


}




/// @nodoc


class SymbolsErrorState with DiagnosticableTreeMixin implements SymbolsProcessState {
  const SymbolsErrorState();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsProcessState.error'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsProcessState.error()';
}


}




/// @nodoc


class SymbolsPriceSuccessState with DiagnosticableTreeMixin implements SymbolsProcessState {
  const SymbolsPriceSuccessState();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'SymbolsProcessState.priceSucces'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsPriceSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'SymbolsProcessState.priceSucces()';
}


}




// dart format on
