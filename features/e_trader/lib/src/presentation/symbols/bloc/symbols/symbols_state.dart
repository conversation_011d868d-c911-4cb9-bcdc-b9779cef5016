part of 'symbols_bloc.dart';

@unfreezed
// ignore: prefer-immutable-bloc-state
sealed class SymbolsState with _$SymbolsState {
  const SymbolsState._();
  factory SymbolsState({
    required final Map<String, Map<String, SymbolDetailViewModel>>
    symbolDetailViewModel,
    required final Map<String, Map<String, SymbolQuoteViewModel>>
    symbolQuoteViewModel,
    @Default(SortOrderOptions.defaultOption) SortOrder sortOrder,
    @Default(SymbolPriceInfoViewTypeOptions.defaultOption)
    SymbolPriceInfoViewType priceInfoViewType,
    @Default('') String selectedCategoryID,
    @Default(1) int currentPage,
    @Default(false) bool hasReachedMax,
    @Default(0) int symbolsCount,
    @Default([]) List<String> previousSearches,
    @Default(SymbolsProcessState.loading()) SymbolsProcessState currentState,
  }) = _SymbolsState;

  Map<String, SymbolDetailViewModel> get symbolsDetail {
    symbolDetailViewModel.putIfAbsent(selectedCategoryID, () => {});
    return symbolDetailViewModel[selectedCategoryID]!;
  }

  Map<String, SymbolQuoteViewModel> get symbolsQuote {
    symbolQuoteViewModel.putIfAbsent(selectedCategoryID, () => {});
    return symbolQuoteViewModel[selectedCategoryID]!;
  }

  void setSymbolsDetail(Map<String, SymbolDetailViewModel> symbolsDetailMap) =>
      symbolDetailViewModel[selectedCategoryID] = symbolsDetailMap;

  void setSymbolsCount(int count) => symbolsCount = count;

  List<String> getSymbols() =>
      symbolsDetail.values.map((symbol) => symbol.symbolName).toList();
}

@freezed
sealed class SymbolsProcessState with _$SymbolsProcessState {
  const factory SymbolsProcessState.loading() = SymbolsLoadingState;
  const factory SymbolsProcessState.changeTab() = SymbolschangeTabState;
  const factory SymbolsProcessState.success() = SymbolsSuccessState;
  const factory SymbolsProcessState.error() = SymbolsErrorState;
  const factory SymbolsProcessState.priceSucces() = SymbolsPriceSuccessState;
}
