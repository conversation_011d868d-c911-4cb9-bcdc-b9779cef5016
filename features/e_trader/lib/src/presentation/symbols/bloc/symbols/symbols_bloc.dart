import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/linked_symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/api/watchlist_model.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_symbols_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_watchlist_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_list_of_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/symbol_local_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_quotes_by_symbols_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/model/symbol_quote_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

part 'symbols_bloc.freezed.dart';
part 'symbols_event.dart';
part 'symbols_state.dart';

class SymbolsBloc extends Bloc<SymbolsEvent, SymbolsState>
    with DisposableMixin {
  SymbolsBloc(
    this._getSymbolsUseCase,
    this._logger,
    this._subscribeToSymbolsQuotesUseCase,
    this._updateQuotesBySymbolsUseCase,
    this._navigation,
    this._symbolLocalDataUseCase,
    this._getAccountNumberUseCase,
    this._getWatchlistDataUseCase,
  ) : super(
        SymbolsState(
          symbolDetailViewModel: {},
          symbolQuoteViewModel: {},
          sortOrder: _symbolLocalDataUseCase.getSortingOption(),
          priceInfoViewType: _symbolLocalDataUseCase.getViewTypeOption(),
          previousSearches: _symbolLocalDataUseCase.getPreviousSearches(),
        ),
      ) {
    on<_ProcessSymbolPriceResult>(
      (event, emit) => _processSymbolPriceResult(event, emit),
    );

    on<_SubscribeToSymbol>((event, emit) => _subscribeToSymbol(event));
    on<_UnsubscribeToSymbol>((event, emit) => _unsubscribeToSymbol(event));
    on<_OnClearSymbols>((event, emit) => _onClearSymbols(emit));
    on<_OnGoToDetails>((event, emit) => _onGoToDetails(event));
    on<SymbolsEvent>((event, emit) {
      if (event is _OnCategorySelected) {
        return _onCategorySelected(event, emit);
      }

      if (event is _OnGetSymbols) {
        return _onGetSymbols(event, emit);
      }
      if (event is _OnGetWtachlistSymbols) {
        return _onGetWatchlistSymbols(emit);
      }
      if (event is _OnSortSymbols) {
        return _onSortSymbols(event, emit);
      }
      if (event is _OnPriceViewChange) {
        return _onPriceViewChange(event, emit);
      }
      if (event is _ManageSubscription) {
        return _manageSubscription(event);
      }
    }, transformer: sequential());
    on<_OnSearchSymbols>((event, emit) {
      return _onSearchSymbols(event, emit);
    }, transformer: throttleTransformer(_throttleDuration));
  }
  final _throttleDuration = Duration(milliseconds: 500);
  final LoggerBase _logger;
  final GetSymbolsUseCase _getSymbolsUseCase;
  final SubscribeToListOfSymbolQuotesUseCase _subscribeToSymbolsQuotesUseCase;
  final UpdateQuotesBySymbolsUseCase _updateQuotesBySymbolsUseCase;
  final EquitiTraderNavigation _navigation;
  final SymbolLocalDataUseCase _symbolLocalDataUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final GetWatchlistDataUseCase _getWatchlistDataUseCase;

  FutureOr<void> _onCategorySelected(
    _OnCategorySelected event,
    Emitter<SymbolsState> emit,
  ) {
    emit(
      state.copyWith(
        selectedCategoryID: event.categoryID,
        currentState: SymbolsProcessState.changeTab(),
      ),
    );
    add(SymbolsEvent.onGetSymbols());
  }

  void _processSymbolPriceResult(
    _ProcessSymbolPriceResult event,
    Emitter<SymbolsState> emit,
  ) {
    final symbolQuoteModel = event.result;
    final existingSymbolDetail = state.symbolsDetail[symbolQuoteModel.symbol];
    if (existingSymbolDetail == null) return;
    final symbolQuote =
        state.symbolsQuote[symbolQuoteModel.symbol] ??
        SymbolQuoteViewModel(
          ask: symbolQuoteModel.ask,
          bid: symbolQuoteModel.bid,
          digits: symbolQuoteModel.digits,
          spread: symbolQuoteModel.spread,
          direction: symbolQuoteModel.direction,
          dailyChange: symbolQuoteModel.dailyRateChange,
          midPrice: symbolQuoteModel.midPrice,
        );

    if (state.symbolsQuote[symbolQuoteModel.symbol] != null) {
      symbolQuote
        ..ask = symbolQuoteModel.ask
        ..bid = symbolQuoteModel.bid
        ..digits = symbolQuoteModel.digits
        ..spread = symbolQuoteModel.spread
        ..direction = symbolQuoteModel.direction
        ..midPrice = symbolQuoteModel.midPrice
        ..dailyChange = symbolQuoteModel.dailyRateChange;
    }

    state.symbolsQuote[symbolQuoteModel.symbol] = symbolQuote;
    state.symbolQuoteViewModel[state.selectedCategoryID] = state.symbolsQuote;
    if (isClosed) return;
    emit(state.copyWith(currentState: SymbolsProcessState.priceSucces()));
  }

  Future<void> _onGetSymbols(
    _OnGetSymbols event,
    Emitter<SymbolsState> emit,
  ) async {
    if (state.selectedCategoryID.isEmpty && (event.query?.length ?? 0) < 1)
      return;
    if (state.hasReachedMax) return;
    if (state.selectedCategoryID == 'watchlist') {
      return add(SymbolsEvent.onGetWtachlistSymbols());
    }
    final response =
        await _fetchAndProcessSymbols(event)
            .flatMap(_handlePagination)
            .flatMap(
              (_) => _subscribeToSymbolsQuotesUseCase(
                subscriberId: '${SymbolsBloc}_$hashCode',
              ),
            )
            .run();
    return response.fold(
      (exception) => _handleError(exception, emit),
      (symbolPriceResult) => _handleSuccess(symbolPriceResult, emit),
    );
  }

  Future<void> _onGetWatchlistSymbols(Emitter<SymbolsState> emit) async {
    final watchlistResponse = await _getWatchlistDataUseCase().run();
    return watchlistResponse.fold(
      (exception) => _handleError(exception, emit),
      (watchlistModels) {
        final symbolModels =
            watchlistModels.map((w) => w.toSymbolModel()).toList();
        final linkedSymbolModel = LinkedSymbolModel(
          symbols: symbolModels,
          count: symbolModels.length,
        );
        return _updateSymbolsDetail(linkedSymbolModel)
            .flatMap(_handlePagination)
            .flatMap(
              (_) => _subscribeToSymbolsQuotesUseCase(
                subscriberId: '${SymbolsBloc}_$hashCode',
              ),
            )
            .run()
            .then((response) {
              return response.fold(
                (exception) => _handleError(exception, emit),
                (symbolPriceResult) => _handleSuccess(symbolPriceResult, emit),
              );
            });
      },
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _fetchAndProcessSymbols(
    _OnGetSymbols event,
  ) => _getSymbolsUseCase(
    categoryID: state.selectedCategoryID,
    pageNumber: state.currentPage,
    query: event.query,
    sortOrder: state.sortOrder,
  ).chainFirst(_updateSymbolsDetail);

  TaskEither<Exception, LinkedSymbolModel> _updateSymbolsDetail(
    LinkedSymbolModel linkedSymbols,
  ) {
    final symbols =
        linkedSymbols.symbols
            .where((symbol) => symbol.platformName != null)
            .toList();
    state.setSymbolsCount(linkedSymbols.count);
    if (state.symbolsDetail.isEmpty ||
        state.selectedCategoryID == "watchlist") {
      _createInitialSymbolsMap(symbols);
    } else {
      _updateExistingSymbolsMap(symbols);
    }

    return TaskEither.of(linkedSymbols);
  }

  void _createInitialSymbolsMap(List<SymbolModel> symbols) {
    if (state.selectedCategoryID == "watchlist") {
      state.sortOrder == SortOrder.ascending
          ? symbols.sort((a, b) => a.tickerName!.compareTo(b.tickerName!))
          : symbols.sort((b, a) => a.tickerName!.compareTo(b.tickerName!));
    }

    final symbolsMap = <String, SymbolDetailViewModel>{
      for (final symbol in symbols)
        symbol.platformName!: SymbolDetailViewModel(
          symbolName: symbol.tickerName!,
          imageURL: symbol.productLogoUrl,
          assetType: symbol.assetType,
          minLot: symbol.minLot,
          maxLot: symbol.maxLot,
          digit: symbol.digits ?? 5,
          isForex: symbol.isForex,
        ),
    };
    state.setSymbolsDetail(symbolsMap);
  }

  void _updateExistingSymbolsMap(List<SymbolModel> symbols) {
    final existingSymbolsMap = state.symbolsDetail;
    for (final symbol in symbols) {
      final existingSymbol = existingSymbolsMap[symbol.platformName];
      existingSymbolsMap[symbol.platformName!] =
          existingSymbol != null
              ? _updateExistingSymbol(existingSymbol, symbol)
              : _createNewSymbol(symbol);
    }
    state.setSymbolsDetail(existingSymbolsMap);
  }

  SymbolDetailViewModel _updateExistingSymbol(
    SymbolDetailViewModel existing,
    SymbolModel symbol,
  ) {
    return existing
      ..symbolName = symbol.tickerName!
      ..imageURL = symbol.productLogoUrl
      ..assetType = symbol.assetType;
  }

  SymbolDetailViewModel _createNewSymbol(SymbolModel symbol) {
    return SymbolDetailViewModel(
      symbolName: symbol.tickerName!,
      imageURL: symbol.productLogoUrl,
      assetType: symbol.assetType,
      minLot: symbol.minLot,
      maxLot: symbol.maxLot,
      digit: symbol.digits ?? 5,
      isForex: symbol.isForex,
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _handlePagination(
    LinkedSymbolModel linkedSymbols,
  ) {
    if (state.symbolsDetail.length == linkedSymbols.count) {
      state.hasReachedMax = true;
    } else {
      state.currentPage = state.currentPage + 1;
    }
    return TaskEither.of(linkedSymbols);
  }

  void _handleError(Exception exception, Emitter<SymbolsState> emit) {
    addError(exception);
    emit(state.copyWith(currentState: SymbolsProcessState.error()));
  }

  void _handleSuccess(
    Stream<SymbolQuoteModel> symbolPriceResult,
    Emitter<SymbolsState> emit,
  ) {
    addSubscription(
      symbolPriceResult.listen(
        (result) {
          add(SymbolsEvent.processSymbolPriceResult(result: result));
        },
        onError: (Object e, StackTrace stackTrace) {
          addError(e, stackTrace);
        },
      ),
    );
    emit(state.copyWith(currentState: SymbolsProcessState.success()));
  }

  FutureOr<void> _onSortSymbols(
    _OnSortSymbols event,
    Emitter<SymbolsState> emit,
  ) async {
    await _symbolLocalDataUseCase.saveSortingOption(event.sortOrder);
    emit(
      state.copyWith(
        currentState: SymbolsProcessState.loading(),
        currentPage: 1,
        hasReachedMax: false,
        sortOrder: event.sortOrder,
        symbolDetailViewModel: {},
      ),
    );

    add(SymbolsEvent.onGetSymbols());
  }

  FutureOr<void> _onPriceViewChange(
    _OnPriceViewChange event,
    Emitter<SymbolsState> emit,
  ) {
    _symbolLocalDataUseCase.saveViewOption(event.viewType);
    emit(state.copyWith(priceInfoViewType: event.viewType));
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    super.addError(error, stackTrace);
    _logger.logError(error, stackTrace: stackTrace);
  }

  Future<void> _subscribeToSymbol(_SubscribeToSymbol event) {
    return _updateQuotesBySymbolsUseCase(
      event.symbols,
      TradingSocketEvent.quotes.subscribe,
    );
  }

  Future<void> _unsubscribeToSymbol(_UnsubscribeToSymbol event) {
    return _updateQuotesBySymbolsUseCase(
      event.symbols,
      TradingSocketEvent.quotes.unsubscribe,
    );
  }

  FutureOr<void> _onGoToDetails(_OnGoToDetails event) {
    return _getAccountNumberUseCase().fold((left) => "", (number) {
      _navigation.navigateToProductDetail(
        symbolDetail: event.symbolDetail,
        accountNumber: number,
      );
    });
  }

  FutureOr<void> _onClearSymbols(Emitter<SymbolsState> emit) {
    emit(
      state.copyWith(
        hasReachedMax: false,
        currentPage: 1,
        selectedCategoryID: '',
      ),
    );
  }

  Future<void> _onSearchSymbols(
    _OnSearchSymbols event,
    Emitter<SymbolsState> emit,
  ) async {
    final query = event.query.trim();
    emit(
      state.copyWith(
        currentState: SymbolsProcessState.loading(),
        symbolDetailViewModel: {},
        currentPage: 1,
        hasReachedMax: false,
      ),
    );
    if (query.isEmpty) {
      emit(state.copyWith(currentState: SymbolsProcessState.success()));
      return;
    }
    ;

    if (event.query.length > 1) {
      add(SymbolsEvent.onGetSymbols(query: query));
      List<String> previousSearches =
          await _symbolLocalDataUseCase.getPreviousSearches();
      if (!previousSearches.contains(event.query)) {
        _symbolLocalDataUseCase.savePreviousSearches(event.query);
      }
    }
  }

  FutureOr<void> _manageSubscription(_ManageSubscription event) {
    if (event.status == SubscriptionStatus.pause) {
      pauseSubscriptions();
    } else {
      resumeSubscriptions();
    }
  }
}
