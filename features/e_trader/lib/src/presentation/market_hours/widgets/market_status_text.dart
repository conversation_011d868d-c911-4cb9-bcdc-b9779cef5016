import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/market_session.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class MarketStatusText extends StatelessWidget {
  const MarketStatusText({super.key, required this.status});
  final MarketStatus status;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    final textColor =
        MarketStatus.open == status
            ? theme.text.textBrandTertiary
            : theme.text.textErrorPrimary;
    final statusText =
        status == MarketStatus.open
            ? localization.trader_open
            : localization.trader_closed;
    final nextStatus =
        status == MarketStatus.open
            ? localization.trader_itWillCloseIn
            : localization.trader_itWillOpen;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: localization.trader_tradingHours,
          style: duploTextStyles.textMd,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
        ),
        SizedBox(height: 4),
        Wrap(
          children: [
            DuploText(
              text: localization.trader_marketIs,
              style: duploTextStyles.textSm,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
            const SizedBox(width: 5),
            DuploText(
              text: statusText,
              style: duploTextStyles.textSm,
              fontWeight: DuploFontWeight.semiBold,
              color: textColor,
            ),
            DuploText(
              text: ", $nextStatus",
              style: duploTextStyles.textSm,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
      ],
    );
  }
}
