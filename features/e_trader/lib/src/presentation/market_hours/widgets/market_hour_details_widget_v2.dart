import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/market_hours/bloc/market_hours_bloc.dart';
import 'package:e_trader/src/presentation/market_hours/widgets/holidays_display_widget.dart';
import 'package:e_trader/src/presentation/market_hours/widgets/market_hours_timer_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MarketHourDetailsWidgetV2 extends StatelessWidget {
  final String symbol;

  const MarketHourDetailsWidgetV2({required this.symbol});

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    final loc = EquitiLocalization.of(context);

    return BlocProvider(
      create:
          (_) =>
              diContainer<MarketHoursBloc>()
                ..add(MarketHoursEvent.getMarketHours(symbolCode: symbol)),
      child: BlocBuilder<MarketHoursBloc, MarketHoursState>(
        buildWhen:
            (previous, current) =>
                previous.proccesState != current.proccesState,
        builder: (blocBuilderContext, state) {
          return switch (state.proccesState) {
            MarketHoursLoadingState() => Container(
              height: 330,
              decoration: BoxDecoration(
                color: theme.background.bgPrimary,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.border.borderSecondary,
                  width: 1.0,
                ),
              ),
              child: Center(
                child: DuploText(
                  text: loc.trader_pleaseWait,
                  style: duploTextStyles.textMd,
                  color: theme.text.textPrimary,
                ),
              ),
            ),
            MarketHoursErrorState() => Container(
              height: 330,
              decoration: BoxDecoration(
                color: theme.background.bgPrimary,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.border.borderSecondary,
                  width: 1.0,
                ),
              ),
              child: Center(
                child: DuploText(
                  text: loc.trader_marketLoadFailed,
                  style: duploTextStyles.textMd,
                  color: theme.text.textErrorPrimary,
                ),
              ),
            ),
            MarketHoursSuccessState(:final marketInfo) => () {
              final currentStatus = marketInfo.currentMarketStatus();
              final todayTimings = marketInfo.todayTimings();
              final marketHours = marketInfo.marketHoursForDisplay(context);
              final holidays = marketInfo.holidaysForDisplay(context);
              final timeUntillNext = marketInfo.timeUntillNextStatusChange();
              return Container(
                decoration: BoxDecoration(
                  color: theme.background.bgPrimary,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.border.borderSecondary,
                    width: 1.0,
                  ),
                ),
                padding: const EdgeInsets.only(left: 16, right: 16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 16),
                      MarketHoursTimerWidget(
                        timeUntill: timeUntillNext,
                        marketStatus: currentStatus,
                      ),
                      const SizedBox(height: 6),
                      DuploKeyValueDisplay(
                        keyValuePairs: [
                          KeyValuePair(
                            label: loc.trader_todays_hours,
                            value:
                                todayTimings.isNotEmpty
                                    ? todayTimings
                                    : loc.trader_closed,
                          ),
                        ],
                      ),
                      if (holidays.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 16, bottom: 0),
                          child: HolidaysDisplayWidget(holidays: holidays),
                        ),
                      const SizedBox(height: 16),
                      _MarketHoursWidget(
                        marketHours: marketHours,
                        localization: loc,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              );
            }(),
          };
        },
      ),
    );
  }
}

class _MarketHoursWidget extends StatefulWidget {
  final List<KeyValuePair> marketHours;
  final EquitiLocalization localization;
  const _MarketHoursWidget({
    required this.marketHours,
    required this.localization,
  });

  @override
  State<_MarketHoursWidget> createState() => _MarketHoursWidgetState();
}

class _MarketHoursWidgetState extends State<_MarketHoursWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              DuploText(
                text:
                    _isExpanded
                        ? widget.localization.trader_hideAll
                        : widget.localization.trader_showAll,
                style: duploTextStyles.textSm,
                color: theme.text.textBrandPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
              AnimatedRotation(
                turns: _isExpanded ? 0.5 : 0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: Icon(
                  Icons.keyboard_arrow_down,
                  color: theme.text.textBrandPrimary,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
        // Smooth vertical slide animation
        ClipRect(
          child: AnimatedAlign(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            heightFactor: _isExpanded ? 1.0 : 0.0,
            alignment: Alignment.topCenter,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 12),
                DuploText(
                  text: widget.localization.trader_trading_hours_each_day,
                  style: duploTextStyles.textMd,
                  color: theme.text.textPrimary,
                  fontWeight: DuploFontWeight.semiBold,
                ),
                // const SizedBox(height: 2),
                DuploKeyValueDisplay(
                  keyValuePairs: widget.marketHours,
                  hideLastDivider: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
