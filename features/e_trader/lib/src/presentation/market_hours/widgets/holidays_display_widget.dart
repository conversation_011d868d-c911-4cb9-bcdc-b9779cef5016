import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/holiday_display_model.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as Trader;

class HolidaysDisplayWidget extends StatelessWidget {
  final List<HolidayDisplayModel> holidays;
  const HolidaysDisplayWidget({required this.holidays});

  @override
  Widget build(BuildContext context) {
    DateFormat formatter = DateFormat('HH:mm / dd.MM.yyyy');

    final duploTextStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    final loc = EquitiLocalization.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: loc.trader_upComingHolidayHour,
          style: duploTextStyles.textMd,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
        ),
        const SizedBox(height: 8),
        Column(
          children:
              holidays.map((holiday) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Trader.Assets.images.calendar.svg(),
                        SizedBox(width: 8),
                        DuploText(
                          text: holiday.title,
                          style: duploTextStyles.textSm,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textSecondary,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DuploText(
                              text: loc.trader_closed,
                              style: duploTextStyles.textSm,
                              fontWeight: DuploFontWeight.regular,
                              color: theme.text.textTertiary,
                            ),
                            DuploText(
                              text: formatter.format(
                                holiday.dateTimeStart.toLocal(),
                              ),
                              style: duploTextStyles.textSm,
                              fontWeight: DuploFontWeight.regular,
                              color: theme.text.textPrimary,
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            DuploText(
                              text: loc.trader_opens,
                              style: duploTextStyles.textSm,
                              fontWeight: DuploFontWeight.regular,
                              color: theme.text.textTertiary,
                            ),
                            DuploText(
                              text: formatter.format(
                                holiday.dateTimeNextOpen.toLocal(),
                              ),
                              style: duploTextStyles.textSm,
                              fontWeight: DuploFontWeight.regular,
                              color: theme.text.textPrimary,
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Divider(height: 1, color: theme.border.borderSecondary),
                  ],
                );
              }).toList(),
        ),
      ],
    );
  }
}
