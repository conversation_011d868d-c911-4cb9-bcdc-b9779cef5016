import 'dart:async';

import 'package:e_trader/src/domain/usecase/get_market_timing_calculator_use_case.dart';
import 'package:e_trader/src/presentation/market_hours/bloc/market_timing_calculator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';

part 'market_hours_bloc.freezed.dart';
part 'market_hours_event.dart';
part 'market_hours_state.dart';

class MarketHoursBloc extends Bloc<MarketHoursEvent, MarketHoursState> {
  final GetMarketTimingCalculatorUseCase _getMarketLimitCalculatorUseCase;
  final LoggerBase _logger;

  MarketHoursBloc({
    required LoggerBase logger,
    required GetMarketTimingCalculatorUseCase getMarketLimitCalculatorUseCase,
  }) : _logger = logger,
       _getMarketLimitCalculatorUseCase = getMarketLimitCalculatorUseCase,
       super(MarketHoursState()) {
    on<_GetMarketHours>(_getMarketHours);
  }

  FutureOr<void> _getMarketHours(
    _GetMarketHours event,
    Emitter<MarketHoursState> emit,
  ) async {
    final result =
        await _getMarketLimitCalculatorUseCase(
          symbolCode: event.symbolCode,
        ).run();
    result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(proccesState: MarketHoursProcessState.error()));
      },
      (checker) {
        emit(
          state.copyWith(
            proccesState: MarketHoursProcessState.success(checker),
          ),
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error);
    super.addError(error, stackTrace);
  }
}
