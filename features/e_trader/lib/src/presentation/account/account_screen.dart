import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/account/account_details/account_details_tab.dart';
import 'package:e_trader/src/presentation/account/bloc/account_details_bloc.dart';
import 'package:e_trader/src/presentation/more/trading_settings/trading_settings_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AccountScreen extends StatefulWidget {
  const AccountScreen({super.key, this.tabIndex = 0});

  final int tabIndex;

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.tabIndex,
    );
  }

  @override
  didUpdateWidget(covariant AccountScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.tabIndex != oldWidget.tabIndex) {
      _tabController.index = widget.tabIndex;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      body: BlocProvider(
        create:
            (ctx) =>
                diContainer<AccountDetailsBloc>()
                  ..add(AccountDetailsEvent.subscribeToTradingAccountBalance()),
        child: Column(
          children: [
            TabBar(
              controller: _tabController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              dividerColor: theme.border.borderSecondary,
              indicatorColor: theme.foreground.fgBrandPrimaryAlt,
              labelColor: theme.text.textBrandSecondary,
              labelStyle: TextStyle(
                fontSize: duploTextStyles.textSm.fontSize,
                fontWeight: DuploFontWeight.semiBold.value,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              unselectedLabelColor: theme.text.textQuaternary,
              splashFactory: null,
              overlayColor: WidgetStateProperty.all(Colors.transparent),
              tabAlignment: TabAlignment.fill,
              unselectedLabelStyle: TextStyle(
                fontSize: duploTextStyles.textSm.fontSize,
                fontWeight: DuploFontWeight.medium.value,
              ),
              isScrollable: false,
              tabs: [
                Tab(text: l10n.trader_details),
                Tab(text: l10n.trader_settings),
              ],
            ),
            Expanded(
              child: TabBarView(
                physics: const NeverScrollableScrollPhysics(),
                controller: _tabController,
                children: [
                  const AccountDetailsTab(),
                  const TradingSettingsScreen(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
