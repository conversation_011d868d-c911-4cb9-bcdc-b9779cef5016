import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/change_leverage/bloc/change_leverage_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ChangeLeverageScreen extends StatelessWidget {
  final String accountNumber;
  final int? leverage;
  const ChangeLeverageScreen({
    super.key,
    required this.accountNumber,
    this.leverage,
  });

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create:
          (createContext) => diContainer<ChangeLeverageBloc>(param1: leverage)
            ..add(
              ChangeLeverageEvent.onGetLeverages(accountNumber: accountNumber),
            ),
      child: BlocConsumer<ChangeLeverageBloc, ChangeLeverageState>(
        buildWhen:
            (previous, current) =>
                previous.changeLeverage != current.changeLeverage,
        listenWhen:
            (previous, current) =>
                previous.changeLeverage != current.changeLeverage,
        listener: (listenerContext, state) {
          if (state.changeLeverage is ChangeLeverageSuccess) {
            Navigator.of(listenerContext).pop();
          }
          if (state.changeLeverage is ChangeLeverageError) {
            final toast = DuploToast();

            toast.showToastMessage(
              context: listenerContext,
              widget: DuploToastMessage(
                titleMessage: localization.trader_somethingWentWrong,
                descriptionMessage:
                    localization.trader_unable_to_change_leverage,

                messageType: ToastMessageType.error,
                onLeadingAction: () {
                  toast.hidesToastMessage();
                },
              ),
            );
          }
        },
        builder: (builder1Context, state1) {
          return BlocBuilder<ChangeLeverageBloc, ChangeLeverageState>(
            buildWhen:
                (previous, current) =>
                    previous.processState != current.processState ||
                    previous.changeLeverage != current.changeLeverage,
            builder: (builderContext, state) {
              return switch (state.processState) {
                ChangeLeverageLoading() || ChangeLeverageInitial() => Center(
                  child: CircularProgressIndicator(),
                ),
                ChangeLeverageSuccess() || ChangeLeverageError() => () {
                  final allOptions = <SelectionOptionModel>[];

                  state.leverages.forEach((element) {
                    final model = SelectionOptionModel(
                      displayText:
                          "1:${EquitiFormatter.formatNumber(value: element, locale: Localizations.localeOf(context).toString())}",
                      identifier: EquitiFormatter.formatNumber(
                        value: element,
                        locale: Localizations.localeOf(context).toString(),
                      ),
                    );

                    allOptions.add(model);
                  });

                  // Only show TextSelectionComponentScreen if we have options
                  if (allOptions.isEmpty) {
                    return Center(child: Text("No leverages available"));
                  }

                  return TextSelectionComponentScreen(
                    pageTitle: "",
                    isLoading: state.changeLeverage is ChangeLeverageLoading,
                    buttonTitle: EquitiLocalization.of(context).trader_save,
                    options: allOptions,
                    selected: allOptions.firstOrNullWhere(
                      (option) =>
                          int.tryParse(option.identifier) ==
                          state.selectedLeverage,
                    ),
                    onSelection: (option) {
                      final loc = Localizations.localeOf(builderContext);
                      builderContext.read<ChangeLeverageBloc>().add(
                        ChangeLeverageEvent.onChangeLeverages(
                          selectedLeverage: EquitiFormatter.formatNumber(
                            value: int.tryParse(option.identifier) ?? 0,
                            locale: loc.toString(),
                          ),
                          accountNumber: accountNumber,
                        ),
                      );
                    },
                  );
                }(),
              };
            },
          );
        },
      ),
    );
  }
}
