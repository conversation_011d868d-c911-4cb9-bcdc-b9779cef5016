import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/symbols/widgets/buy_sell_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:theme_manager/theme_manager.dart';

class BuySellPlaceholderWidget extends StatelessWidget {
  const BuySellPlaceholderWidget({super.key});
  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final l10n = EquitiLocalization.of(context);
    final isDarkMode = diContainer<ThemeManager>().isDarkMode;

    return Container(
      color: theme.background.bgPrimary,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Row(
              children: [
                isDarkMode
                    ? trader.Assets.images.equitiAvatarPlaceholderDark.svg(
                      height: 33,
                      width: 33,
                    )
                    : trader.Assets.images.equitiAvatarPlaceholderLight.svg(
                      height: 33,
                      width: 33,
                    ),
                const SizedBox(width: 5),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploText(
                      text: l10n.trader_marketName,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.text.textPrimary,
                      style: duploTextStyles.textSm,
                    ),
                    DuploText(
                      text: l10n.trader_marketClass,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textTertiary,
                      style: duploTextStyles.textSm,
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: BuySellContainer(
                        price: l10n.trader_close_placeholder_price,
                        type: TradeType.sell,
                        highlightLength: 0,
                        isPriceAText: true,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: BuySellContainer(
                        type: TradeType.buy,
                        price: l10n.trader_close_placeholder_price,
                        highlightLength: 0,
                        isPriceAText: true,
                      ),
                    ),
                  ],
                ),
                Align(
                  alignment: AlignmentDirectional.topCenter,
                  child: Container(
                    width: 60,
                    height: 21,
                    padding: const EdgeInsets.only(
                      left: 11,
                      right: 11,
                      bottom: 5,
                      top: 0,
                    ),
                    decoration: BoxDecoration(
                      color: theme.background.bgPrimary,
                      borderRadius: BorderRadius.vertical(
                        bottom: Radius.circular(4),
                      ),
                      boxShadow: [
                        //TODO(Danya): replace with correct duplo shadows
                        BoxShadow(
                          color: Color(0x1A006B67),
                          blurRadius: 6,
                          offset: Offset(0, 4),
                          spreadRadius: -2,
                        ),
                        BoxShadow(
                          color: Color(0x14101828),
                          blurRadius: 16,
                          offset: Offset(0, 12),
                          spreadRadius: -4,
                        ),
                      ],
                    ),
                    child: FittedBox(
                      fit: BoxFit.cover,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          DuploText(
                            text: l10n.trader_spread,
                            style: duploTextStyles.textXxs,
                            color: theme.text.textPrimary,
                            fontWeight: DuploFontWeight.medium,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
