import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:theme_manager/theme_manager.dart';

class MidPricePlaceholderWidget extends StatelessWidget {
  const MidPricePlaceholderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final l10n = EquitiLocalization.of(context);
    final isDarkMode = diContainer<ThemeManager>().isDarkMode;

    return Container(
      color: theme.background.bgPrimary,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              isDarkMode
                  ? trader.Assets.images.equitiAvatarPlaceholderDark.svg(
                    height: 33,
                    width: 33,
                  )
                  : trader.Assets.images.equitiAvatarPlaceholderLight.svg(
                    height: 33,
                    width: 33,
                  ),
              const SizedBox(width: 5),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: l10n.trader_marketName,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textPrimary,
                    style: duploTextStyles.textSm,
                  ),
                  DuploText(
                    text: l10n.trader_marketClass,
                    fontWeight: DuploFontWeight.regular,
                    color: theme.text.textTertiary,
                    style: duploTextStyles.textSm,
                  ),
                ],
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              DuploText(
                text: l10n.trader_midPriceValue,
                fontWeight: DuploFontWeight.bold,
                color: theme.text.textTertiary,
                style: duploTextStyles.textMd,
              ),
              DuploText(
                text: l10n.trader_onedChange,
                fontWeight: DuploFontWeight.medium,
                color: theme.text.textQuaternary,
                style: duploTextStyles.textSm,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
