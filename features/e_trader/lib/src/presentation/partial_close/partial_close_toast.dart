import 'dart:async';
import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/position_header.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

FutureOr<void> showCloseTradeToast({
  required BuildContext context,
  required double lots,
  required String productIconUrl,
  required String productName,
  required double profit,
  required TradeType? tradeType,
  required DuploToast toast,
  String? titleMessage,
  String? currency,
}) {
  final theme = DuploTheme.of(context);
  if (!Platform.environment.containsKey('FLUTTER_TEST'))
    toast.showToastMessage(
      context: context,
      widget: DuploToastDecoratorWidget(
        statusColor:
            tradeType == TradeType.buy
                ? theme.foreground.fgSuccessPrimary
                : theme.foreground.fgErrorPrimary,
        messageType: ToastMessageType.success,
        titleMessage:
            titleMessage ??
            EquitiLocalization.of(context).trader_tradePartiallyClosed,
        onLeadingAction: () => toast.hidesToastMessage(),
        onTap: () => toast.hidesToastMessage(),
        contentWidget: Container(
          decoration: BoxDecoration(
            border: Border(
              left:
                  Directionality.of(context) == TextDirection.rtl
                      ? BorderSide.none
                      : BorderSide(
                        color:
                            tradeType == TradeType.buy
                                ? theme.foreground.fgSuccessPrimary
                                : theme.foreground.fgErrorPrimary,
                        width: 4,
                      ),
              right:
                  Directionality.of(context) == TextDirection.rtl
                      ? BorderSide(
                        color:
                            tradeType == TradeType.buy
                                ? theme.foreground.fgSuccessPrimary
                                : theme.foreground.fgErrorPrimary,
                        width: 4,
                      )
                      : BorderSide.none,
            ),
          ),
          child: PositionHeader(
            lots: lots,
            productIcon: productIconUrl,
            productName: productName,
            isHedging: false,
            profit: profit,
            tradeType: tradeType,
            currency: currency,
          ),
        ),
      ),
    );
}
