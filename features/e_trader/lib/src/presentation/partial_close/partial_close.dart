import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/input_order_size_widget.dart';
import 'package:e_trader/src/presentation/partial_close/bloc/partial_close_bloc.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

FutureOr<void> showPartialCloseSheet({
  required BuildContext context,
  required int positionId,
  required void Function({
    required double lots,
    required String productIconUrl,
    required String productName,
    required double profit,
    required TradeType? tradeType,
    String? titleMessage,
  })
  onSuccess,
}) {
  final theme = DuploTheme.of(context);
  final l10n = EquitiLocalization.of(context);
  DuploToast? toast;

  return DuploSheet.showModalSheetV2(
    context,
    appBar: DuploAppBar(
      title: l10n.trader_partialClose,
      automaticallyImplyLeading: false,
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    ),
    builder:
        (child) => BlocProvider(
          create:
              (_) =>
                  diContainer<PartialCloseBloc>()
                    ..add(PartialCloseEvent.onLoadPosition(positionId)),
          child: child,
        ),
    content: Container(
      color: DuploTheme.of(context).background.bgSecondary,
      child: BlocConsumer<PartialCloseBloc, PartialCloseState>(
        listenWhen:
            (previous, current) =>
                current.closeTradeProcessState !=
                previous.closeTradeProcessState,
        listener: (listenerContext, state) {
          if (state.closeTradeProcessState ==
              CloseTradeProcessState.success()) {
            final isFullyClosed =
                state.orderSizeState.value == state.position!.lotSize;
            onSuccess(
              lots: (state.orderSizeState.value),
              productIconUrl: state.position!.productLogoUrl,
              productName: state.position!.productName,
              profit: state.profit,
              tradeType: state.position!.positionType,
              titleMessage:
                  isFullyClosed
                      ? l10n.trader_tradeClosed
                      : l10n.trader_tradePartiallyClosed,
            );
            if (isFullyClosed) {
              Navigator.pop(context);
            }
            Navigator.pop(context);
          }
          if (state.closeTradeProcessState case CloseTradeErrorState(
            :final counter,
          )) {
            toast?.hidesToastMessage();
            toast = DuploToast();
            toast!.showToastMessage(
              context: context,
              autoCloseDuration: Duration.zero,
              widget: DuploTheme(
                data: DuploTheme.of(context),
                child: DuploTextStyles(
                  locale: Localizations.localeOf(context),
                  child: DuploToastMessage(
                    titleMessage:
                        counter == 1
                            ? l10n.trader_tradeNotClosed
                            : l10n.trader_contactSupportTeam,
                    descriptionMessage:
                        counter == 1
                            ? l10n.trader_placeholderText
                            : l10n.trader_contactSupportDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () {
                      toast?.hidesToastMessage();
                    },
                    primaryButtonTitle:
                        counter == 1 ? null : l10n.trader_raiseSupportTicket,
                    onTap: () {
                      toast?.hidesToastMessage(); // TODO: Raise support ticket
                    },
                  ),
                ),
              ),
            );
          } else if (state.closeTradeProcessState
              is CloseTradeMarketClosedState) {
            toast?.hidesToastMessage();
            toast = DuploToast();
            toast?.showToastMessage(
              context: context,
              widget: DuploTheme(
                data: DuploTheme.of(context),
                child: DuploTextStyles(
                  locale: Localizations.localeOf(context),
                  child: DuploToastMessage(
                    titleMessage: l10n.trader_marketIsClosed,
                    descriptionMessage:
                        l10n.trader_closeTrade_marketIsClosedDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast?.hidesToastMessage(),
                  ),
                ),
              ),
            );
          }
        },
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          return switch (state.processState) {
            PositionLoadingState() => Center(
              child: DuploShimmerListItem(height: 97),
            ),
            PositionSuccessState() => () {
              final position = state.position!;
              final locale = Localizations.localeOf(context).toString();
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: ListView(
                      children: [
                        TradeTile(
                          lots: position.lotSize,
                          profit: position.profit!,
                          tradeType: position.positionType,
                          tpValue: position.takeProfit,
                          slValue: position.stopLoss,
                          currentPrice: position.currentPrice,
                          priceChange:
                              position.positionType == TradeType.buy
                                  ? position.buyPercentage
                                  : position.sellPercentage,
                          productIcon: position.productLogoUrl,
                          productName: position.productName,
                          digits: position.digits,
                          currency: state.currency,
                        ),
                        Container(
                          margin: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.background.bgPrimary,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.border.borderSecondary,
                            ),
                          ),
                          padding: const EdgeInsets.all(16),
                          child: InputOrderSizeWidget(
                            args: (
                              minLot: position.minLot,
                              maxLot: position.lotSize,
                              initialOrderSize: position.lotSize,
                              isDisabled: false,
                            ),
                            onOrderSizeChanged:
                                (orderSizeState) =>
                                    builderContext.read<PartialCloseBloc>().add(
                                      PartialCloseEvent.orderSizeChanged(
                                        orderSizeState,
                                      ),
                                    ),
                            showBorder: false,
                            footer: DuploText.rich(
                              spans: [
                                DuploTextSpan(
                                  text: '${l10n.trader_profit}:',
                                  style: DuploTextStyles.of(context).textXs,
                                  color: theme.text.textPrimary.withValues(
                                    alpha: .7,
                                  ),
                                ),
                                DuploTextSpan(
                                  text:
                                      (state.profit > 0 ? "+" : '') +
                                      '${EquitiFormatter.decimalPatternDigits(value: state.profit, digits: position.digits, locale: locale)} ${state.currency ?? "USD"}',
                                  style: DuploTextStyles.of(context).textXs,
                                  color:
                                      state.profit < 0
                                          ? theme.text.textErrorPrimary
                                          : theme.text.textSuccessPrimary,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }(),
            _ => EmptyOrErrorStateComponent.defaultError(context, () {
              builderContext.read<PartialCloseBloc>().add(
                PartialCloseEvent.onLoadPosition(0),
              );
            }),
          };
        },
      ),
    ),
    bottomBar: BlocBuilder<PartialCloseBloc, PartialCloseState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return Padding(
          padding: EdgeInsets.all(16),
          child: DuploButton.defaultPrimary(
            semanticsIdentifier: "partial_close_button",
            isLoading: switch (state.closeTradeProcessState) {
              CloseTradeLoadingState() => true,
              _ => false,
            },
            title: switch (state.closeTradeProcessState) {
              CloseTradeLoadingState() => l10n.trader_closing,
              _ => l10n.trader_partialClose,
            },
            isDisabled: !state.isValid(),
            onTap:
                () => builderContext.read<PartialCloseBloc>().add(
                  const PartialCloseEvent.onCloseTrade(),
                ),
          ),
        );
      },
    ),
  );
}
