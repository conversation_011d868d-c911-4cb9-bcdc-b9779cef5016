import 'dart:async';

import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/usecase/calculate_partial_close_profit_use_case.dart';
import 'package:e_trader/src/domain/usecase/close_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/order_size_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'partial_close_bloc.freezed.dart';
part 'partial_close_event.dart';
part 'partial_close_state.dart';

class PartialCloseBloc extends Bloc<PartialCloseEvent, PartialCloseState>
    with DisposableMixin {
  PartialCloseBloc(
    this._subscribeToPositionsUseCase,
    this._getAccountNumberUseCase,
    this._closeTradeUseCase,
    this._calculatePartialCloseProfitUseCase,
    this._getSelectedAccountUseCase,
  ) : super(PartialCloseState()) {
    on<PartialCloseEvent>((event, emit) async {
      await switch (event) {
        _OnLoadPosition val => _onLoadPosition(val, emit),
        _OnCloseTrade() => _onCloseTrade(emit),
        _PartialCloseLotSizeChanged val => _onOrderSizeChanged(val, emit),
      };
    });
  }
  final SubscribeToPositionsUseCase _subscribeToPositionsUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final CloseTradeUseCase _closeTradeUseCase;
  final CalculatePartialCloseProfitUseCase _calculatePartialCloseProfitUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;

  Future<void> _onLoadPosition(
    _OnLoadPosition event,
    Emitter<PartialCloseState> emit,
  ) async {
    emit(state.copyWith(processState: const PositionProcessState.loading()));

    if (state.currency == null) {
      final accountCurrency =
          _getSelectedAccountUseCase()?.homeCurrency ?? "USD";
      emit(state.copyWith(currency: accountCurrency));
    }
    final result =
        await _subscribeToPositionsUseCase(
          positionId: event.positionId,
          subscriberId: '${PartialCloseBloc}_$hashCode',
          eventType: TradingSocketEvent.positions.subscribe,
        ).run();
    await result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(processState: PositionProcessState.error()));
      },
      (stream) async {
        await emit.forEach(
          stream,
          onData: (positionResponse) {
            if (positionResponse != null) {
              return state.copyWith(
                position: positionResponse.position,
                processState: const PositionProcessState.success(),
                profit: _calculatePartialCloseProfitUseCase(
                  totalLotSize: positionResponse.position.lotSize,
                  partialLotSize: positionResponse.position.lotSize,
                  totalFloatingProfit: positionResponse.position.profit ?? 0,
                ),
              );
            } else
              return state;
          },
          onError:
              (error, stackTrace) =>
                  state.copyWith(processState: PositionProcessState.error()),
        );
      },
    );
  }

  Future<void> _onCloseTrade(Emitter<PartialCloseState> emit) async {
    emit(
      state.copyWith(
        closeTradeProcessState: const CloseTradeProcessState.loading(),
      ),
    );
    final result =
        await _getAccountNumberUseCase().toTaskEither().flatMap((
          accountNumber,
        ) {
          final request = CloseTradeRequestModel(
            accountNumber: accountNumber,
            positions: [
              ClosePositionItemModel(
                id: state.position!.positionId,
                volume: (state.orderSizeState.value * 10000).toInt(),
              ),
            ],
          );
          return _closeTradeUseCase(request);
        }).run();
    result.fold(
      (exception) {
        if (!emit.isDone) {
          if (exception is PositionsAndOrdersException &&
              exception.errors.containsKey("IsMarketOpen")) {
            final isMarketOpenError =
                exception.errors["IsMarketOpen"]?.firstOrNull;
            if (isMarketOpenError?.toLowerCase() == "false") {
              emit(
                state.copyWith(
                  closeTradeProcessState: CloseTradeProcessState.marketClosed(),
                ),
              );
              return;
            }
          } else
            emit(
              state.copyWith(
                closeTradeProcessState: CloseTradeProcessState.error(
                  state.errorCounter + 1,
                ),
                errorCounter: state.errorCounter + 1,
              ),
            );
        }
        addError(exception);
      },
      (success) {
        if (!emit.isDone) {
          (success?.success ?? false)
              ? emit(
                state.copyWith(
                  closeTradeProcessState: CloseTradeProcessState.success(),
                  errorCounter: 0,
                ),
              )
              : emit(
                state.copyWith(
                  closeTradeProcessState: CloseTradeProcessState.error(
                    state.errorCounter + 1,
                  ),
                  errorCounter: state.errorCounter + 1,
                ),
              );
        }
      },
    );
  }

  FutureOr<void> _onOrderSizeChanged(
    _PartialCloseLotSizeChanged val,
    Emitter<PartialCloseState> emit,
  ) {
    emit(
      state.copyWith(
        orderSizeState: val.state,
        profit:
            val.state.isValid()
                ? _calculatePartialCloseProfitUseCase(
                  totalLotSize: state.position!.lotSize,
                  partialLotSize: val.state.value,
                  totalFloatingProfit: state.position!.profit ?? 0,
                )
                : 0,
      ),
    );
  }
}
