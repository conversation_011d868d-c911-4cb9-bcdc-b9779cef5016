import 'package:decimal/decimal.dart';
import 'package:e_trader/src/domain/model/method_type.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';

extension PriceMethodCalculation on PriceMethod {
  Decimal getDistance({required Decimal limitPrice}) {
    final multiplier =
        tradeType == TradeType.buy ? Decimal.parse('1') : Decimal.parse('-1');

    final orderMultiplier =
        orderLimitType == OrderLimitType.stopLoss
            ? Decimal.parse('-1')
            : Decimal.parse('1');

    final priceDiff = limitPrice - currentPrice;
    final result = priceDiff * pipMultiplier * multiplier * orderMultiplier;
    return result;
  }

  Decimal getPrice({required Decimal limitPrice, required Decimal distance}) {
    final multiplier =
        (orderLimitType == OrderLimitType.stopLoss) ==
                (tradeType == TradeType.buy)
            ? Decimal.parse('-1')
            : Decimal.parse('1');
    return (limitPrice + (distance * pipSize * multiplier));
  }

  Decimal getProfitOrLoss({required Decimal distance}) {
    Decimal result = distance * pipValue;
    if (orderLimitType == OrderLimitType.stopLoss) {
      result = result * Decimal.parse('-1');
    }
    return result;
  }

  OrderLimitErrorCode? isValid({required Decimal limitPrice}) {
    switch (tradeType) {
      case TradeType.buy:
        switch (orderLimitType) {
          case OrderLimitType.takeProfit:
            return limitPrice > currentPrice
                ? null
                : OrderLimitErrorCode.invalidPrice;

          case OrderLimitType.stopLoss:
            return limitPrice < currentPrice
                ? null
                : OrderLimitErrorCode.invalidPrice;
        }
      case TradeType.sell:
        switch (orderLimitType) {
          case OrderLimitType.takeProfit:
            return limitPrice < currentPrice
                ? null
                : OrderLimitErrorCode.invalidPrice;
          case OrderLimitType.stopLoss:
            return limitPrice > currentPrice
                ? null
                : OrderLimitErrorCode.invalidPrice;
        }
    }
  }
}
