import 'package:decimal/decimal.dart';
import 'package:e_trader/src/domain/model/method_type.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';

extension ProfitOrLossCalculation on ProfitOrLossMethod {
  Decimal getDistance({required Decimal profitOrLoss}) {
    final result =
        orderLimitType == OrderLimitType.takeProfit
            ? Decimal.parse((profitOrLoss / pipValue).toString())
            : (Decimal.parse((profitOrLoss / pipValue).toString())) *
                Decimal.parse('-1');
    return result;
  }

  Decimal getPrice({required Decimal limitPrice, required Decimal distance}) {
    final multiplier =
        orderLimitType == OrderLimitType.takeProfit
            ? tradeType == TradeType.buy
                ? Decimal.parse('1')
                : Decimal.parse('-1')
            : tradeType == TradeType.buy
            ? Decimal.parse('-1')
            : Decimal.parse('1');
    return limitPrice + (distance * pipSize * multiplier);
  }

  OrderLimitErrorCode? isValid({required Decimal profitOrLoss}) {
    final shouldBePositive = orderLimitType == OrderLimitType.takeProfit;
    return shouldBePositive == (profitOrLoss > Decimal.parse('0'))
        ? null
        : OrderLimitErrorCode.invalidProfitOrLoss;
  }
}
