// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'country_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CountryModel _$CountryModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_CountryModel', json, ($checkedConvert) {
      final val = _CountryModel(
        status: $checkedConvert('status', (v) => v as String?),
        data: $checkedConvert(
          'data',
          (v) => v == null ? null : Data.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$CountryModelToJson(_CountryModel instance) =>
    <String, dynamic>{
      if (instance.status case final value?) 'status': value,
      if (instance.data?.toJson() case final value?) 'data': value,
    };

_Data _$DataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Data', json, ($checkedConvert) {
      final val = _Data(
        countries: $checkedConvert(
          'countries',
          (v) =>
              (v as List<dynamic>?)
                  ?.map((e) => Country.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$DataToJson(_Data instance) => <String, dynamic>{
  if (instance.countries?.map((e) => e.toJson()).toList() case final value?)
    'countries': value,
};

_Country _$CountryFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Country', json, ($checkedConvert) {
      final val = _Country(
        code: $checkedConvert('code', (v) => v as String?),
        name: $checkedConvert('name', (v) => v as String?),
        image: $checkedConvert('image', (v) => v as String?),
      );
      return val;
    });

Map<String, dynamic> _$CountryToJson(_Country instance) => <String, dynamic>{
  if (instance.code case final value?) 'code': value,
  if (instance.name case final value?) 'name': value,
  if (instance.image case final value?) 'image': value,
};
