import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/model/price_direction.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'grouped_positions.freezed.dart';

@freezed
abstract class GroupedPositions with _$GroupedPositions {
  const GroupedPositions._();
  factory GroupedPositions({
    required String symbol,
    required String url,
    @Default([]) List<PositionModel> positions,
  }) = _GroupedPositions;

  double _calculateTotalLotSize(List<PositionModel> positions) {
    int buyVolume = 0;
    int sellVolume = 0;

    for (var position in positions) {
      switch (position.positionType) {
        case TradeType.buy:
          buyVolume += position.volume;
        case TradeType.sell:
          sellVolume += position.volume;
      }
    }

    final buyLotSize = buyVolume / 10000;
    final sellLotSize = sellVolume / 10000;

    return buyLotSize > sellLotSize
        ? buyLotSize - sellLotSize
        : sellLotSize - buyLotSize;
  }

  double get totalMargin {
    double total = 0;
    for (final position in positions) {
      total += position.margin;
    }
    return total;
  }

  double get totalProfit {
    double total = 0;
    for (final position in positions) {
      total += position.profit!;
    }
    return total;
  }

  TradeType? _checkGroupTradeType(List<PositionModel> positions) {
    int buyVolume = 0;
    int sellVolume = 0;

    for (var position in positions) {
      switch (position.positionType) {
        case TradeType.buy:
          buyVolume += position.volume;
        case TradeType.sell:
          sellVolume += position.volume;
      }
    }

    if (buyVolume > sellVolume) {
      return TradeType.buy;
    } else if (sellVolume > buyVolume) {
      return TradeType.sell;
    }
    return null;
  }

  double get totalLotSize => _calculateTotalLotSize(positions);
  TradeType? get groupTradeType => _checkGroupTradeType(positions);

  double get price {
    double total = 0;
    for (final position in positions) {
      total += position.profit!;
    }
    return total;
  }

  PriceDirection get direction {
    if (positions.isEmpty) return PriceDirection.noChange;

    int buyCount = 0;
    int sellCount = 0;

    for (var position in positions) {
      switch (position.positionType) {
        case TradeType.buy:
          buyCount++;
        case TradeType.sell:
          sellCount++;
      }
    }

    if (buyCount > sellCount) return PriceDirection.up;
    if (sellCount > buyCount) return PriceDirection.down;
    return PriceDirection.noChange;
  }

  bool get isHedged {
    if (positions.isEmpty) return false;

    int buyVolume = 0;
    int sellVolume = 0;
    bool hasBuyPositions = false;
    bool hasSellPositions = false;

    for (var position in positions) {
      switch (position.positionType) {
        case TradeType.buy:
          buyVolume += position.volume;
          hasBuyPositions = true;
        case TradeType.sell:
          sellVolume += position.volume;
          hasSellPositions = true;
      }
    }

    return hasBuyPositions &&
        hasSellPositions &&
        buyVolume == sellVolume &&
        buyVolume > 0;
  }

  List<PositionModel> get buyTrades =>
      positions
          .where((position) => position.positionType == TradeType.buy)
          .toList();
  List<PositionModel> get sellTrades =>
      positions
          .where((position) => position.positionType == TradeType.sell)
          .toList();
  List<PositionModel> get losingTrades =>
      positions.where((position) => (position.netProfit) < 0).toList();
  List<PositionModel> get winningTrades =>
      positions.where((position) => (position.netProfit) > 0).toList();

  double get totalBuyLotSize => _calculateTotalLotSize(buyTrades);
  double get totalSellLotSize => _calculateTotalLotSize(sellTrades);
  double get totalWinningLotSize => _calculateTotalLotSize(winningTrades);
  double get totalLosingLotSize => _calculateTotalLotSize(losingTrades);
  double get totalBuyPrice =>
      buyTrades.fold(0, (prev, element) => prev + (element.profit ?? 0));
  double get totalSellPrice =>
      sellTrades.fold(0, (prev, element) => prev + (element.profit ?? 0));
  double get totalWinningPrice =>
      winningTrades.fold(0, (prev, element) => prev + (element.profit ?? 0));
  double get totalLosingPrice =>
      losingTrades.fold(0, (prev, element) => prev + (element.profit ?? 0));
}
