import 'package:e_trader/src/domain/validators/trade_validators.dart';
import 'package:validator/validator.dart';

class ValidateMarginAllocationUseCase {
  const ValidateMarginAllocationUseCase(this.inputValidator);

  final InputValidator inputValidator;

  bool call(double marginAllocation) {
    final errors =
        inputValidator
            .validate(marginAllocation)
            .addValidator(marginAllocationValidator())
            .run();
    return errors.isEmpty;
  }
}
