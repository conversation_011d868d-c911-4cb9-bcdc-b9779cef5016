import 'package:e_trader/src/domain/model/legal_document.dart';
import 'package:e_trader/src/domain/repository/legal_document_repository.dart';
import 'package:e_trader/src/domain/usecase/get_selected_language_code_use_case.dart';
import 'package:prelude/prelude.dart';

class GetLegalDocumentsUseCase {
  const GetLegalDocumentsUseCase(
    this.legalDocumentRepository,
    this.getSelectedLanguageCodeUseCase,
  );

  final LegalDocumentRepository legalDocumentRepository;
  final GetSelectedLanguageCodeUseCase getSelectedLanguageCodeUseCase;

  TaskEither<Exception, Map<String, LegalDocument>> call() =>
      legalDocumentRepository.getLegalDocuments().flatMap(
        (legalDocumentsModel) => legalDocumentRepository
            .getLegalDocumentContents()
            .map((legalDocumentContentsModel) {
              final Map<String, LegalDocument> mappedDocumentsList = {};

              for (final document
                  in legalDocumentContentsModel.legalDocumentContents) {
                final String? title =
                    getSelectedLanguageCodeUseCase() == 'en'
                        ? document.docData.en
                        : document.docData.ar;
                ;

                for (final legalDocument
                    in legalDocumentsModel.legalDocuments) {
                  if (legalDocument.type == document.id) {
                    final documentTitle =
                        title ?? _mapKeyToTitle(legalDocument.type ?? "");
                    mappedDocumentsList[documentTitle!] = legalDocument;
                    break;
                  }
                }
              }
              return mappedDocumentsList;
            }),
      );

  String? _mapKeyToTitle(String key) {
    const titleMap = {
      'ClientsAgreement': 'Clients Agreement',
      'TermsOfUse': 'Terms of Use',
      'WebsiteAcceptableUsePolicy': 'Website Acceptable Use Policy',
      'ClientComplaintsHandlingProcedure':
          'Client Complaints Handling Procedure',
      'ConflictsOfInterest': 'Conflicts of Interest Policy',
      'CookiesPolicy': 'Cookies Policy',
      'OrderExecutionPolicy': 'Order Execution Policy',
      'PrivacyPolicy': 'Privacy Policy',
      'RiskWarningDisclosure': 'Risk Warning Disclosure',
      'GuidanceNewsletter': 'Guidance Newsletter',
      'IBAgreement': 'IB Agreement',
      'MAMManual': 'MAM Manual',
      'Regulations': 'Regulations',
      'SwapFreeTermsConditions': 'Swap-Free Terms and Conditions',
      'IBManual': 'Partner Portal Manual',
      'TermsConditions': 'Terms and Conditions',
      'ProductSensitisation': 'Product Sensitisation',
      'PaymentsTermsAndConditions': 'Payments Terms And Conditions',
      'JSCGuidanceNewsletter': 'JSC Guidance Newsletter',
      'JscLeverageAgreement': 'Jsc Leverage Agreement',
      'NSETermsAndConditions': 'NSE Terms And Conditions',
    };

    final languageCode = getSelectedLanguageCodeUseCase();

    return languageCode == 'en' ? titleMap[key] : "";
  }
}
