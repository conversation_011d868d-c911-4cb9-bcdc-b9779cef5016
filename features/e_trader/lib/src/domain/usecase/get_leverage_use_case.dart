import 'package:e_trader/src/domain/repository/broker_settings_repository.dart';
import 'package:e_trader/src/domain/repository/change_leverage_repository.dart';
import 'package:e_trader/src/domain/usecase/get_office_code_use_case.dart';
import 'package:prelude/prelude.dart';

class GetLeverageUseCase {
  final ChangeLeverageRepository changeLeverageRepository;
  final BrokerSettingsRepository brokerSettingsRepository;
  final GetOfficeCodeUseCase getOfficeCodeUseCase;

  const GetLeverageUseCase({
    required this.changeLeverageRepository,
    required this.brokerSettingsRepository,
    required this.getOfficeCodeUseCase,
  });

  TaskEither<Exception, List<int>> call({required String accountNumber}) {
    return brokerSettingsRepository
        .getUserBrokerSettings(
          accountNumber: accountNumber,
          officeCode: getOfficeCodeUseCase() ?? "AE",
        )
        .mapLeft((error) => Exception('Failed to get leverage: $error'))
        .flatMap((response) {
          final leverages = response.leverages;

          if (leverages.isEmpty) {
            return TaskEither.left(Exception('No leverages available'));
          }

          final dulcimerLeverages =
              leverages
                  .firstWhere(
                    (data) => data.name == 'Dulcimer',
                    orElse:
                        () => throw Exception('Dulcimer leverage not found'),
                  )
                  .leverages;

          return TaskEither.right(dulcimerLeverages.reversed.toList());
        });
  }
}
