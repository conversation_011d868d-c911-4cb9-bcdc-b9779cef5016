import 'dart:collection';

import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/data/socket/position_response.dart';
import 'package:e_trader/src/domain/model/grouped_positions.dart';
import 'package:e_trader/src/domain/repository/position_repository.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class SubscribeToGroupedPositionsUseCase {
  final PositionRepository positionRepository;

  const SubscribeToGroupedPositionsUseCase(this.positionRepository);

  TaskEither<Exception, Stream<List<GroupedPositions>>> call({
    required String accountNumber,
    int positionId = 0,
    required String subscriberId,
    required EventType eventType,
  }) {
    final positionsById = <String, PositionResponse>{};

    return positionRepository
        .subscribeToPositionProductInfo(
          accountNumber: accountNumber,
          positionId: positionId,
          subscriberId: subscriberId,
          eventType: eventType,
        )
        .map((positionStream) {
          return positionStream.map((response) {
            if (response == null) {
              return [];
            }
            positionsById[response.position.positionId] = response;
            final groupedMap = positionsById.values
                .where(
                  (positionResponse) =>
                      positionResponse.target != 'PositionDeleted',
                )
                .map((positionResponse) => positionResponse.position)
                .groupBy((p) => p.symbol);

            return groupedMap.entries
                .map(
                  (entry) => GroupedPositions(
                    symbol: entry.key,
                    url: entry.value.firstOrNull?.productLogoUrl ?? '',
                    positions:
                        SplayTreeMap<String, PositionModel>.fromIterable(
                          entry.value,
                          key: (k) => (k as PositionModel).positionId,
                          value: (v) => (v as PositionModel),
                        ).values.toList(),
                  ),
                )
                .toList();
          });
        });
  }
}
