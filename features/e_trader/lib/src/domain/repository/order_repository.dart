import 'package:api_client/api_client.dart';
import 'package:dio/dio.dart';
import 'package:e_trader/src/data/api/create_order_model.dart';
import 'package:e_trader/src/data/api/create_order_request_model.dart';
import 'package:e_trader/src/data/api/modify_order_model.dart';
import 'package:e_trader/src/data/socket/order_response.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class OrderRepository {
  final ApiClientBase apiClient;
  final SocketClient socketClient;
  const OrderRepository({required this.apiClient, required this.socketClient});

  TaskEither<Exception, CreateOrderModel> createOrder(
    CreateOrderRequestModel createOrderRequestModel,
  ) => apiClient
      .post<Map<String, dynamic>>(
        '/api/Order',
        data: createOrderRequestModel.toJson(),
      )
      .mapLeft((error) {
        if (error is ClientException) {
          final dioError = error.cause as DioException;
          final responseData = dioError.response?.data as Map<String, dynamic>;
          return PositionsAndOrdersException.fromJson(responseData);
        }
        return error;
      })
      .flatMap((response) {
        return TaskEither.tryCatch(
          () async => CreateOrderModel.fromJson(response.data!),
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        );
      });

  TaskEither<Exception, bool> modifyOrder(
    ModifyOrderModel modifyOrderModel,
  ) => apiClient
      .put<Map<String, dynamic>>('/api/Order', data: modifyOrderModel.toJson())
      .mapLeft((error) {
        if (error is ClientException) {
          final dioError = error.cause as DioException;
          final responseData = dioError.response?.data as Map<String, dynamic>;
          return PositionsAndOrdersException.fromJson(responseData);
        }
        return error;
      })
      .flatMap((response) {
        return TaskEither.tryCatch(
          () async {
            return response.data!['success'] as bool;
          },
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        );
      });

  TaskEither<Exception, bool> deleteOrder(
    String accountNumber,
    String orderId,
  ) => apiClient
      .delete<Map<String, dynamic>>(
        '/api/Order',
        data: {"accountNumber": accountNumber, "orderId": orderId},
      )
      .mapLeft((error) {
        if (error is ClientException) {
          final dioError = error.cause as DioException;
          final responseData = dioError.response?.data as Map<String, dynamic>;
          return PositionsAndOrdersException.fromJson(responseData);
        }
        return error;
      })
      .flatMap((response) {
        return TaskEither.tryCatch(
          () async {
            return response.data!['success'] as bool;
          },
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        );
      });

  TaskEither<Exception, List<bool>> deleteBulkOrders(
    String accountNumber,
    List<String> orderIds,
  ) => apiClient
      .delete<List<Object?>>(
        'api/Order/bulk',
        data: {"accountNumber": accountNumber, "orderIds": orderIds},
      )
      .mapLeft((error) {
        if (error is ClientException) {
          final dioError = error.cause as DioException;
          final responseData = dioError.response?.data as Map<String, dynamic>;
          return PositionsAndOrdersException.fromJson(responseData);
        }
        return error;
      })
      .flatMap((response) {
        return TaskEither.tryCatch(
          () async {
            final results =
                response.data!
                    .map(
                      (item) =>
                          (item as Map<String, dynamic>)['success'] as bool,
                    )
                    .toList();
            return results;
          },
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        );
      });

  TaskEither<Exception, Stream<OrderResponse?>> subscribeToOrderHub({
    required String accountNumber,
    required EventType eventType,
    required String subscriberId,
    String symbolName = '',
  }) => socketClient
      .subscribe(
        path: 'orderHub',
        eventType: eventType,
        targets: ["OrderUpdated", "OrderAdded", "OrderDeleted"],
        args: [accountNumber, symbolName],
        subscriberId: subscriberId,
      )
      .flatMap((result) {
        return TaskEither.tryCatch(
          () async => result.map((data) {
            if (data == null) {
              return null;
            }
            final arguments = data as Map<String, dynamic>;
            return OrderResponse.fromJson(arguments);
          }),
          (error, stackTrace) => Exception(error),
        );
      });

  Future<void> updateOrderHub({
    required String accountNumber,
    required EventType eventType,
    String symbolName = '',
  }) => socketClient.updateSubscription(
    path: 'orderHub',
    eventType: eventType,
    targets:
        eventType is UnsubscribeEvent
            ? []
            : ["OrderUpdated", "OrderAdded", "OrderDeleted"],
    args: [accountNumber, if (eventType is! UnsubscribeEvent) symbolName],
  );
}
