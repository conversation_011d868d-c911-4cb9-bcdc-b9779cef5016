import 'package:api_client/api_client.dart';
import 'package:e_trader/src/data/api/change_account_password_request_model.dart';
import 'package:e_trader/src/data/api/linked_symbol_model.dart';
import 'package:e_trader/src/data/api/linked_symbols_request_model.dart';
import 'package:e_trader/src/data/api/symbol_model.dart';
import 'package:preferences/preferences.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class AccountRepository {
  final ApiClientBase apiClientBase;
  final SocketClient socketClient;
  final EquitiPreferences preferences;

  const AccountRepository(
    this.apiClientBase,
    this.socketClient,
    this.preferences,
  );

  TaskEither<Exception, LinkedSymbolModel> getLinkedSymbols({
    required LinkedSymbolsRequestModel linkedRequestModel,
  }) {
    return apiClientBase
        .get<Map<String, dynamic>>(
          "api/Account/linkedSymbols",
          queryParams: linkedRequestModel.toJson(),
        )
        .flatMap((response) {
          return TaskEither.tryCatch(() async {
            return convertJsonToModel(response.data);
          }, (e, s) => Exception(e.toString()));
        });
  }

  LinkedSymbolModel convertJsonToModel(Map<String, dynamic>? data) {
    final count = (data as Map)["data"]["count"] as int;
    final symbols =
        ((data)?["data"]["list"] as List)
            .map<SymbolModel>(
              (e) => SymbolModel.fromJson(e as Map<String, dynamic>),
            )
            .toList();
    return LinkedSymbolModel(count: count, symbols: symbols);
  }

  TaskEither<Exception, bool> saveAccountPreferences({
    required String accountNumber,
    required bool isDemo,
  }) {
    return TaskEither.tryCatch(() async {
      await preferences.setValue('accountNumber', accountNumber);
      await preferences.setValue('isDemo', isDemo);
      return true;
    }, (e, s) => Exception(e.toString()));
  }

  TaskEither<Exception, bool> changeAccountPassword({
    required ChangeAccountPasswordRequestModel changePasswordRequestModel,
    String? accountNumber,
  }) {
    return apiClientBase
        .put<Map<String, dynamic>>(
          "/middlewareapi/TradingAccount/change-password/${accountNumber ?? ''}",
          data: changePasswordRequestModel.toJson(),
        )
        .flatMap((response) {
          return TaskEither.tryCatch(() async {
            // Check if the response indicates success
            return response.statusCode == 200;
          }, (e, s) => Exception(e.toString()));
        });
  }
}
