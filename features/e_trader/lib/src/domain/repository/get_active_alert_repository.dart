import 'package:e_trader/src/data/socket/active_alert_response.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class GetActiveAlertRepository {
  SocketClient socketClient;
  GetActiveAlertRepository({required this.socketClient});

  TaskEither<Exception, Stream<ActiveAlertResponse?>> subscribeToAlerts({
    required String? symbol,
    required String accountNumber,
    required EventType eventType,
    required String subscriberId,
  }) {
    return socketClient
        .subscribe(
          path: 'priceAlertHub',
          eventType: eventType,
          subscriberId: subscriberId,
          targets: [
            "priceAlertUpdated",
            "priceAlertAdded",
            "priceAlertDeleted",
            "priceAlertTriggered",
          ],
          args: [
            {"symbolCode": symbol ?? "", "accountNumber": accountNumber},
          ],
        )
        .flatMap((result) {
          return TaskEither.tryCatch(
            () async => result.map((data) {
              if (data == null) {
                return null;
              }
              final response = data as Map<String, dynamic>;
              final positionResponse = ActiveAlertResponse.fromJson(response);

              return positionResponse;
            }),
            (error, stackTrace) => Exception(error),
          );
        });
  }

  Future<void> updateActiveAlerts({
    required String? symbol,
    required String accountNumber,
    required EventType eventType,
  }) {
    return socketClient.updateSubscription(
      path: 'priceAlertHub',
      eventType: eventType,
      targets: [
        "priceAlertUpdated",
        "priceAlertAdded",
        "priceAlertDeleted",
        "priceAlertTriggered",
      ],
      args:
          eventType is UnsubscribeEvent
              ? [accountNumber]
              : [
                {"symbolCode": symbol ?? "", "accountNumber": accountNumber},
              ],
    );
  }
}
