import 'package:api_client/api_client.dart';
import 'package:e_trader/src/data/api/historical_volume_model.dart';
import 'package:e_trader/src/data/api/historical_equity_profit_model.dart';
import 'package:prelude/prelude.dart';

class HistoricalRepository {
  final ApiClientBase apiClientBase;

  const HistoricalRepository(this.apiClientBase);

  TaskEither<Exception, List<HistoricalVolumeModel>> getHistoricalVolume() {
    return apiClientBase
        .get<List<Object?>>(
          "middlewareapi/TradingAccount/historical-volume/USD",
        )
        .flatMap(
          (response) => TaskEither.tryCatch(
            () async {
              return (response.data as List)
                  .map(
                    (e) => HistoricalVolumeModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList();
            },
            (e, s) {
              return Exception(e);
            },
          ),
        );
  }

  TaskEither<Exception, List<HistoricalEquityProfitModel>>
  getHistoricalProfit() {
    return apiClientBase
        .get<List<Object?>>(
          "middlewareapi/TradingAccount/historical-profit/USD",
        )
        .flatMap(
          (response) => TaskEither.tryCatch(
            () async =>
                (response.data as List)
                    .map(
                      (e) => HistoricalEquityProfitModel.fromJson(
                        e as Map<String, dynamic>,
                      ),
                    )
                    .toList(),
            (e, s) => Exception(e),
          ),
        );
  }
}
