// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statements_activity_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StatementsActivityResponse _$StatementsActivityResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_StatementsActivityResponse', json, ($checkedConvert) {
  final val = _StatementsActivityResponse(
    totalCount: $checkedConvert('totalCount', (v) => (v as num?)?.toInt()),
    statements: $checkedConvert(
      'statements',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => StatementItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$StatementsActivityResponseToJson(
  _StatementsActivityResponse instance,
) => <String, dynamic>{
  if (instance.totalCount case final value?) 'totalCount': value,
  if (instance.statements?.map((e) => e.toJson()).toList() case final value?)
    'statements': value,
};

_StatementItem _$StatementItemFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_StatementItem', json, ($checkedConvert) {
      final val = _StatementItem(
        dateTime: $checkedConvert(
          'dateTime',
          (v) => _dateTimeFromJson(v as String?),
        ),
        isDaily: $checkedConvert('isDaily', (v) => v as bool?),
        statementUrl: $checkedConvert('statementUrl', (v) => v as String?),
      );
      return val;
    });

Map<String, dynamic> _$StatementItemToJson(
  _StatementItem instance,
) => <String, dynamic>{
  if (instance.dateTime?.toIso8601String() case final value?) 'dateTime': value,
  if (instance.isDaily case final value?) 'isDaily': value,
  if (instance.statementUrl case final value?) 'statementUrl': value,
};
