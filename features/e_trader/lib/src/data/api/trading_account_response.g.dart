// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trading_account_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TradingAccountResponse _$TradingAccountResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_TradingAccountResponse', json, ($checkedConvert) {
  final val = _TradingAccountResponse(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) =>
          v == null
              ? null
              : TradingAccountData.fromJson(v as Map<String, dynamic>),
    ),
    error: $checkedConvert(
      'error',
      (v) =>
          v == null
              ? null
              : TradingAccountError.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$TradingAccountResponseToJson(
  _TradingAccountResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  if (instance.data?.toJson() case final value?) 'data': value,
  if (instance.error?.toJson() case final value?) 'error': value,
};

_TradingAccountData _$TradingAccountDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_TradingAccountData', json, ($checkedConvert) {
      final val = _TradingAccountData(
        accounts: $checkedConvert(
          'accounts',
          (v) =>
              (v as List<dynamic>)
                  .map(
                    (e) =>
                        TradingAccountModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$TradingAccountDataToJson(_TradingAccountData instance) =>
    <String, dynamic>{
      'accounts': instance.accounts.map((e) => e.toJson()).toList(),
    };

_TradingAccountError _$TradingAccountErrorFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_TradingAccountError', json, ($checkedConvert) {
      final val = _TradingAccountError(
        code: $checkedConvert('code', (v) => v as String?),
        description: $checkedConvert('description', (v) => v as String?),
      );
      return val;
    });

Map<String, dynamic> _$TradingAccountErrorToJson(
  _TradingAccountError instance,
) => <String, dynamic>{
  if (instance.code case final value?) 'code': value,
  if (instance.description case final value?) 'description': value,
};
