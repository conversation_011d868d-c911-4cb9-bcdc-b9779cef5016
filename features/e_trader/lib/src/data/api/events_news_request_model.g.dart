// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'events_news_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EventsNewsRequestModel _$EventsNewsRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_EventsNewsRequestModel', json, ($checkedConvert) {
  final val = _EventsNewsRequestModel(
    start: $checkedConvert('start', (v) => (v as num?)?.toInt() ?? 0),
    length: $checkedConvert('length', (v) => (v as num?)?.toInt() ?? 10),
    order: $checkedConvert(
      'order',
      (v) =>
          v == null
              ? const EventsSearchOrder()
              : EventsSearchOrder.fromJson(v as Map<String, dynamic>),
    ),
    search: $checkedConvert(
      'search',
      (v) =>
          v == null
              ? const EventsSearchCriteria()
              : EventsSearchCriteria.fromJson(v as Map<String, dynamic>),
    ),
    filters: $checkedConvert(
      'filters',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) => EventsSearchFilter.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
    ),
  );
  return val;
});

Map<String, dynamic> _$EventsNewsRequestModelToJson(
  _EventsNewsRequestModel instance,
) => <String, dynamic>{
  'start': instance.start,
  'length': instance.length,
  'order': instance.order.toJson(),
  'search': instance.search.toJson(),
  'filters': instance.filters.map((e) => e.toJson()).toList(),
};

_EventsSearchOrder _$EventsSearchOrderFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_EventsSearchOrder', json, ($checkedConvert) {
      final val = _EventsSearchOrder(
        name: $checkedConvert('name', (v) => v as String? ?? ''),
        dir: $checkedConvert('dir', (v) => v as String? ?? ''),
      );
      return val;
    });

Map<String, dynamic> _$EventsSearchOrderToJson(_EventsSearchOrder instance) =>
    <String, dynamic>{'name': instance.name, 'dir': instance.dir};

_EventsSearchCriteria _$EventsSearchCriteriaFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_EventsSearchCriteria', json, ($checkedConvert) {
  final val = _EventsSearchCriteria(
    value: $checkedConvert('value', (v) => v as String? ?? ''),
    matchingType: $checkedConvert(
      'matchingType',
      (v) => (v as num?)?.toInt() ?? 1,
    ),
  );
  return val;
});

Map<String, dynamic> _$EventsSearchCriteriaToJson(
  _EventsSearchCriteria instance,
) => <String, dynamic>{
  'value': instance.value,
  'matchingType': instance.matchingType,
};

_EventsSearchFilter _$EventsSearchFilterFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_EventsSearchFilter', json, ($checkedConvert) {
      final val = _EventsSearchFilter(
        field: $checkedConvert('field', (v) => v as String? ?? ''),
        value: $checkedConvert('value', (v) => v as String? ?? ''),
      );
      return val;
    });

Map<String, dynamic> _$EventsSearchFilterToJson(_EventsSearchFilter instance) =>
    <String, dynamic>{'field': instance.field, 'value': instance.value};
