// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawModel _$WithdrawModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_WithdrawModel', json, ($checkedConvert) {
      final val = _WithdrawModel(
        withdrawalTypes: $checkedConvert(
          'withdrawalTypes',
          (v) =>
              (v as List<dynamic>)
                  .map(
                    (e) => WithdrawalTypes.fromJson(e as Map<String, dynamic>),
                  )
                  .toList(),
        ),
        countryName: $checkedConvert('countryName', (v) => v as String),
      );
      return val;
    });

Map<String, dynamic> _$WithdrawModelToJson(
  _WithdrawModel instance,
) => <String, dynamic>{
  'withdrawalTypes': instance.withdrawalTypes.map((e) => e.toJson()).toList(),
  'countryName': instance.countryName,
};

Skrill _$SkrillFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Skrill', json, ($checkedConvert) {
      final val = Skrill(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$SkrillToJson(Skrill instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

Neteller _$NetellerFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Neteller', json, ($checkedConvert) {
      final val = Neteller(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$NetellerToJson(Neteller instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

Bank _$BankFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Bank', json, ($checkedConvert) {
      final val = Bank(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$BankToJson(Bank instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

Dinarak _$DinarakFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Dinarak', json, ($checkedConvert) {
      final val = Dinarak(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$DinarakToJson(Dinarak instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

Gate2Pay _$Gate2PayFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Gate2Pay', json, ($checkedConvert) {
      final val = Gate2Pay(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$Gate2PayToJson(Gate2Pay instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

SafeCharge _$SafeChargeFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SafeCharge', json, ($checkedConvert) {
      final val = SafeCharge(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$SafeChargeToJson(SafeCharge instance) =>
    <String, dynamic>{'url': instance.url, 'name': instance.$type};

Selcom _$SelcomFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Selcom', json, ($checkedConvert) {
      final val = Selcom(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$SelcomToJson(Selcom instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

ELipaUG _$ELipaUGFromJson(Map<String, dynamic> json) =>
    $checkedCreate('ELipaUG', json, ($checkedConvert) {
      final val = ELipaUG(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$ELipaUGToJson(ELipaUG instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

VaultsPay _$VaultsPayFromJson(Map<String, dynamic> json) =>
    $checkedCreate('VaultsPay', json, ($checkedConvert) {
      final val = VaultsPay(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$VaultsPayToJson(VaultsPay instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

InstantBanking _$InstantBankingFromJson(Map<String, dynamic> json) =>
    $checkedCreate('InstantBanking', json, ($checkedConvert) {
      final val = InstantBanking(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$InstantBankingToJson(InstantBanking instance) =>
    <String, dynamic>{'url': instance.url, 'name': instance.$type};

Safaricom _$SafaricomFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Safaricom', json, ($checkedConvert) {
      final val = Safaricom(
        url: $checkedConvert('url', (v) => v as String),
        $type: $checkedConvert('name', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {r'$type': 'name'});

Map<String, dynamic> _$SafaricomToJson(Safaricom instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.$type,
};

Unknown _$UnknownFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Unknown', json, ($checkedConvert) {
      final val = Unknown(
        url: $checkedConvert('url', (v) => v as String),
        name: $checkedConvert('name', (v) => v as String),
      );
      return val;
    });

Map<String, dynamic> _$UnknownToJson(Unknown instance) => <String, dynamic>{
  'url': instance.url,
  'name': instance.name,
};
