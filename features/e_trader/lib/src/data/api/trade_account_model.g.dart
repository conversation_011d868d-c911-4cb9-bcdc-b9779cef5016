// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trade_account_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TradeAccountModel _$TradeAccountModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_TradeAccountModel', json, ($checkedConvert) {
  final val = _TradeAccountModel(
    accountNumber: $checkedConvert('accountNumber', (v) => v as String?),
    balance: $checkedConvert('balance', (v) => (v as num?)?.toDouble()),
    equity: $checkedConvert('equity', (v) => (v as num?)?.toDouble()),
    margin: $checkedConvert('margin', (v) => (v as num?)?.toDouble()),
    freeMargin: $checkedConvert('freeMargin', (v) => (v as num?)?.toDouble()),
    profit: $checkedConvert('profit', (v) => (v as num?)?.toDouble()),
    grossProfit: $checkedConvert('grossProfit', (v) => (v as num?)?.toDouble()),
    credit: $checkedConvert('credit', (v) => (v as num?)?.toDouble()),
    marginLevel: $checkedConvert('marginLevel', (v) => (v as num?)?.toDouble()),
    productsMarginAllocation: $checkedConvert(
      'productsMarginAllocation',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) => ProductsMarginAllocationModel.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList() ??
          const [],
    ),
    connectionId: $checkedConvert('connectionId', (v) => v as String?),
    currency: $checkedConvert('currency', (v) => v as String?),
    creditAlternateCurrency: $checkedConvert(
      'creditAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    serverCode: $checkedConvert('serverCode', (v) => v as String?),
    siloAddress: $checkedConvert('siloAddress', (v) => v as String?),
    sendMobileLogOff: $checkedConvert('sendMobileLogOff', (v) => v as bool?),
    balanceAlternateCurrency: $checkedConvert(
      'balanceAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    equityAlternateCurrency: $checkedConvert(
      'equityAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    profitAlternateCurrency: $checkedConvert(
      'profitAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    grossProfitAlternateCurrency: $checkedConvert(
      'grossProfitAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    marginAlternateCurrency: $checkedConvert(
      'marginAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    freeMarginAlternateCurrency: $checkedConvert(
      'freeMarginAlternateCurrency',
      (v) => (v as num?)?.toDouble() ?? 0,
    ),
    connectionIdWeb: $checkedConvert('connectionIdWeb', (v) => v as String?),
    siloAddressWeb: $checkedConvert('siloAddressWeb', (v) => v as String?),
    sendWebLogOff: $checkedConvert('sendWebLogOff', (v) => v as bool?),
  );
  return val;
});

Map<String, dynamic> _$TradeAccountModelToJson(
  _TradeAccountModel instance,
) => <String, dynamic>{
  if (instance.accountNumber case final value?) 'accountNumber': value,
  if (instance.balance case final value?) 'balance': value,
  if (instance.equity case final value?) 'equity': value,
  if (instance.margin case final value?) 'margin': value,
  if (instance.freeMargin case final value?) 'freeMargin': value,
  if (instance.profit case final value?) 'profit': value,
  if (instance.grossProfit case final value?) 'grossProfit': value,
  if (instance.credit case final value?) 'credit': value,
  if (instance.marginLevel case final value?) 'marginLevel': value,
  'productsMarginAllocation':
      instance.productsMarginAllocation.map((e) => e.toJson()).toList(),
  if (instance.connectionId case final value?) 'connectionId': value,
  if (instance.currency case final value?) 'currency': value,
  'creditAlternateCurrency': instance.creditAlternateCurrency,
  if (instance.serverCode case final value?) 'serverCode': value,
  if (instance.siloAddress case final value?) 'siloAddress': value,
  if (instance.sendMobileLogOff case final value?) 'sendMobileLogOff': value,
  'balanceAlternateCurrency': instance.balanceAlternateCurrency,
  'equityAlternateCurrency': instance.equityAlternateCurrency,
  'profitAlternateCurrency': instance.profitAlternateCurrency,
  'grossProfitAlternateCurrency': instance.grossProfitAlternateCurrency,
  'marginAlternateCurrency': instance.marginAlternateCurrency,
  'freeMarginAlternateCurrency': instance.freeMarginAlternateCurrency,
  if (instance.connectionIdWeb case final value?) 'connectionIdWeb': value,
  if (instance.siloAddressWeb case final value?) 'siloAddressWeb': value,
  if (instance.sendWebLogOff case final value?) 'sendWebLogOff': value,
};
