// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trading_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TradingResponseModel _$TradingResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_TradingResponseModel', json, ($checkedConvert) {
  final val = _TradingResponseModel(
    list: $checkedConvert(
      'list',
      (v) =>
          (v as List<dynamic>)
              .map((e) => TradingListModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    totalCount: $checkedConvert('totalCount', (v) => (v as num).toInt()),
  );
  return val;
});

Map<String, dynamic> _$TradingResponseModelToJson(
  _TradingResponseModel instance,
) => <String, dynamic>{
  'list': instance.list.map((e) => e.toJson()).toList(),
  'totalCount': instance.totalCount,
};

_TradingListModel _$TradingListModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_TradingListModel', json, ($checkedConvert) {
      final val = _TradingListModel(
        id: $checkedConvert('id', (v) => (v as num).toInt()),
        activityType: $checkedConvert('activityType', (v) => v as String),
        activityTypeName: $checkedConvert(
          'activityTypeName',
          (v) => v as String,
        ),
        dateTime: $checkedConvert(
          'dateTime',
          (v) => v == null ? null : DateTime.parse(v as String),
        ),
        tradeDetail: $checkedConvert(
          'tradeDetail',
          (v) => TradeDetailModel.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$TradingListModelToJson(
  _TradingListModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'activityType': instance.activityType,
  'activityTypeName': instance.activityTypeName,
  if (instance.dateTime?.toIso8601String() case final value?) 'dateTime': value,
  'tradeDetail': instance.tradeDetail.toJson(),
};

_TradeDetailModel _$TradeDetailModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_TradeDetailModel', json, ($checkedConvert) {
      final val = _TradeDetailModel(
        tradeType: $checkedConvert('tradeType', (v) => v as String),
        symbol: $checkedConvert('symbol', (v) => v as String),
        profit: $checkedConvert('profit', (v) => (v as num).toDouble()),
        entry: $checkedConvert('entry', (v) => v as String),
        volume: $checkedConvert('volume', (v) => (v as num).toInt()),
        openPrice: $checkedConvert('openPrice', (v) => (v as num).toDouble()),
        positionId: $checkedConvert('positionId', (v) => (v as num).toInt()),
        productName: $checkedConvert('productName', (v) => v as String),
        friendlyName: $checkedConvert('friendlyName', (v) => v as String),
        logoUrl: $checkedConvert('logoUrl', (v) => v as String),
        positionOpenPrice: $checkedConvert(
          'positionOpenPrice',
          (v) => (v as num).toDouble(),
        ),
        commission: $checkedConvert('commission', (v) => (v as num).toDouble()),
      );
      return val;
    });

Map<String, dynamic> _$TradeDetailModelToJson(_TradeDetailModel instance) =>
    <String, dynamic>{
      'tradeType': instance.tradeType,
      'symbol': instance.symbol,
      'profit': instance.profit,
      'entry': instance.entry,
      'volume': instance.volume,
      'openPrice': instance.openPrice,
      'positionId': instance.positionId,
      'productName': instance.productName,
      'friendlyName': instance.friendlyName,
      'logoUrl': instance.logoUrl,
      'positionOpenPrice': instance.positionOpenPrice,
      'commission': instance.commission,
    };
