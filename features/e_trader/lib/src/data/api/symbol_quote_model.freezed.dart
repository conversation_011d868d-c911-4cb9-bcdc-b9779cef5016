// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symbol_quote_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SymbolQuoteModel {

 String get symbol; set symbol(String value); double get ask; set ask(double value); double get bid; set bid(double value); String? get date; set date(String? value); int get digits; set digits(int value); double get spread; set spread(double value); double get dailyRateChange; set dailyRateChange(double value); String get direction; set direction(String value); double get midPrice; set midPrice(double value); double? get previousDayPrice; set previousDayPrice(double? value);
/// Create a copy of SymbolQuoteModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<SymbolQuoteModel> get copyWith => _$SymbolQuoteModelCopyWithImpl<SymbolQuoteModel>(this as SymbolQuoteModel, _$identity);

  /// Serializes this SymbolQuoteModel to a JSON map.
  Map<String, dynamic> toJson();




@override
String toString() {
  return 'SymbolQuoteModel(symbol: $symbol, ask: $ask, bid: $bid, date: $date, digits: $digits, spread: $spread, dailyRateChange: $dailyRateChange, direction: $direction, midPrice: $midPrice, previousDayPrice: $previousDayPrice)';
}


}

/// @nodoc
abstract mixin class $SymbolQuoteModelCopyWith<$Res>  {
  factory $SymbolQuoteModelCopyWith(SymbolQuoteModel value, $Res Function(SymbolQuoteModel) _then) = _$SymbolQuoteModelCopyWithImpl;
@useResult
$Res call({
 String symbol, double ask, double bid, String? date, int digits, double spread, double dailyRateChange, String direction, double midPrice, double? previousDayPrice
});




}
/// @nodoc
class _$SymbolQuoteModelCopyWithImpl<$Res>
    implements $SymbolQuoteModelCopyWith<$Res> {
  _$SymbolQuoteModelCopyWithImpl(this._self, this._then);

  final SymbolQuoteModel _self;
  final $Res Function(SymbolQuoteModel) _then;

/// Create a copy of SymbolQuoteModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbol = null,Object? ask = null,Object? bid = null,Object? date = freezed,Object? digits = null,Object? spread = null,Object? dailyRateChange = null,Object? direction = null,Object? midPrice = null,Object? previousDayPrice = freezed,}) {
  return _then(_self.copyWith(
symbol: null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,ask: null == ask ? _self.ask : ask // ignore: cast_nullable_to_non_nullable
as double,bid: null == bid ? _self.bid : bid // ignore: cast_nullable_to_non_nullable
as double,date: freezed == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as String?,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,spread: null == spread ? _self.spread : spread // ignore: cast_nullable_to_non_nullable
as double,dailyRateChange: null == dailyRateChange ? _self.dailyRateChange : dailyRateChange // ignore: cast_nullable_to_non_nullable
as double,direction: null == direction ? _self.direction : direction // ignore: cast_nullable_to_non_nullable
as String,midPrice: null == midPrice ? _self.midPrice : midPrice // ignore: cast_nullable_to_non_nullable
as double,previousDayPrice: freezed == previousDayPrice ? _self.previousDayPrice : previousDayPrice // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SymbolQuoteModel implements SymbolQuoteModel {
   _SymbolQuoteModel({required this.symbol, required this.ask, required this.bid, this.date, required this.digits, required this.spread, required this.dailyRateChange, required this.direction, required this.midPrice, required this.previousDayPrice});
  factory _SymbolQuoteModel.fromJson(Map<String, dynamic> json) => _$SymbolQuoteModelFromJson(json);

@override  String symbol;
@override  double ask;
@override  double bid;
@override  String? date;
@override  int digits;
@override  double spread;
@override  double dailyRateChange;
@override  String direction;
@override  double midPrice;
@override  double? previousDayPrice;

/// Create a copy of SymbolQuoteModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SymbolQuoteModelCopyWith<_SymbolQuoteModel> get copyWith => __$SymbolQuoteModelCopyWithImpl<_SymbolQuoteModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SymbolQuoteModelToJson(this, );
}



@override
String toString() {
  return 'SymbolQuoteModel(symbol: $symbol, ask: $ask, bid: $bid, date: $date, digits: $digits, spread: $spread, dailyRateChange: $dailyRateChange, direction: $direction, midPrice: $midPrice, previousDayPrice: $previousDayPrice)';
}


}

/// @nodoc
abstract mixin class _$SymbolQuoteModelCopyWith<$Res> implements $SymbolQuoteModelCopyWith<$Res> {
  factory _$SymbolQuoteModelCopyWith(_SymbolQuoteModel value, $Res Function(_SymbolQuoteModel) _then) = __$SymbolQuoteModelCopyWithImpl;
@override @useResult
$Res call({
 String symbol, double ask, double bid, String? date, int digits, double spread, double dailyRateChange, String direction, double midPrice, double? previousDayPrice
});




}
/// @nodoc
class __$SymbolQuoteModelCopyWithImpl<$Res>
    implements _$SymbolQuoteModelCopyWith<$Res> {
  __$SymbolQuoteModelCopyWithImpl(this._self, this._then);

  final _SymbolQuoteModel _self;
  final $Res Function(_SymbolQuoteModel) _then;

/// Create a copy of SymbolQuoteModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbol = null,Object? ask = null,Object? bid = null,Object? date = freezed,Object? digits = null,Object? spread = null,Object? dailyRateChange = null,Object? direction = null,Object? midPrice = null,Object? previousDayPrice = freezed,}) {
  return _then(_SymbolQuoteModel(
symbol: null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,ask: null == ask ? _self.ask : ask // ignore: cast_nullable_to_non_nullable
as double,bid: null == bid ? _self.bid : bid // ignore: cast_nullable_to_non_nullable
as double,date: freezed == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as String?,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,spread: null == spread ? _self.spread : spread // ignore: cast_nullable_to_non_nullable
as double,dailyRateChange: null == dailyRateChange ? _self.dailyRateChange : dailyRateChange // ignore: cast_nullable_to_non_nullable
as double,direction: null == direction ? _self.direction : direction // ignore: cast_nullable_to_non_nullable
as String,midPrice: null == midPrice ? _self.midPrice : midPrice // ignore: cast_nullable_to_non_nullable
as double,previousDayPrice: freezed == previousDayPrice ? _self.previousDayPrice : previousDayPrice // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on
