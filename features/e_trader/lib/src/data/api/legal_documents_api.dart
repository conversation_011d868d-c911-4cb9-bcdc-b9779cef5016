import 'package:api_client/api_client.dart';
import 'package:e_trader/src/data/api/document_contents_firestore.dart';
import 'package:e_trader/src/data/api/legal_document_contents_model.dart';
import 'package:e_trader/src/data/api/legal_documents_model.dart';
import 'package:e_trader/src/domain/model/legal_document.dart';
import 'package:prelude/prelude.dart';

class LegalDocumentsApi {
  const LegalDocumentsApi(this.apiClient, this.documentContentsFirestore);

  final ApiClientBase apiClient;
  final DocumentContentsFirestore documentContentsFirestore;

  // TODO(sagar): get data from firestore
  TaskEither<Exception, LegalDocumentContentsModel> getDocumentContents() =>
      TaskEither.tryCatch(
        () => documentContentsFirestore.getDocumentContents(),
        (e, stackTrace) => Exception(e),
      );

  TaskEither<Exception, LegalDocumentsModel> getLegalDocuments() => apiClient
      .get<List<Object?>>("middlewareapi/Broker/get-legal-documents")
      .flatMap(
        (response) => TaskEither.tryCatch(() async {
          final jsonList = response.data ?? [];

          final List<LegalDocument> documents =
              jsonList
                  .map(
                    (json) =>
                        LegalDocument.fromJson(json as Map<String, dynamic>),
                  )
                  .toList();
          return LegalDocumentsModel(legalDocuments: documents);
        }, (error, stackTrace) => Exception(error)),
      );
}
