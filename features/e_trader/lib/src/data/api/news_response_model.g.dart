// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'news_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NewsResponseModel _$NewsResponseModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_NewsResponseModel', json, ($checkedConvert) {
      final val = _NewsResponseModel(
        count: $checkedConvert('count', (v) => (v as num).toInt()),
        data: $checkedConvert(
          'data',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => NewsItem.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$NewsResponseModelToJson(_NewsResponseModel instance) =>
    <String, dynamic>{
      'count': instance.count,
      'data': instance.data.map((e) => e.toJson()).toList(),
    };

_NewsItem _$NewsItemFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_NewsItem', json, ($checkedConvert) {
      final val = _NewsItem(
        item: $checkedConvert(
          'item',
          (v) => NewsItemDetails.fromJson(v as Map<String, dynamic>),
        ),
        rank: $checkedConvert('rank', (v) => (v as num).toInt()),
      );
      return val;
    });

Map<String, dynamic> _$NewsItemToJson(_NewsItem instance) => <String, dynamic>{
  'item': instance.item.toJson(),
  'rank': instance.rank,
};

_NewsItemDetails _$NewsItemDetailsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_NewsItemDetails', json, ($checkedConvert) {
      final val = _NewsItemDetails(
        id: $checkedConvert('id', (v) => v as String),
        title: $checkedConvert('title', (v) => v as String),
        content: $checkedConvert('content', (v) => v as String),
        source: $checkedConvert('source', (v) => v as String),
        date: $checkedConvert('date', (v) => v as String),
        language: $checkedConvert('language', (v) => v as String),
        symbols: $checkedConvert(
          'symbols',
          (v) => (v as List<dynamic>).map((e) => e as String).toList(),
        ),
        tags: $checkedConvert(
          'tags',
          (v) => (v as List<dynamic>).map((e) => e as String).toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$NewsItemDetailsToJson(_NewsItemDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'source': instance.source,
      'date': instance.date,
      'language': instance.language,
      'symbols': instance.symbols,
      'tags': instance.tags,
    };
