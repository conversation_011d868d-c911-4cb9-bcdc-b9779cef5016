// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'legal_document_contents_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LegalDocumentContentsModel _$LegalDocumentContentsModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_LegalDocumentContentsModel', json, ($checkedConvert) {
  final val = _LegalDocumentContentsModel(
    legalDocumentContents: $checkedConvert(
      'legalDocumentContents',
      (v) =>
          (v as List<dynamic>?)
              ?.map(
                (e) => LegalDocumentContent.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
    ),
  );
  return val;
});

Map<String, dynamic> _$LegalDocumentContentsModelToJson(
  _LegalDocumentContentsModel instance,
) => <String, dynamic>{
  'legalDocumentContents':
      instance.legalDocumentContents.map((e) => e.toJson()).toList(),
};

_LegalDocumentContent _$LegalDocumentContentFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_LegalDocumentContent', json, ($checkedConvert) {
  final val = _LegalDocumentContent(
    id: $checkedConvert('id', (v) => v as String),
    docData: $checkedConvert(
      'docData',
      (v) => LegalDocumentTitle.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$LegalDocumentContentToJson(
  _LegalDocumentContent instance,
) => <String, dynamic>{'id': instance.id, 'docData': instance.docData.toJson()};

_LegalDocumentTitle _$LegalDocumentTitleFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_LegalDocumentTitle', json, ($checkedConvert) {
      final val = _LegalDocumentTitle(
        en: $checkedConvert('en', (v) => v as String),
        ar: $checkedConvert('ar', (v) => v as String),
      );
      return val;
    });

Map<String, dynamic> _$LegalDocumentTitleToJson(_LegalDocumentTitle instance) =>
    <String, dynamic>{'en': instance.en, 'ar': instance.ar};
