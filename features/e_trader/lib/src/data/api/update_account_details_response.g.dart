// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_account_details_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UpdateAccountDetailsResponse _$UpdateAccountDetailsResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_UpdateAccountDetailsResponse', json, ($checkedConvert) {
  final val = _UpdateAccountDetailsResponse(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert('data', (v) => v as bool?),
    error: $checkedConvert(
      'error',
      (v) =>
          v == null
              ? null
              : UpdateAccountDetailsError.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$UpdateAccountDetailsResponseToJson(
  _UpdateAccountDetailsResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  if (instance.data case final value?) 'data': value,
  if (instance.error?.toJson() case final value?) 'error': value,
};

_UpdateAccountDetailsError _$UpdateAccountDetailsErrorFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_UpdateAccountDetailsError', json, ($checkedConvert) {
  final val = _UpdateAccountDetailsError(
    errorCode: $checkedConvert('errorCode', (v) => (v as num?)?.toInt()),
    description: $checkedConvert('description', (v) => v as String?),
    fieldErrors: $checkedConvert(
      'fieldErrors',
      (v) => (v as Map<String, dynamic>?)?.map(
        (k, e) =>
            MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
    ),
  );
  return val;
});

Map<String, dynamic> _$UpdateAccountDetailsErrorToJson(
  _UpdateAccountDetailsError instance,
) => <String, dynamic>{
  if (instance.errorCode case final value?) 'errorCode': value,
  if (instance.description case final value?) 'description': value,
  if (instance.fieldErrors case final value?) 'fieldErrors': value,
};
