import 'package:freezed_annotation/freezed_annotation.dart';

part 'symbol_quote_model.freezed.dart';
part 'symbol_quote_model.g.dart';

@unfreezed
abstract class SymbolQuoteModel with _$SymbolQuoteModel {
  factory SymbolQuoteModel({
    required String symbol,
    required double ask,
    required double bid,
    String? date,
    required int digits,
    required double spread,
    required double dailyRateChange,
    required String direction,
    required double midPrice,
    required double? previousDayPrice,
  }) = _SymbolQuoteModel;

  factory SymbolQuoteModel.fromJson(Map<String, dynamic> json) =>
      _$SymbolQuoteModelFromJson(json);
}
