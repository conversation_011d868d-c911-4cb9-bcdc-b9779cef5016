// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'funding_activity_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FundingActivityResponse _$FundingActivityResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_FundingActivityResponse', json, ($checkedConvert) {
  final val = _FundingActivityResponse(
    list: $checkedConvert(
      'list',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => FundingItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    totalCount: $checkedConvert('totalCount', (v) => (v as num?)?.toInt() ?? 0),
  );
  return val;
});

Map<String, dynamic> _$FundingActivityResponseToJson(
  _FundingActivityResponse instance,
) => <String, dynamic>{
  if (instance.list?.map((e) => e.toJson()).toList() case final value?)
    'list': value,
  'totalCount': instance.totalCount,
};

_FundingItem _$FundingItemFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_FundingItem', json, ($checkedConvert) {
  final val = _FundingItem(
    id: $checkedConvert('id', (v) => (v as num?)?.toInt()),
    activityType: $checkedConvert('activityType', (v) => v as String?),
    activityTypeName: $checkedConvert('activityTypeName', (v) => v as String?),
    dateTime: $checkedConvert(
      'dateTime',
      (v) => v == null ? null : DateTime.parse(v as String),
    ),
    activityDetail: $checkedConvert(
      'activityDetail',
      (v) =>
          v == null ? null : ActivityDetail.fromJson(v as Map<String, dynamic>),
    ),
    operationId: $checkedConvert('operationId', (v) => v as String?),
  );
  return val;
});

Map<String, dynamic> _$FundingItemToJson(
  _FundingItem instance,
) => <String, dynamic>{
  if (instance.id case final value?) 'id': value,
  if (instance.activityType case final value?) 'activityType': value,
  if (instance.activityTypeName case final value?) 'activityTypeName': value,
  if (instance.dateTime?.toIso8601String() case final value?) 'dateTime': value,
  if (instance.activityDetail?.toJson() case final value?)
    'activityDetail': value,
  if (instance.operationId case final value?) 'operationId': value,
};

_ActivityDetail _$ActivityDetailFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_ActivityDetail', json, ($checkedConvert) {
  final val = _ActivityDetail(
    currency: $checkedConvert('currency', (v) => v as String?),
    amount: $checkedConvert('amount', (v) => (v as num?)?.toDouble()),
    direction: $checkedConvert('direction', (v) => v as String?),
    account: $checkedConvert('account', (v) => v as String?),
    status: $checkedConvert('status', (v) => v as String?),
    statusName: $checkedConvert('statusName', (v) => v as String?),
    paymentMethod: $checkedConvert('paymentMethod', (v) => v as String?),
    oldLeverage: $checkedConvert('oldLeverage', (v) => (v as num?)?.toInt()),
    newLeverage: $checkedConvert('newLeverage', (v) => (v as num?)?.toInt()),
    transactionType: $checkedConvert('transactionType', (v) => v as String?),
    comment: $checkedConvert('comment', (v) => v as String?),
    campaign: $checkedConvert('campaign', (v) => v as String?),
  );
  return val;
});

Map<String, dynamic> _$ActivityDetailToJson(_ActivityDetail instance) =>
    <String, dynamic>{
      if (instance.currency case final value?) 'currency': value,
      if (instance.amount case final value?) 'amount': value,
      if (instance.direction case final value?) 'direction': value,
      if (instance.account case final value?) 'account': value,
      if (instance.status case final value?) 'status': value,
      if (instance.statusName case final value?) 'statusName': value,
      if (instance.paymentMethod case final value?) 'paymentMethod': value,
      if (instance.oldLeverage case final value?) 'oldLeverage': value,
      if (instance.newLeverage case final value?) 'newLeverage': value,
      if (instance.transactionType case final value?) 'transactionType': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.campaign case final value?) 'campaign': value,
    };
