// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_trade_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CloseTradeRequestModel _$CloseTradeRequestModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_CloseTradeRequestModel', json, ($checkedConvert) {
  final val = _CloseTradeRequestModel(
    accountNumber: $checkedConvert('accountNumber', (v) => v as String?),
    positions: $checkedConvert(
      'positions',
      (v) =>
          (v as List<dynamic>)
              .map(
                (e) =>
                    ClosePositionItemModel.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    ),
  );
  return val;
});

Map<String, dynamic> _$CloseTradeRequestModelToJson(
  _CloseTradeRequestModel instance,
) => <String, dynamic>{
  if (instance.accountNumber case final value?) 'accountNumber': value,
  'positions': instance.positions.map((e) => e.toJson()).toList(),
};

_ClosePositionItemModel _$ClosePositionItemModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_ClosePositionItemModel', json, ($checkedConvert) {
  final val = _ClosePositionItemModel(
    id: $checkedConvert('id', (v) => v as String),
    volume: $checkedConvert('volume', (v) => (v as num).toInt()),
  );
  return val;
});

Map<String, dynamic> _$ClosePositionItemModelToJson(
  _ClosePositionItemModel instance,
) => <String, dynamic>{'id': instance.id, 'volume': instance.volume};
