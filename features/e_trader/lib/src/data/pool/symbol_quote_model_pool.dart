import 'package:e_trader/src/data/api/symbol_quote_model.dart';

/// Object pool for managing and reusing SymbolQuoteModel instances
/// Uses intelligent caching to minimize object creation
class SymbolQuoteModelPool {
  final Map<String, SymbolQuoteModel> _pool = {};
  final Map<String, Map<String, dynamic>> _lastJsonData = {};

  /// Get existing model or create new one for symbol
  /// Reuses existing instances and updates properties in place for maximum efficiency
  SymbolQuoteModel getOrCreate(String symbol, Map<String, dynamic> json) {
    final existingModel = _pool[symbol];
    final lastJson = _lastJsonData[symbol];

    // If we have an existing model, update it in place
    if (existingModel != null && lastJson != null) {
      if (_hasDataChanged(lastJson, json)) {
        // Data changed, update existing model properties in place
        _updateModelInPlace(existingModel, json);
        _lastJsonData[symbol] = Map<String, dynamic>.from(json);
      }
      // Return the same instance (either updated or unchanged)
      return existingModel;
    }

    // No existing model, create new one
    final newModel = SymbolQuoteModel.fromJson(json);
    _pool[symbol] = newModel;
    _lastJsonData[symbol] = Map<String, dynamic>.from(json);
    return newModel;
  }

  /// Update model properties in place to avoid creating new instances
  void _updateModelInPlace(SymbolQuoteModel model, Map<String, dynamic> json) {
    // Update only the properties that can change
    final newAsk = (json['ask'] as num?)?.toDouble();
    final newBid = (json['bid'] as num?)?.toDouble();
    final newSpread = (json['spread'] as num?)?.toDouble();
    final newDailyRateChange = (json['dailyRateChange'] as num?)?.toDouble();
    final newDirection = json['direction'] as String?;
    final newMidPrice = (json['midPrice'] as num?)?.toDouble();
    final newPreviousDayPrice = (json['previousDayPrice'] as num?)?.toDouble();
    final newDigits = (json['digits'] as num?)?.toInt();

    // Update properties directly (now possible with @unfreezed)
    if (newAsk != null) model.ask = newAsk;
    if (newBid != null) model.bid = newBid;
    if (newSpread != null) model.spread = newSpread;
    if (newDailyRateChange != null) model.dailyRateChange = newDailyRateChange;
    if (newDirection != null) model.direction = newDirection;
    if (newMidPrice != null) model.midPrice = newMidPrice;
    if (newPreviousDayPrice != null)
      model.previousDayPrice = newPreviousDayPrice;
    if (newDigits != null) model.digits = newDigits;

    // Update date if provided
    if (json['date'] != null) model.date = json['date'] as String;
  }

  /// Check if the JSON data has changed for important fields
  /// Only checks fields that matter for trading (ask, bid, spread, etc.)
  bool _hasDataChanged(
    Map<String, dynamic> oldJson,
    Map<String, dynamic> newJson,
  ) {
    // List of fields that matter for updates
    const importantFields = [
      'ask',
      'bid',
      'spread',
      'dailyRateChange',
      'direction',
      'midPrice',
      'previousDayPrice',
    ];

    for (final field in importantFields) {
      if (oldJson[field] != newJson[field]) {
        return true;
      }
    }
    return false;
  }

  /// Check if symbol exists in pool
  bool hasSymbol(String symbol) => _pool.containsKey(symbol);

  /// Get existing model without creating new one
  SymbolQuoteModel? getExisting(String symbol) => _pool[symbol];

  /// Remove symbol from pool (for cleanup when unsubscribing)
  void removeSymbol(String symbol) => _pool.remove(symbol);

  /// Clear entire pool (for cleanup)
  void clear() => _pool.clear();

  /// Get pool size for monitoring
  int get size => _pool.length;

  /// Get all symbols in pool
  Set<String> get symbols => _pool.keys.toSet();

  /// Get pool statistics for monitoring and debugging
  Map<String, dynamic> getStats() {
    return {
      'poolSize': size,
      'symbols': symbols.toList(),
      'memoryEstimate': '${(size * 200)} bytes', // Rough estimate
    };
  }
}
