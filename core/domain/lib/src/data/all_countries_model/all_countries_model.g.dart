// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'all_countries_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AllCountriesModel _$AllCountriesModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_AllCountriesModel', json, ($checkedConvert) {
      final val = _AllCountriesModel(
        countries: $checkedConvert(
          'data',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => CountryData.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    }, fieldKeyMap: const {'countries': 'data'});

Map<String, dynamic> _$AllCountriesModelToJson(_AllCountriesModel instance) =>
    <String, dynamic>{
      'data': instance.countries.map((e) => e.toJson()).toList(),
    };

_CountryData _$CountryDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_CountryData', json, ($checkedConvert) {
      final val = _CountryData(
        name: $checkedConvert('name', (v) => v as String),
        code: $checkedConvert('code', (v) => v as String),
        brokerId: $checkedConvert('brokerId', (v) => v as String?),
        dialCode: $checkedConvert('dialCode', (v) => v as String?),
        flag: $checkedConvert('flagUrl', (v) => v as String?),
        cities: $checkedConvert(
          'cities',
          (v) =>
              (v as List<dynamic>?)
                  ?.map((e) => City.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    }, fieldKeyMap: const {'flag': 'flagUrl'});

Map<String, dynamic> _$CountryDataToJson(_CountryData instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      if (instance.brokerId case final value?) 'brokerId': value,
      if (instance.dialCode case final value?) 'dialCode': value,
      if (instance.flag case final value?) 'flagUrl': value,
      if (instance.cities?.map((e) => e.toJson()).toList() case final value?)
        'cities': value,
    };

_City _$CityFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_City', json, ($checkedConvert) {
      final val = _City(
        code: $checkedConvert('code', (v) => v as String),
        name: $checkedConvert('name', (v) => v as String),
        sortOrder: $checkedConvert('sort_order', (v) => (v as num).toInt()),
      );
      return val;
    }, fieldKeyMap: const {'sortOrder': 'sort_order'});

Map<String, dynamic> _$CityToJson(_City instance) => <String, dynamic>{
  'code': instance.code,
  'name': instance.name,
  'sort_order': instance.sortOrder,
};
