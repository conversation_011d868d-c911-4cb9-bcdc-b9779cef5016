import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:duplo/duplo.dart';

/// Usage: I enter {"<EMAIL>"} in the field with hint {"Enter Email"}
Future<void> iEnterInTheFieldWithHint(
  WidgetTester tester,
  String text,
  String hintText,
) async {
  // First try to find DuploTextField with the given hint
  final duploFieldFinder = find.byWidgetPredicate(
    (widget) => widget is DuploTextField && widget.hint == hintText,
  );

  // If not found, try to find by label
  final duploFieldByLabelFinder = find.byWidgetPredicate(
    (widget) => widget is DuploTextField && widget.label == hintText,
  );

  // Determine which finder to use
  final finder =
      duploFieldFinder.evaluate().isNotEmpty
          ? duploFieldFinder
          : duploFieldByLabelFinder;

  expect(
    finder,
    findsOneWidget,
    reason: 'Could not find Dup<PERSON><PERSON>ex<PERSON><PERSON>ield with hint or label: $hintText',
  );

  // Find the TextFormField inside the DuploTextField
  final textFormFieldFinder = find.descendant(
    of: finder,
    matching: find.byType(TextFormField),
  );

  expect(
    textFormFieldFinder,
    findsOneWidget,
    reason: 'Could not find TextFormField inside DuploTextField',
  );

  // Tap on the text field to focus it
  await tester.tap(textFormFieldFinder);
  await tester.pump();

  // Enter the text
  await tester.enterText(textFormFieldFinder, text);
  await tester.pump();
}
