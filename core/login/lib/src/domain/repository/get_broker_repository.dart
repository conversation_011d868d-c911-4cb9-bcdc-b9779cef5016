import 'package:api_client/api_client.dart';
import 'package:login/src/data/api/broker_model.dart';
import 'package:prelude/prelude.dart';

class GetBrokerRepository {
  final ApiClientBase apiClientBase;

  const GetBrokerRepository({required this.apiClientBase});

  TaskEither<Exception, BrokerModel> call({required String email}) {
    return apiClientBase
        .get<List<Object?>>("middlewareapi/Broker/get-brokers/$email")
        .flatMap(
          (response) => TaskEither.tryCatch(() async {
            final List<BrokerContentModel> list =
                response.data!
                    .map(
                      (json) => BrokerContentModel.fromJson(
                        json as Map<String, dynamic>,
                      ),
                    )
                    .toList();
            return BrokerModel(broker: list);
          }, (error, stackTrace) => Exception(error)),
        );
  }
}
