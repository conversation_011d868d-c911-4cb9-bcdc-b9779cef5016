import 'package:api_client/api_client.dart';
import 'package:login/src/data/api/auth_user.dart';
import 'package:login/src/data/api/login_request_model.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:prelude/prelude.dart';

class LoginRepository {
  final ApiClientBase apiClientBase;
  final TokenManager tokenManager;
  final AuthService authService;

  const LoginRepository({
    required this.apiClientBase,
    required this.tokenManager,
    required this.authService,
  });

  // todo (aakash): Delete this
  TaskEither<Exception, AuthUser> login({
    required String username,
    required String password,
    required String broker_id,
  }) {
    return apiClientBase
        .post<Map<String, dynamic>>(
          "api/v1/users/login",
          contentType: 'application/json',
          data:
              LoginRequestModel(
                user_name: username,
                password: password,
                broker_id: broker_id,
              ).toJson(),
        )
        .flatMap(
          (response) => TaskEither.tryCatch(
            () async => AuthUser.fromJson(response.data!),
            (error, stackTrace) => Exception(error),
          ),
        )
        .chainFirst(
          (authUser) => TaskEither.tryCatch(() async {
            await tokenManager.saveTokenInfo(
              authUser.data.access_token,
              authUser.data.refresh_token,
              username,
            );
          }, (error, stackTrace) => Exception(error)),
        );
  }

  TaskEither<Exception, AuthResult> loginWithOkta({required String email}) {
    return authService
        .login(email: email, authFlow: AuthFlow.login)
        .flatMap(
          (AuthResult authResult) => TaskEither.tryCatch(() async {
            await tokenManager.saveTokenInfo(
              authResult.accessToken,
              authResult.refreshToken!,
              email,
            );
            return authResult;
          }, (error, stackTrace) => Exception(error)),
        )
        .mapLeft((error) => Exception(error));
  }
}
