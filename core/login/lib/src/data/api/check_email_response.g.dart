// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_email_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CheckEmailResponse _$CheckEmailResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_CheckEmailResponse', json, ($checkedConvert) {
  final val = _CheckEmailResponse(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) =>
          v == null ? null : CheckEmailData.fromJson(v as Map<String, dynamic>),
    ),
    error: $checkedConvert(
      'error',
      (v) => v == null ? null : ErrorData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

_ErrorData _$ErrorDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ErrorData', json, ($checkedConvert) {
      final val = _ErrorData(
        errorCode: $checkedConvert('errorCode', (v) => (v as num).toInt()),
        description: $checkedConvert('description', (v) => v as String),
        fieldErrors: $checkedConvert(
          'fieldErrors',
          (v) => (v as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
              k,
              (e as List<dynamic>).map((e) => e as String).toList(),
            ),
          ),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ErrorDataToJson(_ErrorData instance) =>
    <String, dynamic>{
      'errorCode': instance.errorCode,
      'description': instance.description,
      if (instance.fieldErrors case final value?) 'fieldErrors': value,
    };

_CheckEmailData _$CheckEmailDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_CheckEmailData', json, ($checkedConvert) {
      final val = _CheckEmailData(
        isExist: $checkedConvert('isExist', (v) => v as bool),
      );
      return val;
    });

Map<String, dynamic> _$CheckEmailDataToJson(_CheckEmailData instance) =>
    <String, dynamic>{'isExist': instance.isExist};
