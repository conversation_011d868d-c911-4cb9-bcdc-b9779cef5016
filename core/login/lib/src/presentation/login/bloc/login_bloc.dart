import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:login/src/data/api/auth_user.dart';
import 'package:login/src/data/api/broker_model.dart';
import 'package:login/src/domain/usecase/broker_usecase.dart';
import 'package:login/src/domain/usecase/login_usecase.dart';
import 'package:prelude/prelude.dart';
import 'package:validator/validator.dart';

part 'login_event.dart';
part 'login_state.dart';
part 'login_bloc.freezed.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase _loginUseCase;
  // todo (aakash): Check if broker usecase is needed or not with the new login
  // ignore: unused_field
  final BrokerUseCase _brokerUseCase;

  LoginBloc(this._loginUseCase, this._brokerUseCase)
    : super(const LoginState()) {
    on<_LoginRequested>(_onLoginRequested);
    on<_UpdateShowPassword>(_updateShowPassword);
    on<_EmptyEmailFieldErrorText>(_emptyFieldsErrorText);
    on<_EmptyPasswordFieldErrorText>(_emptyPasswordFieldErrorText);
  }

  Future<void> _onLoginRequested(
    _LoginRequested event,
    Emitter<LoginState> emit,
  ) async {
    add(LoginEvent.emptyPasswordFieldErrorText());
    InputValidator validator = InputValidator();
    final isEmailValid =
        validator.validate(event.username).email().run().isEmpty;
    final emailError =
        event.username.isEmpty
            ? "emptyEmailField"
            : !isEmailValid
            ? "invalidEmail"
            : null;

    final passwordError = event.password.isEmpty ? "emptyPasswordField" : null;

    if (emailError != null || passwordError != null) {
      return emit(
        state.copyWith(
          emailErrorMessage: emailError,
          passwordErrorMessage: passwordError,
        ),
      );
    }

    emit(state.copyWith(loginRequestState: LoginRequestState.loading()));

    final result =
        await TaskEither<Exception, LoginRequestState>.Do(($) async {
          // final brokers = await $(_brokerUseCase(event.username));

          // if (brokers.broker.isEmpty) {
          //   print("No broker found");
          //   throw Exception("No broker found");
          // }
          // if (brokers.broker.length > 1) {
          //   return LoginRequestState.successMultiBroker();
          // }
          // final brokerId = brokers.broker.firstOrNull!.brokerId!;
          // print("brokerId: $brokerId");
          final authUser = await $(
            _loginUseCase(event.username, event.password, "SCA"),
          );

          return LoginRequestState.success(authUser);
        }).run();

    result.fold(
      (exception) {
        addError(exception);
        emit(
          state.copyWith(
            emailErrorMessage: null,
            passwordErrorMessage: "invalidCredentials",
            loginRequestState: LoginRequestState.failure(exception.toString()),
          ),
        );
      },
      (response) {
        emit(
          state.copyWith(
            loginRequestState: response,
            userInfo: switch (response) {
              LoginRequestSuccess(:final user) => user,
              _ => null,
            },
          ),
        );
      },
    );
  }

  FutureOr<void> _updateShowPassword(
    _UpdateShowPassword event,
    Emitter<LoginState> emit,
  ) {
    emit(state.copyWith(showPassword: event.status));
  }

  FutureOr<void> _emptyFieldsErrorText(
    _EmptyEmailFieldErrorText event,
    Emitter<LoginState> emit,
  ) {
    emit(state.copyWith(emailErrorMessage: null));
  }

  FutureOr<void> _emptyPasswordFieldErrorText(
    _EmptyPasswordFieldErrorText event,
    Emitter<LoginState> emit,
  ) {
    emit(state.copyWith(passwordErrorMessage: null));
  }
}
