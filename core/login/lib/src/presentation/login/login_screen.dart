import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/data/api/auth_user.dart';
import 'package:login/src/di/di_container.dart';
import 'package:login/src/presentation/login/bloc/login_bloc.dart';
import 'package:login/src/presentation/login/widgets/email_field.dart';
import 'package:login/src/presentation/login/widgets/login_button.dart';
import 'package:login/src/presentation/login/widgets/login_header.dart';
import 'package:login/src/presentation/login/widgets/password_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen(this.onLoginSuccess, {this.email});
  final void Function(AuthUser user) onLoginSuccess;
  final String? email;

  @override
  State<LoginScreen> createState() => _EmailLoginWidgetState();
}

class _EmailLoginWidgetState extends State<LoginScreen> {
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;
  FocusNode? _passwordFocusNode;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController();
    _passwordController = TextEditingController();
    if (widget.email?.isNotEmpty ?? false) {
      _passwordFocusNode = FocusNode();
      _emailController.text = widget.email!;
      _passwordFocusNode?.requestFocus();
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _passwordFocusNode?.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocProvider(
      create: (BuildContext createContext) => diContainer<LoginBloc>(),
      child: BlocListener<LoginBloc, LoginState>(
        listenWhen:
            (previous, current) =>
                previous.loginRequestState != current.loginRequestState,
        listener: (listenerContext, state) {
          if (state.loginRequestState case LoginRequestSuccess requestState) {
            widget.onLoginSuccess(requestState.user);
          }
        },
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Stack(
            children: [
              Scaffold(
                resizeToAvoidBottomInset: false,
                appBar: AppBar(
                  backgroundColor: theme.background.bgPrimary,
                  toolbarHeight: 80,
                  elevation: 0,
                ),
                backgroundColor: theme.background.bgPrimary,
                body: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LoginHeader(),
                        SizedBox(height: 20),
                        EmailField(emailController: _emailController),
                        const SizedBox(height: 16),
                        PasswordField(
                          focusNode: _passwordFocusNode,
                          passwordController: _passwordController,
                          onEditingComplete: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                          },
                        ),
                        SizedBox(height: 20),
                        GestureDetector(
                          onTap: () {
                            print("Forgot password");
                          },
                          child: Align(
                            alignment: Alignment.topRight,
                            child: DuploText(
                              text:
                                  EquitiLocalization.of(
                                    context,
                                  ).login_forgotPassword,
                              style: context.duploTextStyles.textMd,
                              color: theme.button.buttonTertiaryFg,
                            ),
                          ),
                        ),
                        SizedBox(height: 20),
                        BlocBuilder<LoginBloc, LoginState>(
                          buildWhen:
                              (previous, current) =>
                                  (previous.passwordErrorMessage !=
                                      current.passwordErrorMessage) ||
                                  (previous.loginRequestState !=
                                      current.loginRequestState),
                          builder: (builderContext, state) {
                            return switch (state.loginRequestState) {
                              LoginRequestSuccessMultiBroker() =>
                                DuploAlertMessage.error(
                                  title:
                                      EquitiLocalization.of(
                                        builderContext,
                                      ).login_multiBrokerError,
                                ),
                              _ =>
                                state.passwordErrorMessage ==
                                        "invalidCredentials"
                                    ? DuploAlertMessage.error(
                                      title:
                                          EquitiLocalization.of(
                                            builderContext,
                                          ).login_loginErrorMessage,
                                    )
                                    : Container(),
                            };
                          },
                        ),
                        const SizedBox(height: 16),
                        Spacer(),
                        BlocBuilder<LoginBloc, LoginState>(
                          buildWhen:
                              (previous, current) =>
                                  previous.loginRequestState !=
                                  current.loginRequestState,
                          builder: (builderContext, state) {
                            return LoginButton(
                              onPressed: () {
                                builderContext.read<LoginBloc>().add(
                                  LoginEvent.loginRequested(
                                    _emailController.text.trim(),
                                    _passwordController.text,
                                  ),
                                );
                              },
                              isLoading: switch (state.loginRequestState) {
                                LoginRequestLoading() => true,
                                _ => false,
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
