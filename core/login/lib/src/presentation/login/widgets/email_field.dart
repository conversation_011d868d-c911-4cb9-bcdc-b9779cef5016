import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/assets/assets.gen.dart' as trader;
import 'package:login/src/presentation/login/bloc/login_bloc.dart';

class EmailField extends StatelessWidget {
  const EmailField({super.key, required this.emailController});
  final TextEditingController emailController;
  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return BlocBuilder<LoginBloc, LoginState>(
      buildWhen:
          (previous, current) =>
              previous.emailErrorMessage != current.emailErrorMessage,
      builder: (builderContext, state) {
        return DuploTextField(
          semanticsIdentifier: "enter_email",
          autoCorrect: false,
          errorMessage: emailErrorMessage(
            state.emailErrorMessage,
            localization,
          ),
          prefixIcon: trader.Assets.images.emailIc.svg(),
          hint: localization.login_enterEmail,
          controller: emailController,
          suffixIcon: Tooltip(
            enableFeedback: true,
            triggerMode: TooltipTriggerMode.tap,
            message: localization.login_emailTooltip,
            child: trader.Assets.images.emailInfoIc.svg(),
          ),
          label: localization.login_emailAddress,
          onChanged: (value) {
            context.read<LoginBloc>().add(
              const LoginEvent.emptyEmailFieldErrorText(),
            );
          },
        );
      },
    );
  }

  String? emailErrorMessage(String? key, EquitiLocalization localization) {
    if (key == "invalidEmail") {
      return localization.login_enterValidEmail;
    }
    if (key == "invalidBroker") {
      return localization.login_invalidBroker;
    }

    if (key == "emptyEmailField") {
      return localization.login_emptEmailField;
    }
    if (key == "") {
      return "";
    }
    return null;
  }
}
