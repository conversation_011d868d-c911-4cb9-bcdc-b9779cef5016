import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/assets/assets.gen.dart' as trader;
import 'package:login/src/presentation/login/bloc/login_bloc.dart';

class PasswordField extends StatelessWidget {
  const PasswordField({
    super.key,
    required this.passwordController,
    this.onEditingComplete,
    this.focusNode,
  });
  final TextEditingController passwordController;
  final VoidCallback? onEditingComplete;
  final FocusNode? focusNode;
  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return BlocBuilder<LoginBloc, LoginState>(
      buildWhen:
          (previous, current) =>
              previous.showPassword != current.showPassword ||
              previous.passwordErrorMessage != current.passwordErrorMessage,
      builder: (builderContext, state) {
        return DuploTextField(
          semanticsIdentifier: "enter_password",
          focusNode: focusNode,
          onEditingComplete: onEditingComplete,
          obscureText: !state.showPassword,
          suffixIcon: InkWell(
            onTap: () {
              context.read<LoginBloc>().add(
                LoginEvent.updateShowPassword(!state.showPassword),
              );
            },
            child: trader.Assets.images.showPasswordIc.svg(),
          ),
          hint: localization.login_enterPassword,
          controller: passwordController,
          errorMessage: passwordErrorMessage(
            state.passwordErrorMessage,
            localization,
          ),
          label: localization.login_password,
          onChanged: (value) {
            context.read<LoginBloc>().add(
              const LoginEvent.emptyPasswordFieldErrorText(),
            );
          },
        );
      },
    );
  }

  String? passwordErrorMessage(String? key, EquitiLocalization localization) {
    if (key == "emptyPasswordField") {
      return localization.login_emptyPasswordField;
    }
    if (key == "") {
      return "";
    }
    return null;
  }
}
