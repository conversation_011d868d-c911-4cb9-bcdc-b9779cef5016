import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:login/src/domain/usecase/check_email_usecase.dart';
import 'package:login/src/domain/usecase/login_with_okta_usecase.dart';
import 'package:login/src/navigation/login_navigation.dart';
import 'package:prelude/prelude.dart';
import 'package:validator/validator.dart';

part 'login_options_bloc.freezed.dart';
part 'login_options_event.dart';
part 'login_options_state.dart';

class LoginOptionsBloc extends Bloc<LoginOptionsEvent, LoginOptionsState> {
  final LoginNavigation _loginNavigation;
  final LoginWithOktaUsecase _loginWithOktaUsecase;
  final CheckEmailUseCase _checkEmailUseCase;

  LoginOptionsBloc(
    this._loginNavigation,
    this._loginWithOktaUsecase,
    this._checkEmailUseCase,
  ) : super(const LoginOptionsState()) {
    on<_NavigateToLogin>(_navigateToLogin);
    on<_NavigateToSignup>(_navigateToSignup);
    on<_ValidateEmail>(_validateEmail);
    on<_BottomSheetClosed>(_bottomSheetClosed);
  }

  FutureOr<void> _navigateToLogin(
    _NavigateToLogin event,
    Emitter<LoginOptionsState> emit,
  ) async {
    final result =
        await TaskEither<Exception, void>.Do(($) async {
          // Set process state to loading
          if (!isClosed) {
            emit(
              state.copyWith(processState: LoginOptionsProcessState.loading()),
            );
          }

          // Check if email exists
          final checkEmailData = await $(
            _checkEmailUseCase(email: event.email),
          );

          if (!checkEmailData.isExist && !isClosed) {
            // Set process state to email does not exist
            emit(
              state.copyWith(
                processState: LoginOptionsProcessState.emailDoesNotExist(),
              ),
            );
            return;
          }

          // Login with Okta if email exists
          await $(_loginWithOktaUsecase(event.email));

          if (!isClosed) {
            // Set process state to initial
            emit(
              state.copyWith(processState: LoginOptionsProcessState.initial()),
            );
          }

          // Navigate to progress tracker
          _loginNavigation.goToProgressTracker();
        }).run();

    result.fold(
      (Exception exception) {
        // todo (aakash): Handle user_cancelled error properly
        print('Operation failed: $exception');
        if (!isClosed) {
          emit(
            state.copyWith(processState: LoginOptionsProcessState.initial()),
          );
        }
      },
      (_) => <String, dynamic>{}, // Success case is handled within the Do block
    );
  }

  FutureOr<void> _navigateToSignup(
    _NavigateToSignup event,
    Emitter<LoginOptionsState> emit,
  ) {
    _loginNavigation.goToCountrySelectorPage();
  }

  FutureOr<void> _validateEmail(
    _ValidateEmail event,
    Emitter<LoginOptionsState> emit,
  ) {
    if (!isClosed) {
      InputValidator validator = InputValidator();
      final isEmailValid =
          validator.validate(event.email).email().run().isEmpty;
      emit(state.copyWith(isButtonEnabled: isEmailValid));
    }
  }

  FutureOr<void> _bottomSheetClosed(
    _BottomSheetClosed event,
    Emitter<LoginOptionsState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(processState: LoginOptionsProcessState.initial()));
    }
  }
}
