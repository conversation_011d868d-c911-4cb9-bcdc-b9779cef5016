import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/assets/assets.gen.dart' as trader;
import 'package:login/src/presentation/login_options/bloc/login_options_bloc.dart';

class ContinueButton extends StatelessWidget {
  const ContinueButton({super.key, required this.onPressed});

  final void Function() onPressed;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(DuploRadius.radius_2xl_16),
        child: BlocBuilder<LoginOptionsBloc, LoginOptionsState>(
          buildWhen:
              (previous, current) =>
                  previous.processState != current.processState ||
                  previous.isButtonEnabled != current.isButtonEnabled,
          builder: (builderContext, state) {
            return DuploButton.defaultPrimary(
              semanticsIdentifier: "login_options_continue_button",
              isDisabled: !state.isButtonEnabled,
              useFullWidth: true,
              isLoading: state.processState == LoginOptionsLoadingState(),
              title: EquitiLocalization.of(context).login_loginButtonContinue,
              trailingIcon: trader.Assets.images.continueIc.keyName,
              onTap: () {
                onPressed();
              },
            );
          },
        ),
      ),
    );
  }
}
