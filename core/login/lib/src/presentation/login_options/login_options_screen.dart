import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/assets/assets.gen.dart' as login;
import 'package:login/src/di/di_container.dart';
import 'package:login/src/presentation/login_options/widgets/continue_button.dart';
import 'package:login/src/presentation/login_options/widgets/email_options_field.dart';
import 'package:theme_manager/theme_manager.dart';

import 'bloc/login_options_bloc.dart';

class LoginOptionsScreen extends StatelessWidget {
  LoginOptionsScreen({super.key});

  final TextEditingController _emailController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    return BlocProvider(
      create: (createContext) => diContainer<LoginOptionsBloc>(),
      child: BlocConsumer<LoginOptionsBloc, LoginOptionsState>(
        buildWhen:
            (previous, current) =>
                previous.isButtonEnabled != current.isButtonEnabled,
        builder: (builderContext, state) {
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            bottomNavigationBar: ContinueButton(
              onPressed: () {
                builderContext.read<LoginOptionsBloc>().add(
                  LoginOptionsEvent.navigateToLogin(_emailController.text),
                );
              },
            ),
            body: SafeArea(
              child: Semantics(
                identifier: "login_options_scroll_view",
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          diContainer<ThemeManager>().isDarkMode
                              ? login.Assets.images.headerDark.image(
                                fit: BoxFit.fill,
                                width: MediaQuery.sizeOf(context).width,
                              )
                              : login.Assets.images.headerLight.image(
                                fit: BoxFit.fill,
                                width: MediaQuery.sizeOf(context).width,
                              ),
                          Positioned(
                            top: 110,
                            child: Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0xff0A0D121A),
                                    blurRadius: 2,
                                    spreadRadius: -1,
                                  ),
                                  BoxShadow(
                                    color: Color(0xff0A0D121A),
                                    blurRadius: 3,
                                    spreadRadius: -1,
                                  ),
                                ],
                              ),
                              child: login.Assets.images.equitiLogo.svg(),
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: DuploSpacing.spacing_xl_16,
                        ),
                        child: Column(
                          children: [
                            DuploText(
                              text: localization.login_welcomeBackOptions,
                              color: theme.text.textPrimary,
                              fontWeight: DuploFontWeight.semiBold,
                              style: DuploTextStyles.of(context).displaySm,
                            ),
                            SizedBox(height: 25),
                            EmailOptionsField(
                              emailController: _emailController,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                DuploText(
                                  textAlign: TextAlign.center,
                                  text: localization.login_dontHaveAccount,
                                  color: theme.text.textTertiary,
                                  fontWeight: DuploFontWeight.regular,
                                  style: DuploTextStyles.of(context).textSm,
                                ),
                                Semantics(
                                  identifier: "login_options_sign_up_button",
                                  child: TextButton(
                                    onPressed: () {
                                      builderContext
                                          .read<LoginOptionsBloc>()
                                          .add(
                                            LoginOptionsEvent.navigateToSignup(),
                                          );
                                    },
                                    child: DuploText(
                                      textAlign: TextAlign.center,
                                      text: localization.login_signUpHere,
                                      color: theme.button.buttonTertiaryFg,
                                      fontWeight: DuploFontWeight.semiBold,
                                      style: DuploTextStyles.of(context).textMd,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height:
                                  MediaQuery.maybeViewInsetsOf(
                                    context,
                                  )?.bottom ??
                                  60,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        listener: (BuildContext listenerContext, LoginOptionsState state) {
          if (state.processState is LoginOptionsEmailDoesNotExistState) {
            _showEmailDoesNotExistBottomSheet(listenerContext);
          }
          if (state.processState is LoginOptionsErrorState) {
            _showErrorBottomSheet(
              listenerContext,
              (state.processState as LoginOptionsErrorState).message,
            );
          }
        },
      ),
    );
  }

  void _showEmailDoesNotExistBottomSheet(BuildContext listenerContext) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      semanticsIdentifierPrimary:
          "login_options_email_does_not_exist_bottom_sheet_primary_button",
      semanticsIdentifierSecondary:
          "login_options_email_does_not_exist_bottom_sheet_secondary_button",
      primaryButtonText: localization.login_signUp,
      secondaryButtonText: localization.login_useAnotherEmail,
      onPrimaryButtonPressed:
          () => listenerContext.read<LoginOptionsBloc>().add(
            const LoginOptionsEvent.navigateToSignup(),
          ),
      onSecondaryButtonPressed: () {
        _emailController.clear();
        Navigator.of(listenerContext).pop();
        listenerContext.read<LoginOptionsBloc>().add(
          const LoginOptionsEvent.bottomSheetClosed(),
        );
      },
      title: localization.login_notRegisteredYet,
      body: localization.login_emailNotAttached,
      asset: login.Assets.images.duplicateEmail.svg(height: 160, width: 160),
      onClose: (_) {
        listenerContext.read<LoginOptionsBloc>().add(
          const LoginOptionsEvent.bottomSheetClosed(),
        );
      },
    );
  }

  void _showErrorBottomSheet(
    BuildContext listenerContext,
    String errorMessage,
  ) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      semanticsIdentifierPrimary:
          "login_options_error_bottom_sheet_primary_button",
      semanticsIdentifierSecondary:
          "login_options_error_bottom_sheet_secondary_button",
      primaryButtonText: localization.login_createNewAccount,
      secondaryButtonText: localization.login_tryAgain,
      onPrimaryButtonPressed:
          () => listenerContext.read<LoginOptionsBloc>().add(
            const LoginOptionsEvent.navigateToSignup(),
          ),
      onSecondaryButtonPressed: () {
        _emailController.clear();
        Navigator.of(listenerContext).pop();
        listenerContext.read<LoginOptionsBloc>().add(
          const LoginOptionsEvent.bottomSheetClosed(),
        );
      },
      title: localization.login_somethingWentWrong,
      body: localization.login_errorOccurred(errorMessage),
      asset: login.Assets.images.duplicateEmail.svg(height: 160, width: 160),
      onClose: (_) {
        listenerContext.read<LoginOptionsBloc>().add(
          const LoginOptionsEvent.bottomSheetClosed(),
        );
      },
    );
  }
}
