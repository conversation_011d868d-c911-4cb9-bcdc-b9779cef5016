import 'package:api_client/api_client.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:onboarding/src/data/signup_body_model/signup_body_model.dart';
import 'package:onboarding/src/data/signup_response_model/signup_response_model.dart';
import 'package:onboarding/src/domain/exceptions/sign_up_with_okta_exception/sign_up_with_okta_exception.dart';
import 'package:onboarding/src/domain/exceptions/signup_exception/signup_exception.dart';
import 'package:preferences/preferences.dart';
import 'package:prelude/prelude.dart';

class SignupRepository {
  final ApiClientBase apiClient;
  final AuthService authService;
  final EquitiPreferences equitiPreferences;
  final TokenManager tokenManager;

  const SignupRepository({
    required this.apiClient,
    required this.equitiPreferences,
    required this.tokenManager,
    required this.authService,
  });

  // todo (aakash): Delete this
  TaskEither<Exception, SignupResponseModel> signup({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String userType,
    required String businessUnitId,
    String? brokerId,
    required String countryCode,
    required String country,
  }) {
    return apiClient
        .post<Map<String, dynamic>>(
          'api/v1/users',
          data:
              SignupBodyModel(
                email: email,
                businessUnitId: businessUnitId,
                firstName: firstName,
                lastName: lastName,
                password: password,
                userType: userType,
                brokerId: brokerId,
                country: country,
                countryCode: countryCode,
              ).toJson(),
        )
        .mapLeft((error) {
          if (error is ClientException) {
            return SignupException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap(
          (response) => TaskEither.tryCatch(
            () async => SignupResponseModel.fromJson(response.data!),
            (error, stackTrace) => Exception(error),
          ),
        )
        .chainFirst(
          (signUpResult) => TaskEither.tryCatch(() async {
            final userInfo = switch (signUpResult) {
              SignupSuccess(:final data) => data,
              _ => null,
            };
            if (userInfo != null) {
              // TODO(aakash): Revisit
              // await setUserUseCase(userData: userInfo).run();
              await equitiPreferences.setValue(
                'user_id',
                userInfo.registration.userId,
              );
              await equitiPreferences.setValue(
                'user_registration_id',
                userInfo.registration.userRegistrationId,
              );

              await tokenManager.saveTokenInfo(
                userInfo.user.token.accessToken,
                userInfo.user.token.refreshToken,
                email,
              );
            }
          }, (error, stackTrace) => Exception(error)),
        );
  }

  TaskEither<Exception, AuthResult> signUpWithOkta({
    required String country,
    required String countryCode,
    required String brokerId,
    String? city,
  }) {
    return authService
        .signup(
          country: country,
          countryCode: countryCode,
          brokerId: brokerId,
          city: city,
        )
        .mapLeft((error) {
          if (error is ClientException) {
            return SignUpWithOktaException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap(
          (AuthResult authResult) => TaskEither.tryCatch(() async {
            await tokenManager.saveTokenInfo(
              authResult.accessToken,
              authResult.refreshToken!,
              authResult.userData.email,
            );
            return authResult;
          }, (error, stackTrace) => Exception(error)),
        );
  }
}
