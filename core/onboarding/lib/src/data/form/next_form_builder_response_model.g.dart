// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'next_form_builder_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NextFormBuilderResponseModel _$NextFormBuilderResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_NextFormBuilderResponseModel', json, ($checkedConvert) {
  final val = _NextFormBuilderResponseModel(
    success: $checkedConvert('success', (v) => v as bool?),
    error: $checkedConvert('error', (v) => v as String?),
    data: $checkedConvert(
      'data',
      (v) => NextFormData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$NextFormBuilderResponseModelToJson(
  _NextFormBuilderResponseModel instance,
) => <String, dynamic>{
  if (instance.success case final value?) 'success': value,
  if (instance.error case final value?) 'error': value,
  'data': instance.data.toJson(),
};

_NextFormData _$NextFormDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  '_NextFormData',
  json,
  ($checkedConvert) {
    final val = _NextFormData(
      userRegistrationId: $checkedConvert(
        'user_registration_id',
        (v) => v as String,
      ),
      registrationProgressId: $checkedConvert(
        'registration_progress_id',
        (v) => (v as num).toInt(),
      ),
      state: $checkedConvert('state', (v) => v as String?),
      progress: $checkedConvert(
        'progress',
        (v) => v == null ? null : Progress.fromJson(v as Map<String, dynamic>),
      ),
      formIds: $checkedConvert(
        'form_ids',
        (v) => (v as List<dynamic>?)?.map((e) => (e as num).toInt()).toList(),
      ),
      formData: $checkedConvert('form_data', (v) => v as String),
    );
    return val;
  },
  fieldKeyMap: const {
    'userRegistrationId': 'user_registration_id',
    'registrationProgressId': 'registration_progress_id',
    'formIds': 'form_ids',
    'formData': 'form_data',
  },
);

Map<String, dynamic> _$NextFormDataToJson(_NextFormData instance) =>
    <String, dynamic>{
      'user_registration_id': instance.userRegistrationId,
      'registration_progress_id': instance.registrationProgressId,
      if (instance.state case final value?) 'state': value,
      if (instance.progress?.toJson() case final value?) 'progress': value,
      if (instance.formIds case final value?) 'form_ids': value,
      'form_data': instance.formData,
    };

_Progress _$ProgressFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_Progress',
  json,
  ($checkedConvert) {
    final val = _Progress(
      currentFormId: $checkedConvert(
        'current_form_id',
        (v) => (v as num?)?.toInt(),
      ),
      totalForms: $checkedConvert('total_forms', (v) => (v as num?)?.toInt()),
      completedForms: $checkedConvert(
        'completed_forms',
        (v) =>
            (v as List<dynamic>?)
                ?.map((e) => CompletedForm.fromJson(e as Map<String, dynamic>))
                .toList(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'currentFormId': 'current_form_id',
    'totalForms': 'total_forms',
    'completedForms': 'completed_forms',
  },
);

Map<String, dynamic> _$ProgressToJson(_Progress instance) => <String, dynamic>{
  if (instance.currentFormId case final value?) 'current_form_id': value,
  if (instance.totalForms case final value?) 'total_forms': value,
  if (instance.completedForms?.map((e) => e.toJson()).toList()
      case final value?)
    'completed_forms': value,
};

_CompletedForm _$CompletedFormFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      '_CompletedForm',
      json,
      ($checkedConvert) {
        final val = _CompletedForm(
          formId: $checkedConvert('form_id', (v) => (v as num?)?.toInt()),
          completedAt: $checkedConvert('completed_at', (v) => v as String?),
        );
        return val;
      },
      fieldKeyMap: const {'formId': 'form_id', 'completedAt': 'completed_at'},
    );

Map<String, dynamic> _$CompletedFormToJson(_CompletedForm instance) =>
    <String, dynamic>{
      if (instance.formId case final value?) 'form_id': value,
      if (instance.completedAt case final value?) 'completed_at': value,
    };
