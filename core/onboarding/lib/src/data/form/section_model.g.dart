// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'section_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IntroductionSection _$IntroductionSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('IntroductionSection', json, ($checkedConvert) {
  final val = IntroductionSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    sectionAssetUrl: $checkedConvert(
      'sectionAssetUrl',
      (v) => SectionAssetUrl.fromJson(v as Map<String, dynamic>),
    ),
    sectionImage: $checkedConvert('sectionImage', (v) => v as String?),
    sectionName: $checkedConvert('sectionName', (v) => v as String?),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$IntroductionSectionToJson(
  IntroductionSection instance,
) => <String, dynamic>{
  'sectionID': instance.sectionID,
  'sectionTemplateType': instance.sectionTemplateType,
  'sectionTitle': instance.sectionTitle,
  if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
  if (instance.sectionDescription case final value?)
    'sectionDescription': value,
  'sectionOrder': instance.sectionOrder,
  'sectionAssetUrl': instance.sectionAssetUrl.toJson(),
  if (instance.sectionImage case final value?) 'sectionImage': value,
  if (instance.sectionName case final value?) 'sectionName': value,
  'fields': instance.fields.map((e) => e.toJson()).toList(),
  if (instance.progress?.toJson() case final value?) 'progress': value,
};

SingleSelectSection _$SingleSelectSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('SingleSelectSection', json, ($checkedConvert) {
  final val = SingleSelectSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionName: $checkedConvert('sectionName', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$SingleSelectSectionToJson(
  SingleSelectSection instance,
) => <String, dynamic>{
  'sectionID': instance.sectionID,
  'sectionTemplateType': instance.sectionTemplateType,
  'sectionName': instance.sectionName,
  if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
  if (instance.sectionDescription case final value?)
    'sectionDescription': value,
  'sectionTitle': instance.sectionTitle,
  'sectionOrder': instance.sectionOrder,
  'fields': instance.fields.map((e) => e.toJson()).toList(),
  if (instance.progress?.toJson() case final value?) 'progress': value,
};

TextInputSection _$TextInputSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('TextInputSection', json, ($checkedConvert) {
  final val = TextInputSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionName: $checkedConvert('sectionName', (v) => v as String),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$TextInputSectionToJson(TextInputSection instance) =>
    <String, dynamic>{
      'sectionID': instance.sectionID,
      'sectionTemplateType': instance.sectionTemplateType,
      'sectionName': instance.sectionName,
      'sectionTitle': instance.sectionTitle,
      if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
      if (instance.sectionDescription case final value?)
        'sectionDescription': value,
      'sectionOrder': instance.sectionOrder,
      'fields': instance.fields.map((e) => e.toJson()).toList(),
      if (instance.progress?.toJson() case final value?) 'progress': value,
    };

DatePickerSection _$DatePickerSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('DatePickerSection', json, ($checkedConvert) {
  final val = DatePickerSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionName: $checkedConvert('sectionName', (v) => v as String),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$DatePickerSectionToJson(DatePickerSection instance) =>
    <String, dynamic>{
      'sectionID': instance.sectionID,
      'sectionTemplateType': instance.sectionTemplateType,
      'sectionName': instance.sectionName,
      'sectionTitle': instance.sectionTitle,
      if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
      if (instance.sectionDescription case final value?)
        'sectionDescription': value,
      'sectionOrder': instance.sectionOrder,
      'fields': instance.fields.map((e) => e.toJson()).toList(),
      if (instance.progress?.toJson() case final value?) 'progress': value,
    };

LinkGroupSection _$LinkGroupSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('LinkGroupSection', json, ($checkedConvert) {
  final val = LinkGroupSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionName: $checkedConvert('sectionName', (v) => v as String),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$LinkGroupSectionToJson(LinkGroupSection instance) =>
    <String, dynamic>{
      'sectionID': instance.sectionID,
      'sectionTemplateType': instance.sectionTemplateType,
      'sectionName': instance.sectionName,
      'sectionTitle': instance.sectionTitle,
      if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
      if (instance.sectionDescription case final value?)
        'sectionDescription': value,
      'sectionOrder': instance.sectionOrder,
      'fields': instance.fields.map((e) => e.toJson()).toList(),
      if (instance.progress?.toJson() case final value?) 'progress': value,
    };

DropDownSelectSection _$DropDownSelectSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('DropDownSelectSection', json, ($checkedConvert) {
  final val = DropDownSelectSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionName: $checkedConvert('sectionName', (v) => v as String),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$DropDownSelectSectionToJson(
  DropDownSelectSection instance,
) => <String, dynamic>{
  'sectionID': instance.sectionID,
  'sectionTemplateType': instance.sectionTemplateType,
  'sectionName': instance.sectionName,
  'sectionTitle': instance.sectionTitle,
  if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
  if (instance.sectionDescription case final value?)
    'sectionDescription': value,
  'sectionOrder': instance.sectionOrder,
  'fields': instance.fields.map((e) => e.toJson()).toList(),
  if (instance.progress?.toJson() case final value?) 'progress': value,
};

RiskAssessmentSection _$RiskAssessmentSectionFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('RiskAssessmentSection', json, ($checkedConvert) {
  final val = RiskAssessmentSection(
    sectionID: $checkedConvert('sectionID', (v) => (v as num).toInt()),
    sectionTemplateType: $checkedConvert(
      'sectionTemplateType',
      (v) => v as String,
    ),
    sectionName: $checkedConvert('sectionName', (v) => v as String?),
    sectionTitle: $checkedConvert('sectionTitle', (v) => v as String),
    sectionInfoText: $checkedConvert('sectionInfoText', (v) => v as String?),
    sectionDescription: $checkedConvert(
      'sectionDescription',
      (v) => v as String?,
    ),
    sectionOrder: $checkedConvert('sectionOrder', (v) => (v as num).toInt()),
    sectionAssetUrl: $checkedConvert(
      'sectionAssetUrl',
      (v) => SectionAssetUrl.fromJson(v as Map<String, dynamic>),
    ),
    fields: $checkedConvert(
      'fields',
      (v) =>
          (v as List<dynamic>)
              .map((e) => FieldModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    ),
    progress: $checkedConvert(
      'progress',
      (v) =>
          v == null
              ? null
              : SectionProgress.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$RiskAssessmentSectionToJson(
  RiskAssessmentSection instance,
) => <String, dynamic>{
  'sectionID': instance.sectionID,
  'sectionTemplateType': instance.sectionTemplateType,
  if (instance.sectionName case final value?) 'sectionName': value,
  'sectionTitle': instance.sectionTitle,
  if (instance.sectionInfoText case final value?) 'sectionInfoText': value,
  if (instance.sectionDescription case final value?)
    'sectionDescription': value,
  'sectionOrder': instance.sectionOrder,
  'sectionAssetUrl': instance.sectionAssetUrl.toJson(),
  'fields': instance.fields.map((e) => e.toJson()).toList(),
  if (instance.progress?.toJson() case final value?) 'progress': value,
};

_SectionProgress _$SectionProgressFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_SectionProgress', json, ($checkedConvert) {
      final val = _SectionProgress(
        totalSectionCount: $checkedConvert(
          'totalSectionCount',
          (v) => (v as num?)?.toInt(),
        ),
        currentSectionIndex: $checkedConvert(
          'currentSectionIndex',
          (v) => (v as num?)?.toInt(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$SectionProgressToJson(
  _SectionProgress instance,
) => <String, dynamic>{
  if (instance.totalSectionCount case final value?) 'totalSectionCount': value,
  if (instance.currentSectionIndex case final value?)
    'currentSectionIndex': value,
};
