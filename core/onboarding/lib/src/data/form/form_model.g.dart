// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'form_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FormModel _$FormModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_FormModel', json, ($checkedConvert) {
      final val = _FormModel(
        formID: $checkedConvert('formID', (v) => (v as num).toInt()),
        name: $checkedConvert('name', (v) => v as String),
        description: $checkedConvert('description', (v) => v as String?),
        formVersion: $checkedConvert('formVersion', (v) => (v as num).toInt()),
        isActive: $checkedConvert('isActive', (v) => v as bool?),
        sections: $checkedConvert(
          'sections',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => SectionModel.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$FormModelToJson(_FormModel instance) =>
    <String, dynamic>{
      'formID': instance.formID,
      'name': instance.name,
      if (instance.description case final value?) 'description': value,
      'formVersion': instance.formVersion,
      if (instance.isActive case final value?) 'isActive': value,
      'sections': instance.sections.map((e) => e.toJson()).toList(),
    };
