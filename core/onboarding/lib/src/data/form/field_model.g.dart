// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'field_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ButtonPrimaryField _$ButtonPrimaryFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('ButtonPrimaryField', json, ($checkedConvert) {
      final val = ButtonPrimaryField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldName: $checkedConvert('fieldName', (v) => v as String),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        label: $checkedConvert('label', (v) => v as String?),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        initialValue: $checkedConvert('initialValue', (v) => v as bool?),
        nextSectionID: $checkedConvert(
          'nextSectionID',
          (v) => (v as num?)?.toInt(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ButtonPrimaryFieldToJson(ButtonPrimaryField instance) =>
    <String, dynamic>{
      'fieldID': instance.fieldID,
      'fieldName': instance.fieldName,
      'fieldType': instance.fieldType,
      if (instance.label case final value?) 'label': value,
      'fieldOrder': instance.fieldOrder,
      if (instance.initialValue case final value?) 'initialValue': value,
      if (instance.nextSectionID case final value?) 'nextSectionID': value,
    };

ButtonTertiaryField _$ButtonTertiaryFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('ButtonTertiaryField', json, ($checkedConvert) {
      final val = ButtonTertiaryField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        label: $checkedConvert('label', (v) => v as String),
        fieldName: $checkedConvert('fieldName', (v) => v as String?),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        fieldValue: $checkedConvert('fieldValue', (v) => v as String?),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        initialValue: $checkedConvert('initialValue', (v) => v as bool?),
        nextSectionID: $checkedConvert(
          'nextSectionID',
          (v) => (v as num?)?.toInt(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ButtonTertiaryFieldToJson(
  ButtonTertiaryField instance,
) => <String, dynamic>{
  'fieldID': instance.fieldID,
  'label': instance.label,
  if (instance.fieldName case final value?) 'fieldName': value,
  'fieldType': instance.fieldType,
  if (instance.fieldValue case final value?) 'fieldValue': value,
  'fieldOrder': instance.fieldOrder,
  if (instance.initialValue case final value?) 'initialValue': value,
  if (instance.nextSectionID case final value?) 'nextSectionID': value,
};

RadioField _$RadioFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('RadioField', json, ($checkedConvert) {
      final val = RadioField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldName: $checkedConvert('fieldName', (v) => v as String?),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        isRequired: $checkedConvert('isRequired', (v) => v as bool?),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        validations: $checkedConvert(
          'validations',
          (v) =>
              (v as List<dynamic>?)
                  ?.map(
                    (e) => ValidationModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList(),
        ),
        options: $checkedConvert(
          'options',
          (v) =>
              (v as List<dynamic>)
                  .map((e) => OptionModel.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$RadioFieldToJson(
  RadioField instance,
) => <String, dynamic>{
  'fieldID': instance.fieldID,
  if (instance.fieldName case final value?) 'fieldName': value,
  'fieldType': instance.fieldType,
  if (instance.isRequired case final value?) 'isRequired': value,
  'fieldOrder': instance.fieldOrder,
  if (instance.validations?.map((e) => e.toJson()).toList() case final value?)
    'validations': value,
  'options': instance.options.map((e) => e.toJson()).toList(),
};

TextareaField _$TextareaFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('TextareaField', json, ($checkedConvert) {
      final val = TextareaField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldName: $checkedConvert('fieldName', (v) => v as String),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        label: $checkedConvert('label', (v) => v as String?),
        placeholder: $checkedConvert('placeholder', (v) => v as String),
        helpText: $checkedConvert('helpText', (v) => v as String?),
        defaultValue: $checkedConvert('defaultValue', (v) => v as String?),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        validations: $checkedConvert(
          'validations',
          (v) =>
              (v as List<dynamic>?)
                  ?.map(
                    (e) => ValidationModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList() ??
              const [],
        ),
      );
      return val;
    });

Map<String, dynamic> _$TextareaFieldToJson(TextareaField instance) =>
    <String, dynamic>{
      'fieldID': instance.fieldID,
      'fieldName': instance.fieldName,
      'fieldType': instance.fieldType,
      if (instance.label case final value?) 'label': value,
      'placeholder': instance.placeholder,
      if (instance.helpText case final value?) 'helpText': value,
      if (instance.defaultValue case final value?) 'defaultValue': value,
      'fieldOrder': instance.fieldOrder,
      'validations': instance.validations.map((e) => e.toJson()).toList(),
    };

DatePickerField _$DatePickerFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('DatePickerField', json, ($checkedConvert) {
      final val = DatePickerField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldName: $checkedConvert('fieldName', (v) => v as String),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        fieldValue: $checkedConvert('fieldValue', (v) => v as String?),
        label: $checkedConvert('label', (v) => v as String?),
        placeholder: $checkedConvert('placeholder', (v) => v as String),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        helpText: $checkedConvert('helpText', (v) => v as String),
        nextSectionID: $checkedConvert(
          'nextSectionID',
          (v) => (v as num?)?.toInt(),
        ),
        validations: $checkedConvert(
          'validations',
          (v) =>
              (v as List<dynamic>?)
                  ?.map(
                    (e) => ValidationModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList() ??
              const [],
        ),
      );
      return val;
    });

Map<String, dynamic> _$DatePickerFieldToJson(DatePickerField instance) =>
    <String, dynamic>{
      'fieldID': instance.fieldID,
      'fieldName': instance.fieldName,
      'fieldType': instance.fieldType,
      if (instance.fieldValue case final value?) 'fieldValue': value,
      if (instance.label case final value?) 'label': value,
      'placeholder': instance.placeholder,
      'fieldOrder': instance.fieldOrder,
      'helpText': instance.helpText,
      if (instance.nextSectionID case final value?) 'nextSectionID': value,
      'validations': instance.validations.map((e) => e.toJson()).toList(),
    };

LinkGroupField _$LinkGroupFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('LinkGroupField', json, ($checkedConvert) {
      final val = LinkGroupField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldName: $checkedConvert('fieldName', (v) => v as String?),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        options: $checkedConvert(
          'options',
          (v) =>
              (v as List<dynamic>?)
                  ?.map((e) => OptionModel.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$LinkGroupFieldToJson(LinkGroupField instance) =>
    <String, dynamic>{
      'fieldID': instance.fieldID,
      if (instance.fieldName case final value?) 'fieldName': value,
      'fieldType': instance.fieldType,
      'fieldOrder': instance.fieldOrder,
      if (instance.options?.map((e) => e.toJson()).toList() case final value?)
        'options': value,
    };

DropDownField _$DropDownFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('DropDownField', json, ($checkedConvert) {
      final val = DropDownField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldName: $checkedConvert('fieldName', (v) => v as String),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        fieldValue: $checkedConvert('fieldValue', (v) => v as String?),
        placeholder: $checkedConvert('placeholder', (v) => v as String),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        label: $checkedConvert('label', (v) => v as String?),
        options: $checkedConvert(
          'options',
          (v) =>
              (v as List<dynamic>?)
                  ?.map((e) => OptionModel.fromJson(e as Map<String, dynamic>))
                  .toList(),
        ),
        validations: $checkedConvert(
          'validations',
          (v) =>
              (v as List<dynamic>?)
                  ?.map(
                    (e) => ValidationModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList() ??
              const [],
        ),
      );
      return val;
    });

Map<String, dynamic> _$DropDownFieldToJson(DropDownField instance) =>
    <String, dynamic>{
      'fieldID': instance.fieldID,
      'fieldName': instance.fieldName,
      'fieldType': instance.fieldType,
      if (instance.fieldValue case final value?) 'fieldValue': value,
      'placeholder': instance.placeholder,
      'fieldOrder': instance.fieldOrder,
      if (instance.label case final value?) 'label': value,
      if (instance.options?.map((e) => e.toJson()).toList() case final value?)
        'options': value,
      'validations': instance.validations.map((e) => e.toJson()).toList(),
    };

CheckboxField _$CheckboxFieldFromJson(Map<String, dynamic> json) =>
    $checkedCreate('CheckboxField', json, ($checkedConvert) {
      final val = CheckboxField(
        fieldID: $checkedConvert('fieldID', (v) => (v as num).toInt()),
        fieldType: $checkedConvert('fieldType', (v) => v as String),
        fieldName: $checkedConvert('fieldName', (v) => v as String?),
        fieldValue: $checkedConvert('fieldValue', (v) => v as String?),
        label: $checkedConvert('label', (v) => v as String),
        fieldOrder: $checkedConvert('fieldOrder', (v) => (v as num).toInt()),
        initialValue: $checkedConvert('initialValue', (v) => v as bool?),
        nextSectionID: $checkedConvert(
          'nextSectionID',
          (v) => (v as num?)?.toInt(),
        ),
      );
      return val;
    });

Map<String, dynamic> _$CheckboxFieldToJson(CheckboxField instance) =>
    <String, dynamic>{
      'fieldID': instance.fieldID,
      'fieldType': instance.fieldType,
      if (instance.fieldName case final value?) 'fieldName': value,
      if (instance.fieldValue case final value?) 'fieldValue': value,
      'label': instance.label,
      'fieldOrder': instance.fieldOrder,
      if (instance.initialValue case final value?) 'initialValue': value,
      if (instance.nextSectionID case final value?) 'nextSectionID': value,
    };
