import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding/src/data/progress_tracker/progress_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_sub_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_action.dart';

part 'progress_state_info.freezed.dart';
part 'progress_state_info.g.dart';

// todo (aakash): Delete this file

@freezed
sealed class ProgressStateInfo with _$ProgressStateInfo {
  factory ProgressStateInfo(
    @Json<PERSON>ey(name: "id") ProgressState state,
    @Json<PERSON>ey(name: "status") ProgressSubState subState,
    @<PERSON><PERSON><PERSON><PERSON>(name: "message") String? message,
    @<PERSON><PERSON><PERSON><PERSON>(name: "title") String? title,
  ) = _ProgressStateInfo;

  factory ProgressStateInfo.fromJson(Map<String, dynamic> json) =>
      _$ProgressStateInfoFromJson(json);
}

extension ProgressStateInfoAction on ProgressStateInfo {
  ProgressTrackerAction? primaryAction() {
    switch (subState) {
      case ProgressSubState.inProgress || ProgressSubState.pending:
        switch (state) {
          case ProgressState.personalInfo:
            return ProgressTrackerAction.completePersonalInfo;
          case ProgressState.financialInfo:
            return ProgressTrackerAction.completeFinancialInfo;
          case ProgressState.verifyIdentity:
            return ProgressTrackerAction.completeVerification;
          case ProgressState.accountCreation:
            return ProgressTrackerAction.completeAccountCreation;
          case ProgressState.addFunds:
            return ProgressTrackerAction.depositInProgress;
          case ProgressState.placeTrade:
            return ProgressTrackerAction.placeTrade;
        }
      case ProgressSubState.veificationRejected:
        return ProgressTrackerAction.verificationRejected;
      case ProgressSubState.verificationInReview:
        return ProgressTrackerAction.verificationInReview;
      case ProgressSubState.verificationUnsuccessful:
        return ProgressTrackerAction.verificationFailed;
      case ProgressSubState.completed:
      case ProgressSubState.locked:
        return null;
      case ProgressSubState.depositInProgress:
        return ProgressTrackerAction.depositInProgress;
      case ProgressSubState.depositInReview:
        return ProgressTrackerAction.depositInReview;
    }
  }
}

@freezed
class ProgressTrackerResponse with _$ProgressTrackerResponse {
  factory ProgressTrackerResponse.success({required ProgressTracker data}) =
      ProgressTrackerSuccess;

  const factory ProgressTrackerResponse.internalServerError({
    required String code,
    required String message,
  }) = ProgressTrackerInternalServerError;

  factory ProgressTrackerResponse.fromJson(Map<String, dynamic> json) {
    if (json['success'] == true) {
      return ProgressTrackerResponse.success(
        data: ProgressTracker.fromJson(json['data'] as Map<String, dynamic>),
      );
    }
    switch (json['code']) {
      default:
        return ProgressTrackerResponse.internalServerError(
          code: (json['code'] as String?) ?? 'UNKNOWN',
          message: (json['message'] as String?) ?? 'Unknown error',
        );
    }
  }
}

@freezed
sealed class ProgressTracker with _$ProgressTracker {
  factory ProgressTracker({
    required List<ProgressStateInfo> allStatesInfo,
    required int currentStateIndex,
    required bool phoneNumberAdded,
    required bool registrationStarted,
    required String userId,
    required String brokerId,
  }) = _ProgressTracker;
  factory ProgressTracker.fromJson(Map<String, dynamic> json) {
    List<ProgressStateInfo> allStatesInfo =
        (json['steps'] as List)
            .map((e) => ProgressStateInfo.fromJson(e as Map<String, dynamic>))
            .toList();

    int currentStateIndex = 0;
    final currentStateName = json['current_step'] ?? "";
    for (var element in allStatesInfo) {
      if (element.state.jsonName() == currentStateName) {
        break;
      }
      currentStateIndex++;
    }
    final tracker = ProgressTracker(
      allStatesInfo: allStatesInfo,
      currentStateIndex: currentStateIndex,
      phoneNumberAdded: json['phone_number_added'] as bool? ?? false,
      registrationStarted: json['registration_started'] as bool? ?? false,
      userId: json['user_id'] as String,
      brokerId: json['broker_id'] as String,
    );
    return tracker;
  }
}
