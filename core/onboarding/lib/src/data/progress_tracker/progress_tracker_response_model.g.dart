// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'progress_tracker_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProgressTrackerResponseModel _$ProgressTrackerResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_ProgressTrackerResponseModel', json, ($checkedConvert) {
  final val = _ProgressTrackerResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) =>
          v == null
              ? null
              : ProgressTrackerData.fromJson(v as Map<String, dynamic>),
    ),
    error: $checkedConvert(
      'error',
      (v) => v == null ? null : ErrorData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

_ErrorData _$ErrorDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ErrorData', json, ($checkedConvert) {
      final val = _ErrorData(
        errorCode: $checkedConvert('errorCode', (v) => (v as num).toInt()),
        description: $checkedConvert('description', (v) => v as String),
        fieldErrors: $checkedConvert(
          'fieldErrors',
          (v) => (v as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
              k,
              (e as List<dynamic>).map((e) => e as String).toList(),
            ),
          ),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ErrorDataToJson(_ErrorData instance) =>
    <String, dynamic>{
      'errorCode': instance.errorCode,
      'description': instance.description,
      if (instance.fieldErrors case final value?) 'fieldErrors': value,
    };

_ProgressTrackerData _$ProgressTrackerDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  '_ProgressTrackerData',
  json,
  ($checkedConvert) {
    final val = _ProgressTrackerData(
      registrationId: $checkedConvert('registration_id', (v) => v as String?),
      userId: $checkedConvert('user_id', (v) => v as String),
      brokerId: $checkedConvert('broker_id', (v) => v as String?),
      steps: $checkedConvert(
        'steps',
        (v) =>
            (v as List<dynamic>?)
                ?.map(
                  (e) =>
                      ProgressTrackerStep.fromJson(e as Map<String, dynamic>),
                )
                .toList(),
      ),
      currentStep: $checkedConvert(
        'current_step',
        (v) => $enumDecodeNullable(_$ProgressStateEnumMap, v),
      ),
      phoneNumberAdded: $checkedConvert(
        'phone_number_added',
        (v) => v as bool?,
      ),
      registrationStarted: $checkedConvert(
        'registration_started',
        (v) => v as bool,
      ),
      currentStepIndex: $checkedConvert(
        'currentStepIndex',
        (v) => (v as num?)?.toInt(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'registrationId': 'registration_id',
    'userId': 'user_id',
    'brokerId': 'broker_id',
    'currentStep': 'current_step',
    'phoneNumberAdded': 'phone_number_added',
    'registrationStarted': 'registration_started',
  },
);

Map<String, dynamic> _$ProgressTrackerDataToJson(
  _ProgressTrackerData instance,
) => <String, dynamic>{
  if (instance.registrationId case final value?) 'registration_id': value,
  'user_id': instance.userId,
  if (instance.brokerId case final value?) 'broker_id': value,
  if (instance.steps?.map((e) => e.toJson()).toList() case final value?)
    'steps': value,
  if (_$ProgressStateEnumMap[instance.currentStep] case final value?)
    'current_step': value,
  if (instance.phoneNumberAdded case final value?) 'phone_number_added': value,
  'registration_started': instance.registrationStarted,
  if (instance.currentStepIndex case final value?) 'currentStepIndex': value,
};

const _$ProgressStateEnumMap = {
  ProgressState.personalInfo: 'personal_info',
  ProgressState.financialInfo: 'financial_info',
  ProgressState.verifyIdentity: 'verify_identity',
  ProgressState.accountCreation: 'account_creation',
  ProgressState.addFunds: 'place_funds',
  ProgressState.placeTrade: 'place_trade',
};

_ProgressTrackerStep _$ProgressTrackerStepFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ProgressTrackerStep', json, ($checkedConvert) {
      final val = _ProgressTrackerStep(
        state: $checkedConvert(
          'id',
          (v) => $enumDecode(_$ProgressStateEnumMap, v),
        ),
        subState: $checkedConvert(
          'status',
          (v) => $enumDecode(_$ProgressSubStateEnumMap, v),
        ),
        message: $checkedConvert('message', (v) => v as String?),
        title: $checkedConvert('title', (v) => v as String?),
      );
      return val;
    }, fieldKeyMap: const {'state': 'id', 'subState': 'status'});

Map<String, dynamic> _$ProgressTrackerStepToJson(
  _ProgressTrackerStep instance,
) => <String, dynamic>{
  'id': _$ProgressStateEnumMap[instance.state]!,
  'status': _$ProgressSubStateEnumMap[instance.subState]!,
  if (instance.message case final value?) 'message': value,
  if (instance.title case final value?) 'title': value,
};

const _$ProgressSubStateEnumMap = {
  ProgressSubState.inProgress: 'in_progress',
  ProgressSubState.pending: 'pending',
  ProgressSubState.completed: 'completed',
  ProgressSubState.veificationRejected: 'rejected',
  ProgressSubState.locked: 'locked',
  ProgressSubState.verificationInReview: 'in_review',
  ProgressSubState.depositInReview: 'deposit_in_review',
  ProgressSubState.verificationUnsuccessful: 'unsuccessful',
  ProgressSubState.depositInProgress: 'deposit_in_progress',
};
