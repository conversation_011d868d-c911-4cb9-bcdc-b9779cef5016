import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_response_model.freezed.dart';
part 'signup_response_model.g.dart';

@Freezed(unionKey: 'status', unionValueCase: FreezedUnionCase.none)
class SignupResponseModel with _$SignupResponseModel {
  const factory SignupResponseModel.success({required UserDataModel data}) =
      SignupSuccess;

  const factory SignupResponseModel.validationError({
    required String code,
    required String message,
    required List<FieldError> errors,
  }) = SignupValidationError;

  const factory SignupResponseModel.emailExists({
    required String code,
    required String message,
  }) = SignupEmailExists;

  const factory SignupResponseModel.passwordPolicyViolation({
    required String code,
    required String message,
    required PasswordPolicyDetails details,
  }) = SignupPasswordPolicyViolation;

  const factory SignupResponseModel.internalServerError({
    required String code,
    required String message,
  }) = SignupInternalServerError;

  factory SignupResponseModel.fromJson(Map<String, dynamic> json) {
    if (json['status'] == 'success' || json['success'] == true) {
      return SignupResponseModel.success(
        data: UserDataModel.fromJson(json['data'] as Map<String, dynamic>),
      );
    }

    switch (json['code']) {
      case 'VALIDATION_ERROR':
        return SignupResponseModel.validationError(
          code: json['code'] as String,
          message: json['message'] as String,
          errors:
              (json['errors'] as List)
                  .map((e) => FieldError.fromJson(e as Map<String, dynamic>))
                  .toList(),
        );
      case 'EMAIL_EXISTS':
        return SignupResponseModel.emailExists(
          code: json['code'] as String,
          message: json['message'] as String,
        );
      case 'PASSWORD_POLICY_VIOLATION':
        return SignupResponseModel.passwordPolicyViolation(
          code: json['code'] as String,
          message: json['message'] as String,
          details: PasswordPolicyDetails.fromJson(
            json['details'] as Map<String, dynamic>,
          ),
        );
      default:
        return SignupResponseModel.internalServerError(
          code: (json['code'] as String?) ?? 'UNKNOWN',
          message: (json['message'] as String?) ?? 'Unknown error',
        );
    }
  }
}

@freezed
sealed class UserDataModel with _$UserDataModel {
  const factory UserDataModel({
    @JsonKey(name: "user") required User user,
    @JsonKey(name: "registration") required Registration registration,
  }) = _UserDataModel;

  factory UserDataModel.fromJson(Map<String, dynamic> json) =>
      _$UserDataModelFromJson(json);
}

@freezed
sealed class Registration with _$Registration {
  const factory Registration({
    @JsonKey(name: "userRegistrationId") required String userRegistrationId,
    @JsonKey(name: "userId") required String userId,
    @JsonKey(name: "userState") required String userState,
    @JsonKey(name: "applicationState") required String applicationState,
  }) = _Registration;

  factory Registration.fromJson(Map<String, dynamic> json) =>
      _$RegistrationFromJson(json);
}

@freezed
sealed class User with _$User {
  const factory User({
    @JsonKey(name: "userId") required String userId,
    @JsonKey(name: "token") required Token token,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
sealed class Token with _$Token {
  const factory Token({
    @JsonKey(name: "access_token") required String accessToken,
    @JsonKey(name: "refresh_token") required String refreshToken,
    @JsonKey(name: "expires_in") required int expiresIn,
  }) = _Token;
  factory Token.fromJson(Map<String, dynamic> json) => _$TokenFromJson(json);
}

@freezed
sealed class FieldError with _$FieldError {
  const factory FieldError({required String field, required String message}) =
      _FieldError;

  factory FieldError.fromJson(Map<String, dynamic> json) =>
      _$FieldErrorFromJson(json);
}

@freezed
sealed class PasswordPolicyDetails with _$PasswordPolicyDetails {
  const factory PasswordPolicyDetails({
    required List<String> requirements,
    required List<String> violations,
  }) = _PasswordPolicyDetails;

  factory PasswordPolicyDetails.fromJson(Map<String, dynamic> json) =>
      _$PasswordPolicyDetailsFromJson(json);
}
