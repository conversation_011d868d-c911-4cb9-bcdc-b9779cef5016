// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'submit_api_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SubmitApiResponse _$SubmitApiResponseFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_SubmitApiResponse', json, ($checkedConvert) {
      final val = _SubmitApiResponse(
        success: $checkedConvert('success', (v) => v as bool),
        data: $checkedConvert(
          'data',
          (v) => Data.fromJson(v as Map<String, dynamic>),
        ),
      );
      return val;
    });

Map<String, dynamic> _$SubmitApiResponseToJson(_SubmitApiResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data.toJson(),
    };

_Data _$DataFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_Data',
  json,
  ($checkedConvert) {
    final val = _Data(
      isComplete: $checkedConvert('is_complete', (v) => v as bool),
      nextForm: $checkedConvert(
        'next_form',
        (v) => NextForm.fromJson(v as Map<String, dynamic>),
      ),
      progress: $checkedConvert(
        'progress',
        (v) => Progress.fromJson(v as Map<String, dynamic>),
      ),
    );
    return val;
  },
  fieldKeyMap: const {'isComplete': 'is_complete', 'nextForm': 'next_form'},
);

Map<String, dynamic> _$DataToJson(_Data instance) => <String, dynamic>{
  'is_complete': instance.isComplete,
  'next_form': instance.nextForm.toJson(),
  'progress': instance.progress.toJson(),
};

_NextForm _$NextFormFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_NextForm', json, ($checkedConvert) {
      final val = _NextForm(
        available: $checkedConvert('available', (v) => v as bool),
        formId: $checkedConvert('form_id', (v) => (v as num?)?.toInt()),
      );
      return val;
    }, fieldKeyMap: const {'formId': 'form_id'});

Map<String, dynamic> _$NextFormToJson(_NextForm instance) => <String, dynamic>{
  'available': instance.available,
  if (instance.formId case final value?) 'form_id': value,
};

_Progress _$ProgressFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_Progress',
  json,
  ($checkedConvert) {
    final val = _Progress(
      currentFormId: $checkedConvert(
        'current_form_id',
        (v) => (v as num?)?.toInt(),
      ),
      totalForms: $checkedConvert('total_forms', (v) => (v as num).toInt()),
      completedForms: $checkedConvert(
        'completed_forms',
        (v) =>
            (v as List<dynamic>)
                .map((e) => CompletedForm.fromJson(e as Map<String, dynamic>))
                .toList(),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'currentFormId': 'current_form_id',
    'totalForms': 'total_forms',
    'completedForms': 'completed_forms',
  },
);

Map<String, dynamic> _$ProgressToJson(_Progress instance) => <String, dynamic>{
  if (instance.currentFormId case final value?) 'current_form_id': value,
  'total_forms': instance.totalForms,
  'completed_forms': instance.completedForms.map((e) => e.toJson()).toList(),
};

_CompletedForm _$CompletedFormFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      '_CompletedForm',
      json,
      ($checkedConvert) {
        final val = _CompletedForm(
          formId: $checkedConvert('form_id', (v) => (v as num).toInt()),
          completedAt: $checkedConvert('completed_at', (v) => v as String),
        );
        return val;
      },
      fieldKeyMap: const {'formId': 'form_id', 'completedAt': 'completed_at'},
    );

Map<String, dynamic> _$CompletedFormToJson(_CompletedForm instance) =>
    <String, dynamic>{
      'form_id': instance.formId,
      'completed_at': instance.completedAt,
    };
