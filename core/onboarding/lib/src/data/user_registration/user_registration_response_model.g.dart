// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_registration_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserRegistrationResponseModel _$UserRegistrationResponseModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_UserRegistrationResponseModel', json, ($checkedConvert) {
  final val = _UserRegistrationResponseModel(
    success: $checkedConvert('success', (v) => v as bool),
    data: $checkedConvert(
      'data',
      (v) =>
          v == null
              ? null
              : UserRegistrationData.fromJson(v as Map<String, dynamic>),
    ),
    error: $checkedConvert(
      'error',
      (v) => v == null ? null : ErrorData.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

_ErrorData _$ErrorDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_ErrorData', json, ($checkedConvert) {
      final val = _ErrorData(
        errorCode: $checkedConvert('errorCode', (v) => (v as num).toInt()),
        description: $checkedConvert('description', (v) => v as String),
        fieldErrors: $checkedConvert(
          'fieldErrors',
          (v) => (v as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
              k,
              (e as List<dynamic>).map((e) => e as String).toList(),
            ),
          ),
        ),
      );
      return val;
    });

Map<String, dynamic> _$ErrorDataToJson(_ErrorData instance) =>
    <String, dynamic>{
      'errorCode': instance.errorCode,
      'description': instance.description,
      if (instance.fieldErrors case final value?) 'fieldErrors': value,
    };

_UserRegistrationData _$UserRegistrationDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_UserRegistrationData', json, ($checkedConvert) {
  final val = _UserRegistrationData(
    registration: $checkedConvert(
      'registration',
      (v) => Registration.fromJson(v as Map<String, dynamic>),
    ),
  );
  return val;
});

Map<String, dynamic> _$UserRegistrationDataToJson(
  _UserRegistrationData instance,
) => <String, dynamic>{'registration': instance.registration.toJson()};

_Registration _$RegistrationFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_Registration', json, ($checkedConvert) {
      final val = _Registration(
        userRegistrationId: $checkedConvert(
          'userRegistrationId',
          (v) => v as String,
        ),
        userId: $checkedConvert('userId', (v) => v as String),
        userState: $checkedConvert('userState', (v) => v as String),
        applicationState: $checkedConvert(
          'applicationState',
          (v) => v as String,
        ),
      );
      return val;
    });

Map<String, dynamic> _$RegistrationToJson(_Registration instance) =>
    <String, dynamic>{
      'userRegistrationId': instance.userRegistrationId,
      'userId': instance.userId,
      'userState': instance.userState,
      'applicationState': instance.applicationState,
    };
