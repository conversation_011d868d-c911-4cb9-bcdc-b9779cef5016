import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:domain/domain.dart';
import 'package:onboarding/src/domain/usecase/get_selected_country_usecase.dart';
import 'package:onboarding/src/domain/usecase/set_selected_city_usecase.dart';
import 'package:onboarding/src/navigation/arguments/signup_options_args.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

part 'city_bloc.freezed.dart';
part 'city_event.dart';
part 'city_state.dart';

@injectable
class CityBloc extends Bloc<CityEvent, CityState> {
  final OnboardingNavigation _onboardingNavigation;
  final SetSelectedCityUseCase _setSelectedCityUseCase;
  final GetSelectedCountryUseCase _getSelectedCountryUseCase;

  CityBloc(
    this._onboardingNavigation,
    this._setSelectedCityUseCase,
    this._getSelectedCountryUseCase,
  ) : super(const CityState()) {
    on<OnInitEvent>(_onInit);
    on<SelectCityEvent>(_onSelectCity);
    on<OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
    add(OnInitEvent());
  }

  Future<void> _onConfirmButtonPressed(
    OnConfirmButtonPressedEvent event,
    Emitter<CityState> emit,
  ) async {
    final selectedCity = event.selectedCity;
    if (selectedCity == null) {
      emit(state.copyWith(currentState: CityProcessState.error()));
      return;
    }

    // Get the selected country data to extract required parameters
    final selectedCountryResult = await _getSelectedCountryUseCase().run();
    final selectedCountry = selectedCountryResult.fold((l) {
      if (!isClosed) {
        emit(state.copyWith(currentState: CityProcessState.error()));
        addError(l);
      }
    }, (r) => r);

    if (selectedCountry == null && !isClosed) {
      emit(state.copyWith(currentState: CityProcessState.error()));
      return;
    }

    final setSelectedCity =
        await _setSelectedCityUseCase(city: selectedCity).run();
    setSelectedCity.fold(
      (l) {
        if (!isClosed) {
          emit(state.copyWith(currentState: CityProcessState.error()));
          addError(l);
        }
      },
      (r) {
        if (!isClosed) {
          emit(state.copyWith(currentState: CityProcessState.success()));
        }

        // Create SignupOptionsArgs with all required parameters
        final signupArgs = SignupOptionsArgs(
          country: selectedCountry!.name,
          countryCode: selectedCountry.code,
          // todo (aakash): Set default broker id
          brokerId: selectedCountry.brokerId ?? "",
          city: selectedCity.name,
        );

        _onboardingNavigation.goToSignupOptions(args: signupArgs);
      },
    );
  }

  FutureOr<void> _onSelectCity(SelectCityEvent event, Emitter<CityState> emit) {
    emit(state.copyWith(selectedIndex: event.selectedIndex));
  }

  Future<void> _onInit(OnInitEvent event, Emitter<CityState> emit) async {
    emit(state.copyWith(currentState: CityProcessState.loading()));
    final selectedCountry = await _getSelectedCountryUseCase().run();
    selectedCountry.fold(
      (l) {
        if (!isClosed) {
          emit(state.copyWith(currentState: CityProcessState.error()));
          addError(l);
        }
      },
      (r) {
        if (!isClosed) {
          emit(
            state.copyWith(
              currentState: CityProcessState.success(),
              cities: r!.cities,
            ),
          );
        }
      },
    );
  }
}
