import 'package:duplo/src/components/loading_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/morph_form_builder_bloc.dart';
import 'package:onboarding/src/presentation/morph_form_builder/widgets/morph_form_builder_page_view.dart';

class MorphFormBuilder extends StatelessWidget {
  const MorphFormBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MorphFormBuilderBloc>(
      create: (ctx) => diContainer<MorphFormBuilderBloc>(),
      child: BlocBuilder<MorphFormBuilderBloc, MorphFormBuilderState>(
        buildWhen:
            (previous, current) =>
                previous.currentState != current.currentState ||
                previous.formModel != current.formModel,
        builder:
            (ctx, state) => switch (state.currentState) {
              Loading() => LoadingView(),
              Success() => MorphFormBuilderPageView(
                sections: state.formModel?.sections ?? [],
              ),
              Error() => Scaffold(
                // TODO (Aakash, Sambhav): Check how to properly handle this
                body: Center(child: Text('Error loading form')),
              ),
              Redirecting() => Scaffold(
                body: Center(
                  child: Text(
                    'Form Submitted\nRedirecting to next page...',
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            },
      ),
    );
  }
}
