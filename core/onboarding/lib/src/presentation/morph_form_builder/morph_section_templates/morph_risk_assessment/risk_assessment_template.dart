// ignore_for_file: prefer-single-widget-per-file

import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/data/form/section_asset_url.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/cubits/section_cubit.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_checkbox_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_info_text.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_link_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_primary_button_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_description.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_title.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_tertiary_button_field.dart';
import 'package:url_launcher/url_launcher.dart';

class RiskAssessmentTemplate extends StatefulWidget {
  const RiskAssessmentTemplate({
    super.key,
    required this.title,
    this.description,
    required this.primaryButtonText,
    required this.tertiaryButtonText,
    required this.onPrimaryButtonTap,
    required this.onTertiaryButtonTap,
    required this.checkboxField,
    required this.linkGroup,
    required this.sectionAssetUrl,
    required this.buttonField,
    required this.tertiaryButtonField,
    required this.onBackButtonTap,
    this.infoText,
  });
  final String title;
  final String? description, infoText;
  final String primaryButtonText;
  final String tertiaryButtonText;
  final VoidCallback onPrimaryButtonTap;
  final VoidCallback onTertiaryButtonTap;
  final CheckboxField? checkboxField;
  final ButtonPrimaryField? buttonField;
  final ButtonTertiaryField? tertiaryButtonField;
  final LinkGroupField? linkGroup;
  final SectionAssetUrl sectionAssetUrl;
  final VoidCallback onBackButtonTap;

  @override
  State<RiskAssessmentTemplate> createState() => _RiskAssessmentTemplateState();
}

class _RiskAssessmentTemplateState extends State<RiskAssessmentTemplate> {
  final ScrollController _controller = ScrollController();

  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final sectionCubit = context.read<SectionCubit>();
    final buttonFieldCubit =
        sectionCubit.fieldsMapCubit[widget.buttonField?.fieldID];
    final tertiaryButtonFieldCubit =
        sectionCubit.fieldsMapCubit[widget.tertiaryButtonField?.fieldID];
    final checkboxFieldCubit =
        sectionCubit.fieldsMapCubit[widget.checkboxField?.fieldID];
    if (checkboxFieldCubit == null) {
      buttonFieldCubit?.setValue(true);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Center(
                child: DuploLottieView.network(
                  lightUrl: widget.sectionAssetUrl.light ?? '',
                  darkUrl: widget.sectionAssetUrl.dark ?? '',
                  fit: BoxFit.fitWidth,
                ),
              ),
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(DuploSpacing.spacing_xs_4),
                  child: IconTheme(
                    data: IconTheme.of(context).copyWith(
                      color: context.duploTheme.foreground.fgSecondary,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: widget.onBackButtonTap,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          flex: 7,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DuploSpacing.spacing_xl_16,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Scrollbar(
                    trackVisibility: true,
                    thumbVisibility: true,
                    controller: _controller,
                    child: Semantics(
                      identifier: "${widget.title}_section_scroll_view",
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.only(
                            right: DuploSpacing.spacing_md_8,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 32),
                              MorphSectionTitle(text: widget.title),
                              const SizedBox(height: 8),
                              if (widget.description != null) ...[
                                MorphSectionDescription(
                                  text: widget.description,
                                ),
                                const SizedBox(height: 24),
                              ],
                              if (checkboxFieldCubit != null)
                                MorphCheckboxField(
                                  field:
                                      checkboxFieldCubit as BooleanFieldCubit,
                                  checkboxField: widget.checkboxField,
                                  onChanged: (value) {
                                    buttonFieldCubit?.setValue(value);
                                  },
                                ),
                              Container(
                                height: 1,
                                margin: EdgeInsets.symmetric(
                                  vertical: DuploSpacing.spacing_xl_16,
                                  horizontal: DuploSpacing.spacing_md_8,
                                ),
                                decoration: BoxDecoration(
                                  color: theme.border.borderSecondary,
                                ),
                              ),
                              Semantics(
                                identifier:
                                    "${widget.title}_section_scroll_view",
                                child: ListView.separated(
                                  itemCount:
                                      widget.linkGroup?.options?.length ?? 0,
                                  controller: _controller,
                                  shrinkWrap: true,
                                  separatorBuilder:
                                      (ctx, index) => Container(
                                        height: 1,
                                        margin: EdgeInsets.symmetric(
                                          horizontal:
                                              DuploSpacing.spacing_xl_16,
                                          vertical: DuploSpacing.spacing_xs_4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: theme.border.borderSecondary,
                                        ),
                                      ),
                                  itemBuilder: (ctx, index) {
                                    final item = widget.linkGroup?.options
                                        ?.elementAtOrNull(index);
                                    return item == null
                                        ? SizedBox.shrink()
                                        : MorphLinkField(
                                          item: item,
                                          onTap: () async {
                                            final url =
                                                item.url ??
                                                'https://equiti.com';
                                            if (!await launchUrl(
                                              Uri.parse(url),
                                            )) {
                                              log('Could not launch $url');
                                            }
                                          },
                                        );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                if (widget.infoText != null)
                  MorphInfoText(
                    text: widget.infoText!,
                    bottomPadding: DuploSpacing.spacing_xl_16,
                  ),
                MorphPrimaryButtonField(
                  field: buttonFieldCubit as BooleanFieldCubit,
                  buttonText: widget.primaryButtonText,
                  onButtonTap: () => widget.onPrimaryButtonTap(),
                ),
                SizedBox(height: 8),
                if (tertiaryButtonFieldCubit != null)
                  MorphTertiaryButtonField(
                    field: tertiaryButtonFieldCubit as BooleanFieldCubit,
                    buttonText: widget.tertiaryButtonText,
                    onButtonTap: () => widget.onTertiaryButtonTap(),
                  ),
                SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
