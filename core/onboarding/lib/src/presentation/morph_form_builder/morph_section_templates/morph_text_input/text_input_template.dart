// ignore_for_file: prefer-single-widget-per-file

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/cubits/section_cubit.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_info_text.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_primary_button_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_description.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_title.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_text_field.dart';

class TextInputTemplate extends StatefulWidget {
  const TextInputTemplate({
    super.key,
    required this.title,
    this.description,
    required this.textFields,
    required this.buttonText,
    required this.onButtonTap,
    required this.buttonField,
    this.infoText,
  });
  final String title;
  final String? description, infoText;
  final List<TextareaField> textFields;
  final ButtonPrimaryField buttonField;
  final String buttonText;
  final VoidCallback onButtonTap;

  @override
  State<TextInputTemplate> createState() => _TextInputTemplateState();
}

class _TextInputTemplateState extends State<TextInputTemplate> {
  final ScrollController _controller = ScrollController();

  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sectionCubit = context.read<SectionCubit>();
    final buttonFieldCubit =
        sectionCubit.fieldsMapCubit[widget.buttonField.fieldID]
            as BooleanFieldCubit;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MorphSectionTitle(text: widget.title),
          const SizedBox(height: 8),
          if (widget.description != null) ...[
            MorphSectionDescription(text: widget.description),
            const SizedBox(height: 24),
          ],
          Expanded(
            child: Scrollbar(
              trackVisibility: true,
              thumbVisibility: true,
              controller: _controller,
              child: Padding(
                padding: const EdgeInsets.only(
                  right: DuploSpacing.spacing_md_8,
                ),
                child: Semantics(
                  identifier: "text_input_template_scroll_view",
                  child: SingleChildScrollView(
                    controller: _controller,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        for (int i = 0; i < widget.textFields.length; i++)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: MorphTextField(
                              field:
                                  sectionCubit.fieldsMapCubit[widget.textFields
                                          .elementAtOrNull(i)
                                          ?.fieldID]
                                      as TextFieldCubit,
                              label:
                                  widget.textFields.elementAtOrNull(i)?.label ??
                                  '',
                              placeholder:
                                  widget.textFields
                                      .elementAtOrNull(i)
                                      ?.placeholder ??
                                  '',
                              initialValue:
                                  widget.textFields
                                      .elementAtOrNull(i)
                                      ?.defaultValue,
                              onChanged: (value) {
                                (buttonFieldCubit).setValue(
                                  sectionCubit.validate(),
                                );
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (widget.infoText != null)
            MorphInfoText(
              text: widget.infoText!,
              bottomPadding: DuploSpacing.spacing_xl_16,
            ),
          MorphPrimaryButtonField(
            field: buttonFieldCubit,
            buttonText: widget.buttonText,
            onButtonTap: () => widget.onButtonTap(),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
