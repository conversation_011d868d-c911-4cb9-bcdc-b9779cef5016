// ignore_for_file: prefer-single-widget-per-file

import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/cubits/section_cubit.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_info_text.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_link_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_primary_button_field.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_description.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_field_widgets/morph_section_title.dart';
import 'package:url_launcher/url_launcher.dart';

class LinkGroupTemplate extends StatefulWidget {
  const LinkGroupTemplate({
    super.key,
    required this.title,
    this.description,
    required this.linkGroup,
    required this.buttonText,
    required this.onButtonTap,
    required this.buttonField,
    this.infoText,
  });
  final String title;
  final String? description, infoText;
  final LinkGroupField? linkGroup;
  final String buttonText;
  final VoidCallback onButtonTap;
  final ButtonPrimaryField? buttonField;

  @override
  State<LinkGroupTemplate> createState() => _LinkGroupTemplateState();
}

class _LinkGroupTemplateState extends State<LinkGroupTemplate> {
  ScrollController _controller = ScrollController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sectionCubit = context.read<SectionCubit>();
    final buttonFieldCubit =
        sectionCubit.fieldsMapCubit[widget.buttonField?.fieldID]
            as BooleanFieldCubit;
    final theme = DuploTheme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MorphSectionTitle(text: widget.title),
          const SizedBox(height: 8),
          if (widget.description != null) ...[
            MorphSectionDescription(text: widget.description),
            const SizedBox(height: 24),
          ],
          Expanded(
            child: Scrollbar(
              thumbVisibility: true,
              trackVisibility: true,
              controller: _controller,
              child: Semantics(
                identifier: "Link_group_scroll_view",
                child: ListView.separated(
                  controller: _controller,
                  itemCount: widget.linkGroup?.options?.length ?? 0,
                  padding: EdgeInsets.only(right: 8),
                  separatorBuilder:
                      (ctx, index) => Container(
                        height: 1,
                        margin: EdgeInsets.symmetric(
                          horizontal: DuploSpacing.spacing_xl_16,
                          vertical: DuploSpacing.spacing_xs_4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.border.borderSecondary,
                        ),
                      ),
                  itemBuilder: (ctx, index) {
                    final item = widget.linkGroup?.options?.elementAtOrNull(
                      index,
                    );
                    return item == null
                        ? SizedBox.shrink()
                        : MorphLinkField(
                          item: item,
                          onTap: () async {
                            final url = item.url ?? 'https://equiti.com';
                            if (!await launchUrl(Uri.parse(url))) {
                              log('Could not launch $url');
                            }
                          },
                        );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (widget.infoText != null)
            MorphInfoText(
              text: widget.infoText!,
              bottomPadding: DuploSpacing.spacing_xl_16,
            ),
          MorphPrimaryButtonField(
            field: buttonFieldCubit,
            buttonText: widget.buttonText,
            onButtonTap: () => widget.onButtonTap(),
            isDisabled: false,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
