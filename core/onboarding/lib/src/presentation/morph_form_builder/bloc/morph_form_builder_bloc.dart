import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/data/form/form_model.dart';
import 'package:onboarding/src/data/form/next_form_builder_response_model.dart';
import 'package:onboarding/src/data/form/option_model.dart';
import 'package:onboarding/src/data/form/section_model.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_response_model.dart';
import 'package:onboarding/src/data/submit_api_request/submit_api_request.dart';
import 'package:onboarding/src/domain/exceptions/get_next_form_exception/get_next_form_exception.dart';
import 'package:onboarding/src/domain/exceptions/get_progress_tracker_exception/get_progress_tracker_exception.dart';
import 'package:onboarding/src/domain/exceptions/post_submit_form_exception/post_submit_form_exception.dart';
import 'package:onboarding/src/domain/usecase/get_next_form_model_use_case.dart';
import 'package:onboarding/src/domain/usecase/post_submit_form_use_case.dart';
import 'package:onboarding/src/domain/usecase/progress_tracker_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/cubits/section_cubit.dart';
import 'package:onboarding/src/utils/decider.dart';
import 'package:prelude/prelude.dart';
import 'package:user_account/user_account.dart';

part 'morph_form_builder_bloc.freezed.dart';
part 'morph_form_builder_event.dart';
part 'morph_form_builder_state.dart';

class MorphFormBuilderBloc
    extends Bloc<MorphFormBuilderEvent, MorphFormBuilderState> {
  MorphFormBuilderBloc({
    required ProgressTrackerUseCase progressTrackerUseCase,
    required GetNextFormModelUseCase getFormModelUseCase,
    required LoggerBase logger,
    required PostSubmitFormUseCase postSubmitFormUseCase,
    required OnboardingNavigation onboardingNavigation,
    required GetUserRegistrationIdUseCase getUserRegistrationIdUseCase,
    ProgressTrackerData? progressTracker,
  }) : _logger = logger,
       _progressTracker = progressTracker,
       _onboardingNavigation = onboardingNavigation,
       _getUserRegistrationIdUseCase = getUserRegistrationIdUseCase,
       _postSubmitFormUseCase = postSubmitFormUseCase,
       _getFormModelUseCase = getFormModelUseCase,
       _progressTrackerUseCase = progressTrackerUseCase,
       super(const MorphFormBuilderState()) {
    on<_ProgressTracker>(_getProgressTracker);
    on<_NextForm>(_getNextForm);
    on<_CreateSectionCubit>(_createSectionCubit);
    on<_RemoveSectionCubit>(_removeSectionCubit);
    on<_OnSubmitPressed>(_onSubmitPressed);
    add(const MorphFormBuilderEvent.getProgressTracker());
  }

  final ProgressTrackerUseCase _progressTrackerUseCase;
  final GetNextFormModelUseCase _getFormModelUseCase;
  final PostSubmitFormUseCase _postSubmitFormUseCase;
  final GetUserRegistrationIdUseCase _getUserRegistrationIdUseCase;
  final OnboardingNavigation _onboardingNavigation;
  final ProgressTrackerData? _progressTracker;
  final LoggerBase _logger;

  Future<void> _getProgressTracker(
    _ProgressTracker event,
    Emitter<MorphFormBuilderState> emit,
  ) async {
    emit(state.copyWith(currentState: MorphFormBuilderProcessState.loading()));
    final localProgressTracker = await _getProgressTrackerData(emit);
    if (localProgressTracker == null) {
      if (!isClosed)
        emit(
          state.copyWith(currentState: MorphFormBuilderProcessState.error()),
        );
      return;
    }
    final deciderResult = Decider.decide(
      progressTracker: localProgressTracker,
      onboardingNavigation: _onboardingNavigation,
    );
    if (deciderResult) {
      if (!isClosed) add(const MorphFormBuilderEvent.getNextFrom());
    } else {
      // emit(state.copyWith(
      //   currentState: MorphFormBuilderProcessState.redirecting(),
      // ));
      log('Form builder not required navigating to destination page');
    }
  }

  Future<void> _getNextForm(
    _NextForm _,
    Emitter<MorphFormBuilderState> emit,
  ) async {
    emit(state.copyWith(currentState: MorphFormBuilderProcessState.loading()));

    final getUserRegistrationId = _getUserRegistrationIdFromPreferences();
    if (getUserRegistrationId == null) {
      if (!isClosed)
        emit(
          state.copyWith(currentState: MorphFormBuilderProcessState.error()),
        );
      return null;
    }

    final result =
        await _getFormModelUseCase(
          userRegistrationId: getUserRegistrationId,
        ).run();
    result.fold(
      (error) {
        //todo: handle error properly
        if (error is GetNextFormException) {
          switch (error) {
            case GetNextFormUnknownError():
              emit(
                state.copyWith(
                  currentState: MorphFormBuilderProcessState.error(),
                ),
              );
          }
        } else {
          emit(
            state.copyWith(currentState: MorphFormBuilderProcessState.error()),
          );
        }
        _logger.logError('Error loading form: $error');
        addError(error);
      },
      (NextFormData nextFormData) {
        final jsonData = jsonDecode(nextFormData.formData);
        final formModel = FormModel.fromJson(jsonData as Map<String, dynamic>);
        log(
          'Next form loaded successfully ✅ \nData is ${nextFormData.toJson()}',
        );

        add(
          MorphFormBuilderEvent.createSectionCubit(
            section: formModel.sections.firstOrNull,
            isInitial: true,
          ),
        );
        emit(
          state.copyWith(
            formModel: formModel,
            nextFormData: nextFormData,
            currentState: MorphFormBuilderProcessState.success(),
          ),
        );
        log('Form model loaded successfully ✅ ');
      },
    );
  }

  void _createSectionCubit(
    _CreateSectionCubit event,
    Emitter<MorphFormBuilderState> emit,
  ) {
    if (event.section == null) {
      _logger.logError('Section is null, cannot create SectionCubit');
      return;
    }
    if (event.isInitial) {
      log('existing section cubit map existing refreshing it.... ※');
    }
    log('checking section cubit for section: ${event.section!.sectionID}');
    Map<int, SectionCubit>? existingSectionCubitMap =
        event.isInitial ? ({}) : {...?state.sectionCubitMap};
    if (!existingSectionCubitMap.containsKey((event.section)!.sectionID)) {
      final sectionCubit = SectionCubit(event.section!);
      existingSectionCubitMap[(event.section)!.sectionID] = sectionCubit;
      emit(state.copyWith(sectionCubitMap: existingSectionCubitMap));
      log('SectionCubit created for section: ${event.section!.sectionID} ✅');
    } else {
      log(
        'SectionCubit for section: ${event.section!.sectionID} already exists ❌',
      );
    }
    if (event.isInitial) {
      log('Section cubit map refreshed ${state.sectionCubitMap?.length ?? -1}');
    }
  }

  void _removeSectionCubit(
    _RemoveSectionCubit event,
    Emitter<MorphFormBuilderState> emit,
  ) {
    if (event.section == null) {
      _logger.logError('Section is null, cannot remove SectionCubit');
      return;
    }
    log('checking section cubit for section: ${event.section!.sectionID}');
    var existingSectionCubitMap = {...?state.sectionCubitMap};
    if (existingSectionCubitMap.containsKey((event.section)!.sectionID)) {
      existingSectionCubitMap.remove((event.section)!.sectionID);
      emit(state.copyWith(sectionCubitMap: existingSectionCubitMap));
      log('SectionCubit removed for section: ${event.section!.sectionID} ✅');
    } else {
      log(
        'SectionCubit for section: ${event.section!.sectionID} does not exist ❌',
      );
    }
  }

  Future<void> _onSubmitPressed(
    _OnSubmitPressed event,
    Emitter<MorphFormBuilderState> emit,
  ) async {
    final Map<String, dynamic> formData = {};
    final List<Map<String, dynamic>> sectionDataList = [];

    state.sectionCubitMap?.forEach((int mapKey, SectionCubit value) {
      List<Map<String, dynamic>> fieldDataList = [];
      value.fieldsMapCubit
      // ignore: avoid-dynamic
      .forEach((int key, FieldCubit<dynamic, dynamic> field) {
        final Map<String, dynamic> fieldsData = {};
        final formField = state.formModel?.sections
            // ignore: avoid-unsafe-collection-methods
            .firstWhere((section) => section.sectionID == mapKey)
            .fields
            // ignore: avoid-unsafe-collection-methods
            .firstWhere((localFormField) => localFormField.fieldID == key);
        if (formField is ButtonPrimaryField ||
            formField is ButtonTertiaryField) {
          return;
        }
        if (formField is DatePickerField) {
          fieldsData['field_id'] = key;
          // todo (aakash) : Think of a better way to handle this case
          fieldsData['value'] = field.state.value.toString().removeSpaces();
          fieldsData['field_name'] = formField.fieldName;
          fieldDataList.add(fieldsData);
        } else {
          fieldsData['field_id'] = key;
          fieldsData['value'] = _getValue(field.state.value);
          if (_getOptionId(field.state.value) != null) {
            fieldsData['option_id'] = _getOptionId(field.state.value);
          }
          fieldsData['field_name'] = formField?.fieldName;
          fieldDataList.add(fieldsData);
        }
      });
      Map<String, dynamic> sectionData = {};

      sectionData['section_id'] = mapKey;
      sectionData['fields'] = fieldDataList;
      sectionDataList.add(sectionData);
    });

    formData['form_id'] = state.formModel?.formID;
    formData['sections'] = sectionDataList;

    log(
      'Form submission data: ${formData.length} \nData: ${jsonEncode(formData)}',
    );

    emit(state.copyWith(currentState: MorphFormBuilderProcessState.loading()));
    final result =
        await _postSubmitFormUseCase(
          data: SubmitApiRequest(
            userRegistrationId: state.nextFormData?.userRegistrationId ?? '',
            registrationProgressId:
                state.nextFormData?.registrationProgressId ?? 0,
            formData: jsonEncode(formData),
          ),
        ).run();
    result.fold(
      (error) {
        //todo: handle error properly
        if (error is PostSubmitFormException) {
          switch (error) {
            case PostSubmitFormUnknownError():
              emit(
                state.copyWith(
                  currentState: MorphFormBuilderProcessState.error(),
                ),
              );
          }
        } else {
          emit(
            state.copyWith(currentState: MorphFormBuilderProcessState.error()),
          );
        }
        log('Error submitting form: $error');
      },
      (response) {
        log('Form submitted successfully ✅ \nResponse: $response');
        add(const MorphFormBuilderEvent.getProgressTracker());
      },
    );
  }

  // ignore: avoid-dynamic
  dynamic _getValue(dynamic value) {
    if (value == null) {
      return null;
    } else if (value is String ||
        value is int ||
        value is bool ||
        value is double) {
      return value;
    } else if (value is OptionModel) {
      return value.optionValue;
    } else if (value is Map<String, dynamic>) {
      return value['optionValue'] ?? '';
    }
    return value;
  }

  // ignore: avoid-dynamic
  dynamic _getOptionId(dynamic value) {
    if (value is OptionModel) {
      return value.optionID;
    }
    return null;
  }

  Future<ProgressTrackerData?> _getProgressTrackerData(
    Emitter<MorphFormBuilderState> emit,
  ) async {
    ProgressTrackerData? progressTrackerValue = this._progressTracker;
    if (progressTrackerValue != null) {
      return progressTrackerValue;
    }

    final response = await _progressTrackerUseCase.fetchCurrentProgress().run();
    await response.fold(
      (exception) {
        //todo: handle error properly
        if (exception is GetProgressTrackerException) {
          switch (exception) {
            case GetProgressTrackerUnknownError():
              emit(
                state.copyWith(
                  currentState: MorphFormBuilderProcessState.error(),
                ),
              );
          }
        } else {
          log('Error fetching progress: $exception');
          emit(
            state.copyWith(currentState: MorphFormBuilderProcessState.error()),
          );
        }
      },
      (ProgressTrackerData progressTrackerData) {
        log(
          'Progress tracker loaded successfully ✅ \nData is $progressTrackerData',
        );
        progressTrackerValue = progressTrackerData;
      },
    );
    return progressTrackerValue;
  }

  String? _getUserRegistrationIdFromPreferences() {
    return _getUserRegistrationIdUseCase();
  }
}
