import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:leancode_forms/leancode_forms.dart';

class MorphTextField extends StatefulWidget {
  const MorphTextField({
    super.key,
    required this.field,
    required this.label,
    required this.placeholder,
    this.initialValue,
    required this.onChanged,
  });
  final TextFieldCubit? field;
  final String label;
  final String placeholder;
  final String? initialValue;
  final void Function(String) onChanged;

  @override
  State<MorphTextField> createState() => _MorphTextFieldState();
}

class _MorphTextFieldState extends State<MorphTextField> {
  final focusNode = FocusNode();
  final textController = TextEditingController();
  @override
  void initState() {
    super.initState();
    // Initialize the text controller with the existing value if any
    if (widget.field != null) {
      textController.text = widget.field?.state.value ?? "";
    }
    _listenFocusNode();
  }

  @override
  void dispose() {
    focusNode.removeListener(_listenFocusNode);
    focusNode.dispose();
    textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.field == null
        ? SizedBox()
        : FieldBuilder(
          field: widget.field!,
          builder: (ctx, state) {
            return DuploTextField(
              semanticsIdentifier: "text_field",
              controller: textController,
              label: widget.label,
              focusNode: focusNode,
              hint: widget.placeholder,
              onChanged: (value) {
                widget.field!.getValueSetter()!(value);
                widget.onChanged(value);
              },
              onFieldSubmitted:
                  (p0) => FocusManager.instance.primaryFocus?.unfocus(),
              errorMessage:
                  state.validationError == null || state.value.isEmpty
                      ? null
                      : state.validationError?.toString(),
            );
          },
        );
  }

  void _listenFocusNode() {
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        widget.field?.setAutovalidate(true);
      } else {
        widget.field?.setAutovalidate(false);
      }
    });
  }
}
