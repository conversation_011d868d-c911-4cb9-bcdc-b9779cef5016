import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:leancode_forms/leancode_forms.dart';

class MorphPrimaryButtonField extends StatelessWidget {
  const MorphPrimaryButtonField({
    super.key,
    required this.field,
    required this.buttonText,
    required this.onButtonTap,
    this.isDisabled,
  });
  final BooleanFieldCubit field;
  final String buttonText;
  final VoidCallback onButtonTap;
  final bool? isDisabled;

  @override
  Widget build(BuildContext context) {
    return FieldBuilder(
      field: field,
      builder:
          (ctx, state) => DuploButton.defaultPrimary(
            semanticsIdentifier: "primary_button",
            title: buttonText,
            onTap: onButtonTap,
            isDisabled: isDisabled ?? !field.state.value,
            useFullWidth: true,
            trailingIcon:
                Assets.images.chevronRightDirectional(context).keyName,
          ),
    );
  }
}
