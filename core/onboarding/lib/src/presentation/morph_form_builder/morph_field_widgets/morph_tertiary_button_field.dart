import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:leancode_forms/leancode_forms.dart';

class MorphTertiaryButtonField extends StatelessWidget {
  const MorphTertiaryButtonField({
    super.key,
    required this.field,
    required this.buttonText,
    required this.onButtonTap,
  });
  final BooleanFieldCubit field;
  final String buttonText;
  final VoidCallback onButtonTap;

  @override
  Widget build(BuildContext context) {
    return FieldBuilder(
      field: field,
      builder:
          (ctx, state) => DuploButton.link(
            title: buttonText,
            onTap: onButtonTap,
            isDisabled: !field.state.value,
            textColor: context.duploTheme.foreground.fgTertiary,
            useFullWidth: true,
          ),
    );
  }
}
