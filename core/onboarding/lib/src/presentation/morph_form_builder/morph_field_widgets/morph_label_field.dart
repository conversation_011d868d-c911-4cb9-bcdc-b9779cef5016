import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as morph_assets;
import 'package:onboarding/src/data/form/option_model.dart';

class MorphLabelField extends StatelessWidget {
  const MorphLabelField({
    super.key,
    required this.field,
    required this.onTap,
    required this.option,
  });
  final OptionModel option;
  // ignore: strict_raw_type
  final SingleSelectFieldCubit field;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textTheme = DuploTextStyles.of(context);
    final label = option.optionLabel ?? option.optionName;
    return FieldBuilder(
      field: field,
      builder: (ctx, state) {
        return DuploTap(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              vertical: DuploSpacing.spacing_xl_16,
              horizontal: DuploSpacing.spacing_md_8,
            ),
            child: Row(
              children: [
                Expanded(
                  child: DuploText(
                    text: label,
                    style: textTheme.textSm,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textSecondary,
                  ),
                ),
                field.state.value == option
                    ? morph_assets.Assets.images.check.svg()
                    : Assets.images
                        .chevronRightDirectional(context)
                        .svg(
                          height: 16,
                          width: 16,
                          colorFilter: ColorFilter.mode(
                            theme.foreground.fgQuaternary,
                            BlendMode.srcIn,
                          ),
                        ),
              ],
            ),
          ),
        );
      },
    );
  }
}
