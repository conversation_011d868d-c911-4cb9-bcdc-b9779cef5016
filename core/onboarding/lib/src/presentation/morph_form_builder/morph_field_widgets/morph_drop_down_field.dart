import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/data/form/option_model.dart';

class MorphDropDownField extends StatelessWidget {
  const MorphDropDownField({
    super.key,
    required this.field,
    required this.onChanged,
    required this.dropDownField,
  });
  // ignore: strict_raw_type
  final SingleSelectFieldCubit? field;
  final DropDownField? dropDownField;
  final void Function() onChanged;

  @override
  Widget build(BuildContext context) {
    return field == null
        ? SizedBox()
        : FieldBuilder(
          field: field!,
          builder: (ctx, state) {
            return DuploDropDown.selector(
              semanticsIdentifier: "drop_down",
              context: context,
              hint: dropDownField?.label ?? dropDownField?.fieldName ?? '',
              dropDownItemModels:
                  (dropDownField?.options ?? [])
                      .map(
                        (option) => DropDownItemModel(
                          title: option.optionName,
                          image: FlagProvider.getFlagFromCountryName(
                            option.optionName,
                          ),
                        ),
                      )
                      .toList(),
              helperText: dropDownField?.placeholder ?? '',
              onChanged: (index) {
                field?.setValue(dropDownField?.options?.elementAtOrNull(index));
                onChanged();
              },
              selectedIndex: getIndex(),
            );
          },
        );
  }

  int getIndex() {
    final selectedValue = field?.state.value as OptionModel?;
    if (selectedValue == null) {
      return -1;
    }
    final index = dropDownField?.options?.indexWhere(
      (option) => option == selectedValue,
    );
    return index ?? -1;
  }
}
