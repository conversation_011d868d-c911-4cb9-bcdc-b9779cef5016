import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:leancode_forms/leancode_forms.dart';

class MorphDatePickerField extends StatefulWidget {
  const MorphDatePickerField({
    super.key,
    required this.field,
    required this.label,
    required this.placeholder,
    this.initialValue,
    this.onChanged,
    required this.helpText,
  });
  final TextFieldCubit? field;
  final String label;
  final String helpText;
  final String placeholder;
  final String? initialValue;
  final void Function(String)? onChanged;

  @override
  State<MorphDatePickerField> createState() => _MorphDatePickerFieldState();
}

class _MorphDatePickerFieldState extends State<MorphDatePickerField> {
  final focusNode = FocusNode();
  final textController = TextEditingController();
  @override
  void initState() {
    super.initState();
    // Initialize the text controller with the existing value if any
    if (widget.field != null) {
      textController.text = widget.field?.state.value ?? "";
    }
    _listenFocusNode();
  }

  @override
  void dispose() {
    focusNode.removeListener(_listenFocusNode);
    focusNode.dispose();
    textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.field == null
        ? SizedBox()
        : FieldBuilder(
          field: widget.field!,
          builder: (ctx, state) {
            return Semantics(
              identifier: 'date_picker',
              child: DuploDateOfBirthInputField(
                controller: textController,
                label: widget.label,
                showYears: true,
                hint: widget.placeholder,
                onChanged: (value) {
                  widget.field!.getValueSetter()!(value);
                  widget.onChanged?.call(value);
                },
                helperText: widget.helpText,
                focusNode: focusNode,
                onSubmitted:
                    (p0) => FocusManager.instance.primaryFocus?.unfocus(),
                errorMessage:
                    state.validationError == null || state.value.isEmpty
                        ? null
                        : state.validationError?.toString(),
              ),
            );
          },
        );
  }

  void _listenFocusNode() {
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        log('Focus gained validating now');
      } else {
        log('Focus lossed not validating ');
        widget.field?.validate();
      }
    });
  }
}
