import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:leancode_forms/leancode_forms.dart';
import 'package:onboarding/src/data/form/field_model.dart';

class MorphCheckboxField extends StatelessWidget {
  const MorphCheckboxField({
    super.key,
    required this.field,
    required this.checkboxField,
    required this.onChanged,
  });
  final BooleanFieldCubit? field;
  final CheckboxField? checkboxField;
  final void Function(bool) onChanged;

  @override
  Widget build(BuildContext context) {
    return field == null || checkboxField == null
        ? SizedBox.shrink()
        : FieldBuilder(
          field: field!,
          builder:
              (ctx, state) => Semantics(
                identifier: "check_box",
                child: DuploCheckBox(
                  body: checkboxField!.label,
                  bodyStyle: context.duploTextStyles.textSm,
                  currentValue: state.value,
                  onChanged: (bool) {
                    field!.setValue(bool);
                    onChanged(bool);
                  },
                ),
              ),
        );
  }
}
