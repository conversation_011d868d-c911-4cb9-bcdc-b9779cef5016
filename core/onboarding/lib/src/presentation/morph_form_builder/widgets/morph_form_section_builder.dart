// ignore_for_file: prefer-number-format

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/data/form/field_model.dart';
import 'package:onboarding/src/data/form/section_model.dart';
import 'package:onboarding/src/data/form/section_submit_result.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/morph_form_builder_bloc.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_date_picker/morph_date_picker.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_drop_down/morph_drop_down.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_introduction/morph_introduction.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_link_group/morph_link_group.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_risk_assessment/morph_risk_assessment.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_single_select/morph_single_select.dart';
import 'package:onboarding/src/presentation/morph_form_builder/morph_section_templates/morph_text_input/morph_text_input.dart';

class MorphFormSectionBuilder extends StatelessWidget {
  final SectionModel section;
  final ValueChanged<SectionSubmitResult> onSubmit;
  final VoidCallback onBackButtonTap;

  const MorphFormSectionBuilder({
    super.key,
    required this.section,
    required this.onSubmit,
    required this.onBackButtonTap,
  });

  @override
  Widget build(BuildContext context) {
    return _SectionContent(
      sectionModel: section,
      onSubmit: onSubmit,
      onBackButtonTap: onBackButtonTap,
    );
  }
}

class _SectionContent extends StatelessWidget {
  final SectionModel sectionModel;
  final ValueChanged<SectionSubmitResult> onSubmit;
  final VoidCallback onBackButtonTap;

  const _SectionContent({
    required this.sectionModel,
    required this.onSubmit,
    required this.onBackButtonTap,
  });

  @override
  Widget build(BuildContext context) {
    final sectionCubitMap =
        context.read<MorphFormBuilderBloc>().state.sectionCubitMap;

    return switch (sectionModel) {
      IntroductionSection section => () {
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphIntroduction(
              sectionCubit: sectionCubit,
              sectionAssetUrl: section.sectionAssetUrl,
              title: section.sectionTitle,
              infoText: section.sectionInfoText,
              description: section.sectionDescription ?? '',
              buttonField: _getPrimaryButtonField(section.fields),
              buttonText: getButtonLabel(section.fields),
              isButtonEnabled: true,
              onButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getPrimaryButtonField(section.fields)?.nextSectionID,
                    ),
                  ),
            );
      }(),
      SingleSelectSection section => () {
        final radioField = section.fields.whereType<RadioField>().firstOrNull;
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphSingleSelect(
              sectionCubit: sectionCubit,
              appBarTitle: section.sectionName,
              title: section.sectionTitle,
              infoText: section.sectionInfoText,
              description: section.sectionDescription,
              radioField: radioField,
              onSelect: (selectedOption) {
                onSubmit(selectedOption);
              },
            );
      }(),
      TextInputSection section => () {
        final textFields = section.fields.whereType<TextareaField>().toList();
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphTextInput(
              sectionCubit: sectionCubit,
              title: section.sectionTitle,
              description: section.sectionDescription,
              textFields: textFields,
              buttonField: _getPrimaryButtonField(section.fields)!,
              buttonText: getButtonLabel(section.fields),
              infoText: section.sectionInfoText,
              onButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getPrimaryButtonField(section.fields)?.nextSectionID,
                    ),
                  ),
            );
      }(),
      DatePickerSection section => () {
        final datePickerField =
            section.fields.whereType<DatePickerField>().toList();
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphDatePicker(
              sectionCubit: sectionCubit,
              title: section.sectionTitle,
              description: section.sectionDescription,
              datePickerField: datePickerField,
              infoText: section.sectionInfoText,
              buttonField: _getPrimaryButtonField(section.fields),
              buttonText: getButtonLabel(section.fields),
              onButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getPrimaryButtonField(section.fields)?.nextSectionID,
                    ),
                  ),
            );
      }(),
      LinkGroupSection section => () {
        final linkGroup =
            section.fields.whereType<LinkGroupField>().firstOrNull;
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphLinkGroup(
              sectionCubit: sectionCubit,
              title: section.sectionTitle,
              description: section.sectionDescription,
              linkGroup: linkGroup,
              infoText: section.sectionInfoText,
              buttonField: _getPrimaryButtonField(section.fields),
              buttonText: getButtonLabel(section.fields),
              onButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getPrimaryButtonField(section.fields)?.nextSectionID,
                    ),
                  ),
            );
      }(),
      DropDownSelectSection section => () {
        final dropDownSection =
            section.fields.whereType<DropDownField>().toList();
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphDropDown(
              sectionCubit: sectionCubit,
              title: section.sectionTitle,
              description: section.sectionDescription,
              dropDownFields: dropDownSection,
              infoText: section.sectionInfoText,
              buttonField: _getPrimaryButtonField(section.fields),
              buttonText: getButtonLabel(section.fields),
              onButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getPrimaryButtonField(section.fields)?.nextSectionID,
                    ),
                  ),
            );
      }(),
      RiskAssessmentSection section => () {
        final checkbox = section.fields.whereType<CheckboxField>().firstOrNull;
        final linkGroup =
            section.fields.whereType<LinkGroupField>().firstOrNull;
        final sectionCubit = sectionCubitMap![section.sectionID];
        return sectionCubit == null
            ? SizedBox.shrink()
            : MorphRiskAssessment(
              sectionCubit: sectionCubit,
              title: section.sectionTitle,
              description: section.sectionDescription,
              checkboxField: checkbox,
              linkGroup: linkGroup,
              infoText: section.sectionInfoText,
              sectionAssetUrl: section.sectionAssetUrl,
              buttonField: _getPrimaryButtonField(section.fields),
              tertiaryButtonField: _getTertiaryButtonField(section.fields),
              primaryButtonText: getButtonLabel(section.fields),
              tertiaryButtonText:
                  _getTertiaryButtonField(section.fields)?.label ?? "",
              onPrimaryButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getPrimaryButtonField(section.fields)?.nextSectionID,
                    ),
                  ),
              onTertiaryButtonTap:
                  () => onSubmit(
                    SectionSubmitResult(
                      nextSectionId:
                          _getTertiaryButtonField(
                            section.fields,
                          )?.nextSectionID,
                    ),
                  ),
              onBackButtonTap: onBackButtonTap,
            );
      }(),
    };
  }

  ButtonPrimaryField? _getPrimaryButtonField(List<FieldModel> fields) {
    return fields.whereType<ButtonPrimaryField>().firstOrNull;
  }

  ButtonTertiaryField? _getTertiaryButtonField(List<FieldModel> fields) {
    return fields.whereType<ButtonTertiaryField>().firstOrNull;
  }

  String getButtonLabel(List<FieldModel> fields) {
    final buttonField = _getPrimaryButtonField(fields);
    //todo change this continue localization
    return buttonField?.label ?? buttonField?.fieldName ?? 'Continue';
  }
}
