import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/data/form/section_model.dart';

class MorphProgressView extends StatelessWidget {
  const MorphProgressView({
    super.key,
    required this.progressValue,
    this.totalBarCount = 1,
    this.currentBarIndex = 1,
  });
  final SectionProgress? progressValue;
  final int totalBarCount;
  final int currentBarIndex;

  @override
  Widget build(BuildContext context) {
    final totalPage = progressValue?.totalSectionCount ?? 0;

    final currentPage = progressValue?.currentSectionIndex ?? 0;

    return progressValue != null && totalPage > 0
        ? DuploProgressBar(
          progressValue: currentPage / totalPage,
          padding: EdgeInsets.only(
            left: DuploSpacing.spacing_xl_16,
            right: DuploSpacing.spacing_xl_16,
            bottom: DuploSpacing.spacing_xl_16,
          ),
          barCount: totalBarCount,
          currentBarIndex: currentBarIndex,
        )
        : SizedBox.shrink();
  }
}
