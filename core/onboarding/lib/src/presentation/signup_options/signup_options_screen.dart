import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/assets/assets.gen.dart' as trader;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/signup_options/bloc/signup_options_bloc.dart';
import 'package:theme_manager/theme_manager.dart';

class SignupOptionsScreen extends StatelessWidget {
  const SignupOptionsScreen({
    super.key,
    required this.country,
    required this.countryCode,
    required this.brokerId,
    this.city,
  }) : isUaePassEnabled = country == "United Arab Emirates";

  final String country;
  final String countryCode;
  final String brokerId;
  final String? city;
  final bool isUaePassEnabled;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    return BlocProvider(
      create: (createContext) {
        final signupOptionsBloc = diContainer<SignupOptionsBloc>();
        if (!isUaePassEnabled) {
          signupOptionsBloc.add(
            SignupOptionsEvent.startSignup(
              country: country,
              countryCode: countryCode,
              brokerId: brokerId,
              city: city,
            ),
          );
        }
        return signupOptionsBloc;
      },
      child: BlocBuilder<SignupOptionsBloc, SignupOptionsState>(
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          // Show loading view when process state is loading
          if (state.processState is SignupOptionsLoadingState) {
            return LoadingView(
              title: localization.onboarding_loading,
              subtitle: "Please wait while we log you in",
            );
          }

          return Scaffold(
            resizeToAvoidBottomInset: true,
            backgroundColor: theme.background.bgPrimary,
            bottomNavigationBar: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 0,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DuploText(
                      textAlign: TextAlign.center,
                      text: localization.onboarding_privacyPolicyAgreement,
                      color: theme.text.textSecondary,
                      fontWeight: DuploFontWeight.regular,
                      style: DuploTextStyles.of(context).textSm,
                    ),
                    SizedBox(height: 32),
                    DuploButton.link(
                      semanticsIdentifier: "signup_options_privacy_policy",
                      title: localization.onboarding_privacyPolicy,
                      onTap: () {
                        print("Privacy Policy");
                      },
                    ),
                    SizedBox(height: 16),
                  ],
                ),
              ),
            ),
            body: SafeArea(
              child: Semantics(
                identifier: "signup_options_scroll_view",
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          diContainer<ThemeManager>().isDarkMode
                              ? trader.Assets.images.headerDark.image(
                                fit: BoxFit.fill,
                                width: MediaQuery.sizeOf(context).width,
                              )
                              : trader.Assets.images.headerLight.image(
                                fit: BoxFit.fill,
                                width: MediaQuery.sizeOf(context).width,
                              ),
                          Positioned(
                            top: 110,
                            child: Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0xff0A0D121A),
                                    blurRadius: 2,
                                    spreadRadius: -1,
                                  ),
                                  BoxShadow(
                                    color: Color(0xff0A0D121A),
                                    blurRadius: 3,
                                    spreadRadius: -1,
                                  ),
                                ],
                              ),
                              child: trader.Assets.images.equitiLogo.svg(),
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: DuploSpacing.spacing_xl_16,
                        ),
                        child: Column(
                          children: [
                            SizedBox(height: DuploSpacing.spacing_4xl_32),
                            DuploText(
                              text: localization.onboarding_getStarted,
                              color: theme.text.textPrimary,
                              fontWeight: DuploFontWeight.semiBold,
                              style: DuploTextStyles.of(context).displaySm,
                            ),
                            SizedBox(height: 16),
                            DuploText(
                              textAlign: TextAlign.center,
                              text:
                                  localization
                                      .onboarding_signup_options_description,
                              color: theme.text.textSecondary,
                              fontWeight: DuploFontWeight.regular,
                              style: DuploTextStyles.of(context).textSm,
                            ),
                            SizedBox(height: 24),
                            DuploButton.secondary(
                              semanticsIdentifier: "signup_options_with_email",
                              title: localization.onboarding_signUpWithEmail,
                              onTap: () {
                                builderContext.read<SignupOptionsBloc>().add(
                                  SignupOptionsEvent.startSignup(
                                    country: country,
                                    countryCode: countryCode,
                                    brokerId: brokerId,
                                    city: city,
                                  ),
                                );
                              },
                              leadingIcon:
                                  onboarding.Assets.images.emailIc.keyName,
                              useFullWidth: true,
                            ),
                            if (isUaePassEnabled) ...[
                              SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: Divider(
                                      color: theme.border.borderSecondary,
                                      height: 1,
                                      thickness: 1,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                    ),
                                    child: DuploText(
                                      text: localization.onboarding_or,
                                      color: theme.text.textTertiary,
                                      fontWeight: DuploFontWeight.regular,
                                      style: DuploTextStyles.of(context).textSm,
                                    ),
                                  ),
                                  Expanded(
                                    child: Divider(
                                      color: theme.border.borderSecondary,
                                      height: 1,
                                      thickness: 1,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12),
                              DuploButton.secondary(
                                semanticsIdentifier:
                                    "signup_options_uae_pass_button",
                                title:
                                    localization.onboarding_signUpWithUaePass,
                                onTap: () {
                                  print("UAE Pass");
                                },
                                useFullWidth: true,
                              ),
                            ],
                            SizedBox(height: 24),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                DuploText(
                                  textAlign: TextAlign.center,
                                  text:
                                      localization.onboarding_alreadyGotAccount,
                                  color: theme.text.textTertiary,
                                  fontWeight: DuploFontWeight.regular,
                                  style: DuploTextStyles.of(context).textSm,
                                ),
                                Semantics(
                                  identifier: "signup_options_login_button",
                                  child: TextButton(
                                    onPressed: () {
                                      builderContext.read<SignupOptionsBloc>().add(
                                        const SignupOptionsEvent.navigateToLogin(),
                                      );
                                    },
                                    child: DuploText(
                                      textAlign: TextAlign.center,
                                      text: localization.onboarding_login,
                                      color: theme.button.buttonTertiaryFg,
                                      fontWeight: DuploFontWeight.semiBold,
                                      style: DuploTextStyles.of(context).textMd,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height:
                                  MediaQuery.maybeViewInsetsOf(
                                    context,
                                  )?.bottom ??
                                  60,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
