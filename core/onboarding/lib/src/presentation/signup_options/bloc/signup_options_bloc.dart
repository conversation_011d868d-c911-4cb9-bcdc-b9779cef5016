import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/src/domain/exceptions/sign_up_with_okta_exception/sign_up_with_okta_exception.dart';
import 'package:onboarding/src/domain/usecase/signup_with_okta_usecase.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

part 'signup_options_bloc.freezed.dart';
part 'signup_options_event.dart';
part 'signup_options_state.dart';

class SignupOptionsBloc extends Bloc<SignupOptionsEvent, SignupOptionsState> {
  final OnboardingNavigation _onboardingNavigation;
  final SignupWithOktaUsecase _signupUseCase;
  final LoggerBase _logger;

  SignupOptionsBloc(
    this._onboardingNavigation,
    this._signupUseCase,
    this._logger,
  ) : super(SignupOptionsState()) {
    on<_NavigateToLogin>(_navigateToLogin);
    on<_StartSignup>(_startSignup);
  }

  FutureOr<void> _navigateToLogin(
    _NavigateToLogin event,
    Emitter<SignupOptionsState> emit,
  ) {
    _onboardingNavigation.goToLoginOptions();
  }

  FutureOr<void> _startSignup(
    _StartSignup event,
    Emitter<SignupOptionsState> emit,
  ) async {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.loading()));
    }
    final result =
        await _signupUseCase(
          country: event.country,
          countryCode: event.countryCode,
          brokerId: event.brokerId,
          city: event.city,
        ).run();
    result.fold(
      (exception) {
        //todo: handle error properly

        if (exception is SignUpWithOktaException) {
          switch (exception) {
            case SignUpWithOktaUnknownError():
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.initial(),
                  ),
                );
              }
          }
        } else {
          if (!isClosed) {
            emit(
              state.copyWith(processState: SignupOptionsProcessState.initial()),
            );
          }
        }
      },
      (authResult) {
        _logger.logInfo('Success! Access token: ${authResult.toString()}');
        if (!isClosed) {
          emit(
            state.copyWith(processState: SignupOptionsProcessState.initial()),
          );
        }
        _onboardingNavigation.navigateToMorphFormBuilder();
      },
    );
  }
}
