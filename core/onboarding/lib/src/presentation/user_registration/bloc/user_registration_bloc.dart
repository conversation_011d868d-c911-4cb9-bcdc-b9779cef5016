import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding/src/domain/exceptions/user_registration_exception/user_registration_exception.dart';
import 'package:onboarding/src/domain/usecase/user_registration_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

part 'user_registration_bloc.freezed.dart';
part 'user_registration_event.dart';
part 'user_registration_state.dart';

/// BLoC for handling user registration form logic and state management.
///
/// This BLoC follows the project's established patterns for form handling,
/// validation, and API integration. It manages the user registration form
/// state, validates input fields, and coordinates with the UserRegistrationUseCase
/// for submitting registration data.
class UserRegistrationBloc
    extends Bloc<UserRegistrationEvent, UserRegistrationState> {
  final UserRegistrationUseCase _userRegistrationUseCase;
  final OnboardingNavigation _onboardingNavigation;

  UserRegistrationBloc({
    required UserRegistrationUseCase userRegistrationUseCase,
    required OnboardingNavigation onboardingNavigation,
  }) : _userRegistrationUseCase = userRegistrationUseCase,
       _onboardingNavigation = onboardingNavigation,
       super(UserRegistrationState()) {
    on<_ValidateFirstName>(_onValidateFirstName);
    on<_ValidateLastName>(_onValidateLastName);
    on<_ClearFirstNameErrorMessage>(_onClearFirstNameErrorMessage);
    on<_ClearLastNameErrorMessage>(_onClearLastNameErrorMessage);
    on<_SubmitRegistration>(_onSubmitRegistration);
  }

  /// Validates the first name input field.
  ///
  /// Performs comprehensive validation checks including:
  /// - Required field validation (non-empty after trimming)
  /// - Length validation (2-50 characters)
  /// - Character validation (letters, hyphens, apostrophes, single spaces, Latin characters only)
  /// - Consecutive character validation (no consecutive hyphens, spaces, or apostrophes)
  FutureOr<void> _onValidateFirstName(
    _ValidateFirstName event,
    Emitter<UserRegistrationState> emit,
  ) {
    final firstName = event.firstName.trim();
    final validationResult = _validateName(firstName, isFirstName: true);

    emit(
      state.copyWith(
        firstName: firstName,
        isFirstNameValid: validationResult.isValid,
        firstNameErrorCode: validationResult.errorCode,
        isSubmitButtonEnabled:
            validationResult.isValid && state.isLastNameValid,
      ),
    );
  }

  /// Validates the last name input field.
  ///
  /// Performs the same comprehensive validation checks as first name validation.
  FutureOr<void> _onValidateLastName(
    _ValidateLastName event,
    Emitter<UserRegistrationState> emit,
  ) {
    final lastName = event.lastName.trim();
    final validationResult = _validateName(lastName, isFirstName: false);

    emit(
      state.copyWith(
        lastName: lastName,
        isLastNameValid: validationResult.isValid,
        lastNameErrorCode: validationResult.errorCode,
        isSubmitButtonEnabled:
            state.isFirstNameValid && validationResult.isValid,
      ),
    );
  }

  /// Clears the first name error message.
  FutureOr<void> _onClearFirstNameErrorMessage(
    _ClearFirstNameErrorMessage event,
    Emitter<UserRegistrationState> emit,
  ) {
    emit(state.copyWith(firstNameErrorCode: null));
  }

  /// Clears the last name error message.
  FutureOr<void> _onClearLastNameErrorMessage(
    _ClearLastNameErrorMessage event,
    Emitter<UserRegistrationState> emit,
  ) {
    emit(state.copyWith(lastNameErrorCode: null));
  }

  /// Submits the user registration form.
  ///
  /// Creates a UserRegistrationRequestModel with the form data and
  /// calls the UserRegistrationUseCase to submit the registration.
  /// Handles both success and error responses appropriately.
  FutureOr<void> _onSubmitRegistration(
    _SubmitRegistration event,
    Emitter<UserRegistrationState> emit,
  ) async {
    if (!state.isSubmitButtonEnabled) return;

    emit(state.copyWith(processState: UserRegistrationProcessState.loading()));

    final result =
        await _userRegistrationUseCase(
          firstName: state.firstName!,
          lastName: state.lastName!,
        ).run();

    result.fold(
      (error) {
        if (!isClosed) {
          if (error is UserRegistrationException) {
            switch (error) {
              case UserRegistrationUnknownError():
                emit(
                  state.copyWith(
                    processState: UserRegistrationProcessState.error(
                      error.message,
                    ),
                  ),
                );
            }
          } else {
            emit(
              state.copyWith(
                processState: UserRegistrationProcessState.error(
                  error.toString(),
                ),
              ),
            );
          }
        }
      },
      (_) {
        if (!isClosed) {
          emit(
            state.copyWith(
              processState: UserRegistrationProcessState.success(),
            ),
          );
          // Navigate to the next step in the onboarding flow
          // This would typically be determined by the navigation system
          _onboardingNavigation.navigateToMobileNumberInput();
        }
      },
    );
  }

  /// Comprehensive name validation with detailed error reporting.
  ///
  /// Validates a name according to the specified requirements:
  /// - Required field validation (non-empty after trimming)
  /// - Length validation (2-50 characters)
  /// - Character validation (letters, hyphens, apostrophes, single spaces, Latin characters only)
  /// - Consecutive character validation (no consecutive hyphens, spaces, or apostrophes)
  _ValidationResult _validateName(String name, {required bool isFirstName}) {
    // Required field validation
    if (name.isEmpty) {
      return _ValidationResult(
        isValid: false,
        errorCode:
            isFirstName
                ? NameErrorCode.enterFirstNameError
                : NameErrorCode.enterLastNameError,
      );
    }

    // Length validation
    if (name.length < 2) {
      return _ValidationResult(
        isValid: false,
        errorCode: NameErrorCode.nameTooShortError,
      );
    }

    if (name.length > 50) {
      return _ValidationResult(
        isValid: false,
        errorCode: NameErrorCode.nameTooLongError,
      );
    }

    // Character validation - only letters, hyphens, apostrophes, single spaces, Latin characters
    if (!_isValidCharacterFormat(name)) {
      return _ValidationResult(
        isValid: false,
        errorCode: NameErrorCode.invalidCharactersError,
      );
    }

    // Consecutive character validation
    final consecutiveError = _getConsecutiveCharacterError(name);
    if (consecutiveError != null) {
      return _ValidationResult(isValid: false, errorCode: consecutiveError);
    }

    return _ValidationResult(isValid: true, errorCode: null);
  }

  /// Validates if the name contains only allowed characters.
  ///
  /// Allowed characters: Letters (a-z, A-Z), hyphens (-), apostrophes ('), single spaces, Latin characters
  /// Not allowed: Special characters, non-Latin characters, numbers
  bool _isValidCharacterFormat(String name) {
    // Updated regex to be more restrictive - only Latin letters, hyphens, apostrophes, and single spaces
    final regex = RegExp(r"^[a-zA-ZÀ-ÿ\s\-']+$");
    return regex.hasMatch(name);
  }

  /// Checks for consecutive character violations and returns the appropriate error code.
  ///
  /// Returns null if no consecutive character violations are found.
  NameErrorCode? _getConsecutiveCharacterError(String name) {
    if (name.contains('--')) {
      return NameErrorCode.consecutiveHyphensError;
    }

    if (name.contains('  ')) {
      return NameErrorCode.consecutiveSpacesError;
    }

    if (name.contains("''")) {
      return NameErrorCode.consecutiveApostrophesError;
    }

    return null;
  }
}

/// Helper class to encapsulate validation results.
class _ValidationResult {
  final bool isValid;
  final NameErrorCode? errorCode;

  const _ValidationResult({required this.isValid, required this.errorCode});
}
