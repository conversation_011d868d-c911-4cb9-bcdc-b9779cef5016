import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/country_selection/bloc/country_bloc.dart';

class CountrySelection extends StatelessWidget {
  const CountrySelection({super.key, this.onConfirm});
  final void Function(BuildContext context, String? selectedCountry)? onConfirm;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider(
      create:
          (_) =>
              diContainer<CountryBloc>()
                ..add(const CountryEvent.loadCountries()),
      child: BlocBuilder<CountryBloc, CountryState>(
        buildWhen: (previous, current) {
          return previous != current;
        },
        builder: (blocContext, state) {
          final isTestRunning = Platform.environment.containsKey(
            'FLUTTER_TEST',
          );
          return switch (state.currentState) {
            Loading() => isTestRunning ? SizedBox() : LoadingView(),
            Success() => Scaffold(
              backgroundColor: theme.background.bgPrimary,
              appBar: DuploAppBar(title: ""),
              bottomNavigationBar: SafeArea(
                top: false,
                child: Padding(
                  padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
                  child: DuploButton.defaultPrimary(
                    semanticsIdentifier: 'confirm',
                    title: localization.trader_confirm,
                    onTap: () => _onConfirmButtonPressed(blocContext),
                    trailingIcon:
                        Assets.images.chevronRightDirectional(context).keyName,
                    isDisabled:
                        !state.isCheckboxSelected ||
                        state.selectedIndex == null ||
                        state.selectedIndex == -1,
                  ),
                ),
              ),
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploText(
                        text: localization.trader_countryPickerTitle,
                        style: duploTextStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                        textAlign:
                            Directionality.of(blocContext) == TextDirection.ltr
                                ? TextAlign.left
                                : TextAlign.right,
                      ),
                      SizedBox(height: 8),
                      DuploText(
                        text: localization.trader_countryPickerBody,
                        style: duploTextStyles.textSm,
                        fontWeight: DuploFontWeight.regular,
                        color: theme.text.textSecondary,
                        textAlign:
                            Directionality.of(blocContext) == TextDirection.ltr
                                ? TextAlign.left
                                : TextAlign.right,
                      ),
                      SizedBox(height: 16),
                      DuploDropDown.selector(
                        semanticsIdentifier: 'country_of_residence',
                        context: blocContext,
                        bottomSheetTitle: localization.trader_selectCountry,
                        hint: localization.trader_countryOfResidence,
                        hintText: localization.trader_search,
                        dropDownItemModels:
                            (state.allCountriesData ?? [])
                                .map(
                                  (country) => DropDownItemModel(
                                    title: country.name,
                                    image: FlagProvider.getFlagFromCountryCode(
                                      country.code,
                                    ),
                                  ),
                                )
                                .toList(),
                        selectedIndex: state.selectedIndex ?? -1,
                        onChanged: (index) {
                          blocContext.read<CountryBloc>().add(
                            CountryEvent.selectCountry(index),
                          );
                        },
                        helperText: localization.trader_countryPickerHelper,
                      ),
                      SizedBox(height: 16),
                      DuploCheckBox(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.background.bgSecondary,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        title: localization.trader_countryPickerCheckBoxTitle,
                        body: localization.trader_countryPickerCheckBoxBody,
                        onChanged:
                            (value) =>
                                _onCheckboxChanged(value, blocContext, state),
                        currentValue: state.isCheckboxSelected,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Error() => SafeArea(
              child: Scaffold(
                body: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: DuploSpacing.spacing_xl_16,
                  ),
                  child: EmptyOrErrorStateComponent.error(
                    raiseIssueText: localization.trader_raiseATicket,
                    onRaiseIssue: () {
                      debugPrint('Raise ticket');
                    },
                    svgImage: onboarding.Assets.images.searchError.svg(),
                    title: localization.trader_somethingWentWrong,
                    description: localization.trader_countryLoadingMessage,
                    retryButtonText: localization.trader_tryAgain,
                    onTapRetry: () {
                      blocContext.read<CountryBloc>().add(
                        const CountryEvent.loadCountries(),
                      );
                    },
                  ),
                ),
              ),
            ),
          };
        },
      ),
    );
  }

  /// Handles the checkbox change event.
  ///
  /// If the country is not selected, it shows an error toast message.
  /// Otherwise, it updates the checkbox state in the [CountryBloc].
  ///
  /// [value] is the new value of the checkbox.
  /// [context] is the build context.
  /// [state] is the current state of the [CountryBloc].
  void _onCheckboxChanged(
    bool value,
    BuildContext context,
    CountryState state,
  ) {
    if (state.selectedIndex == -1) {
      final toast = DuploToast();

      toast.showToastMessage(
        context: context,
        widget: DuploToastMessage(
          titleMessage:
              EquitiLocalization.of(context).trader_countryNotSelected,
          descriptionMessage:
              EquitiLocalization.of(context).trader_pleaseSelectCountry,
          messageType: ToastMessageType.error,
          onLeadingAction: () {
            toast.hidesToastMessage();
          },
        ),
      );
      return;
    }
    context.read<CountryBloc>().add(CountryEvent.updateCheckbox(value));
  }

  void _onConfirmButtonPressed(BuildContext context) {
    final bloc = context.read<CountryBloc>();
    final state = bloc.state;
    final countriesList = state.allCountriesData ?? [];
    final selectedIndex = state.selectedIndex ?? -1;

    onConfirm?.call(
      context,
      selectedIndex != -1
          ? countriesList.elementAtOrNull(selectedIndex)?.name
          : null,
    );
    bloc.add(const CountryEvent.onConfirmButtonPressed());
  }
}
