import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:domain/domain.dart';
import 'package:onboarding/src/domain/usecase/set_selected_country_usecase.dart';
import 'package:onboarding/src/navigation/arguments/signup_options_args.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

part 'country_bloc.freezed.dart';
part 'country_event.dart';
part 'country_state.dart';

@injectable
class CountryBloc extends Bloc<CountryEvent, CountryState> {
  final GetCountryUseCase _getCountriesUseCase;
  final OnboardingNavigation _onboardingNavigation;
  final SetSelectedCountryUseCase _setSelectedCountryUseCase;

  CountryBloc(
    this._getCountriesUseCase,
    this._onboardingNavigation,
    this._setSelectedCountryUseCase,
  ) : super(const CountryState()) {
    on<LoadCountriesEvent>(_onLoadCountries);
    on<SelectCountryEvent>(_onSelectCountry);
    on<UpdateCheckboxEvent>(_onUpdateCheckbox);
    on<OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
  }

  Future<void> _onLoadCountries(
    LoadCountriesEvent event,
    Emitter<CountryState> emit,
  ) async {
    emit(state.copyWith(currentState: CountryProcessState.loading()));
    final result = await _getCountriesUseCase().run();
    result.fold(
      (failure) {
        //todo: handle error properly
        if (failure is GetCountriesException) {
          switch (failure) {
            case GetCountriesUnknownError():
              emit(state.copyWith(currentState: CountryProcessState.error()));
          }
        } else {
          emit(state.copyWith(currentState: CountryProcessState.error()));
        }
      },
      (AllCountriesModel allCountriesModel) {
        final countries = allCountriesModel.countries;
        emit(
          state.copyWith(
            currentState: CountryProcessState.success(),
            allCountriesData: countries,
            // todo (sambhav, aakash) need to get this from location
            selectedIndex: -1,
          ),
        );
        final isRunningTest = Platform.environment.containsKey('FLUTTER_TEST');

        if (!isRunningTest) {
          emit(
            state.copyWith(
              selectedIndex: countries.indexWhere(
                (country) => country.code == 'AE',
              ),
              isCheckboxSelected: true,
            ),
          );
        }
      },
    );
  }

  void _onSelectCountry(SelectCountryEvent event, Emitter<CountryState> emit) {
    emit(state.copyWith(selectedIndex: event.selectedIndex));
  }

  void _onUpdateCheckbox(
    UpdateCheckboxEvent event,
    Emitter<CountryState> emit,
  ) {
    emit(state.copyWith(isCheckboxSelected: event.isSelected));
  }

  Future<void> _onConfirmButtonPressed(
    OnConfirmButtonPressedEvent event,
    Emitter<CountryState> emit,
  ) async {
    final allCountryModel = state.allCountriesData;
    final selectedIndex = state.selectedIndex;
    final selectedCountry = allCountryModel?.elementAtOrNull(
      selectedIndex ?? -1,
    );
    if (selectedCountry == null) {
      emit(state.copyWith(currentState: CountryProcessState.error()));
      return;
    }
    var setSelectedCountry =
        await _setSelectedCountryUseCase(countryData: selectedCountry).run();
    setSelectedCountry.fold((l) {
      log('Error setting selected country: $l');
      addError(l);
    }, (r) => null);
    if (selectedCountry.cities?.isNotEmpty ?? false) {
      _onboardingNavigation.goToCitySelection();
      return;
    }
    _onboardingNavigation.goToSignupOptions(
      args: SignupOptionsArgs(
        country: selectedCountry.name,
        countryCode: selectedCountry.code,
        // todo (aakash): Set default broker id
        brokerId: selectedCountry.brokerId ?? "",
      ),
    );
  }
}
