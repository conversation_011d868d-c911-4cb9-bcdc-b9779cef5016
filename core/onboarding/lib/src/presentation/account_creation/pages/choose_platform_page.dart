// ignore_for_file: avoid-unsafe-collection-methods

import 'package:broker_settings/broker_settings.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;

class ChoosePlatformPage extends StatelessWidget {
  const ChoosePlatformPage({super.key, this.pageIndex});

  final int? pageIndex;

  SvgPicture getLeadingIcon(AccountCreationPlatform code) {
    switch (code) {
      case AccountCreationPlatform.dulcimer:
        return onboarding.Assets.images.traderCandle.svg();
      case AccountCreationPlatform.mt5:
        return onboarding.Assets.images.mt5Icon.svg();
      case AccountCreationPlatform.mt4:
        return onboarding.Assets.images.mt4Icon.svg();
    }
  }

  bool checkPlatformAvailability(CreateAccountState state) {
    final selectedPlatform = state.accountCreationRequestModel.platform;

    final platform = state.tradingPlatforms!.firstWhere(
      (p) => p.code == selectedPlatform,
    );
    return platform.accountCreationAvailability.enabled;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocBuilder<CreateAccountBloc, CreateAccountState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final List<TradingPlatform> platforms = state.tradingPlatforms!;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_choosePlatform,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 24),
            for (int i = 0; i < platforms.length; i++) ...[
              DuploTap(
                onTap: () {
                  context.read<CreateAccountBloc>().add(
                    CreateAccountEvent.updateAccountCreationData(
                      accountCreationRequestModel: state
                          .accountCreationRequestModel
                          .copyWith(platform: platforms[i].code),
                    ),
                  );
                },
                child: DuploSelectionContainer(
                  isSelected:
                      state.accountCreationRequestModel.platform ==
                      platforms[i].code,
                  borderWidth: 2,
                  borderRadius: 8,
                  backgroundColor: theme.background.bgActive,
                  selectedIconAlignment: CrossAxisAlignment.center,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 20,
                  ),
                  leading: getLeadingIcon(platforms[i].code),
                  title: DuploText(
                    text: platforms[i].name,
                    style: textStyles.textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  subTitle: DuploText(
                    text: platforms[i].description,
                    style: textStyles.textXs,
                    color: theme.text.textTertiary,
                  ),
                ),
              ),
              if (i < platforms.length - 1) const SizedBox(height: 12),
            ],
            const Spacer(),
            if (!checkPlatformAvailability(state)) ...[
              DuploAlertMessage.warning(
                title: localization.onboarding_reachedMaxAccount,
                titleFontWeight: DuploFontWeight.regular,
              ),
              SizedBox(height: 16),
            ],
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              isDisabled: !checkPlatformAvailability(state),
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
