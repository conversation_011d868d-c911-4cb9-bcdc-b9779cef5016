// ignore_for_file: avoid-unsafe-collection-methods
import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';

class ChooseNicknamePage extends StatefulWidget {
  const ChooseNicknamePage({
    super.key,
    this.pageIndex,
    required this.tradingEnvironment,
  });

  final int? pageIndex;
  final CreateAccountFlow tradingEnvironment;

  @override
  State<ChooseNicknamePage> createState() => _ChooseNicknamePageState();
}

class _ChooseNicknamePageState extends State<ChooseNicknamePage> {
  late final TextEditingController _nicknameController;

  @override
  void initState() {
    super.initState();
    _nicknameController = TextEditingController();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  bool isValid(String value) {
    final isValid = RegExp(
      r'^[\p{L}\p{N} ]{1,36}$',
      unicode: true,
    ).hasMatch(value);
    return isValid;
  }

  String? getValidationError(String value, EquitiLocalization localization) {
    if (value.isEmpty) {
      return null;
    }

    if (value.length > 36) {
      return localization.onboarding_invalidNameLength;
    }

    if (!isValid(value)) {
      return localization.onboarding_useLettersAndSpaces;
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocConsumer<CreateAccountBloc, CreateAccountState>(
      listener: (ctx, state) {
        switch (state.progressState) {
          case DataSubmitError():
            DuploErrorSheet.show<void>(
              context: ctx,
              bodyTitle: localization.onboarding_somethingWentWrong,
              bodySubTitle: localization.onboarding_errorDescription,
              actionWidget: DuploButton.secondary(
                title: localization.onboarding_goBack,
                useFullWidth: true,
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            );
            break;
          case DataSubmittedState():
            diContainer<OnboardingNavigation>().navigateToAccountSuccessful(
              data: state.accountCreationRequestModel,
              createAccountFlow: widget.tradingEnvironment,
              replace: true,
            );
            break;
          default:
            break;
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        _nicknameController.text =
            state.accountCreationRequestModel.accountNickname;

        final AccountCreationPlatform selectedPlatform =
            state.accountCreationRequestModel.platform;

        final List<AccountType> accountTypes =
            state.tradingPlatforms!
                .firstWhere((element) => element.code == selectedPlatform)
                .accountTypes;

        final bool requirePassword =
            accountTypes
                .firstWhere(
                  (element) =>
                      element.name ==
                      state.accountCreationRequestModel.platformAccountType,
                )
                .requirePassword;

        final isLoading = switch (state.progressState) {
          DataSubmittingState() => true,
          _ => false,
        };

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_personalizeYourAccount,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: localization.onboarding_setNicknameDescription,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
            ),
            const SizedBox(height: 24),
            DuploTextField(
              semanticsIdentifier: "nickname_field",
              label: localization.onboarding_accountNickname,
              hint: localization.onboarding_enterNickname,
              errorMessage: getValidationError(
                state.accountCreationRequestModel.accountNickname,
                localization,
              ),
              controller: _nicknameController,
              autoFocus: true,
              onChanged: (value) {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.updateAccountCreationData(
                    accountCreationRequestModel: state
                        .accountCreationRequestModel
                        .copyWith(accountNickname: value),
                  ),
                );
              },
              suffixIcon: Semantics(
                identifier: "nickname_info_icon",
                child: Tooltip(
                  enableFeedback: true,
                  triggerMode: TooltipTriggerMode.tap,
                  message: localization.onboarding_enterNicknameForAccount,
                  child: onboarding.Assets.images.emailInfoIc.svg(),
                ),
              ),
            ),
            const SizedBox(height: 4),
            DuploText(
              text: localization.onboarding_nameValidationDescription,
              style: textStyles.textXs,
              color: theme.text.textTertiary,
            ),
            Spacer(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                onboarding.Assets.images.infoRoundOutlined.svg(height: 14),
                const SizedBox(width: 6),
                Expanded(
                  child: DuploText(
                    text: localization.onboarding_nicknameReferenceText,
                    style: textStyles.textXs,
                    color: theme.text.textQuaternary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              isLoading: isLoading,
              isDisabled:
                  !isValid(state.accountCreationRequestModel.accountNickname),
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                if (requirePassword) {
                  context.read<CreateAccountBloc>().add(
                    CreateAccountEvent.navigateToNext(
                      pageIndex: widget.pageIndex,
                    ),
                  );
                } else {
                  context.read<CreateAccountBloc>().add(
                    CreateAccountEvent.submitAccountCreationData(),
                  );
                }
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
