// ignore_for_file: avoid-unsafe-collection-methods

import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';

class ChooseAccountPage extends StatelessWidget {
  const ChooseAccountPage({
    super.key,
    this.pageIndex,
    required this.tradingEnvironment,
  });

  final int? pageIndex;
  final CreateAccountFlow tradingEnvironment;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateAccountBloc, CreateAccountState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final AccountCreationPlatform selectedPlatform =
            state.accountCreationRequestModel.platform;
        List<AccountType> accountType =
            state.tradingPlatforms!
                .firstWhere((element) => element.code == selectedPlatform)
                .accountTypes;

        if (tradingEnvironment == CreateAccountFlow.demoAccount) {
          accountType =
              accountType
                  .where(
                    (element) => element.name == AccountCreationType.standard,
                  )
                  .toList();
        }

        final theme = DuploTheme.of(context);
        final textStyles = DuploTextStyles.of(context);
        final localization = EquitiLocalization.of(context);
        return Column(
          children: [
            Expanded(
              child: Semantics(
                identifier: 'choose_account_page_scroll',
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploText(
                        text: localization.onboarding_chooseAccount,
                        style: textStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      const SizedBox(height: 24),
                      for (int i = 0; i < accountType.length; i++) ...[
                        DuploTap(
                          onTap: () {
                            context.read<CreateAccountBloc>().add(
                              CreateAccountEvent.updateAccountCreationData(
                                accountCreationRequestModel: state
                                    .accountCreationRequestModel
                                    .copyWith(
                                      platformAccountType: accountType[i].name,
                                    ),
                              ),
                            );
                          },
                          child: DuploSelectionContainer(
                            isSelected:
                                state
                                    .accountCreationRequestModel
                                    .platformAccountType ==
                                accountType[i].name,
                            backgroundColor: Colors.transparent,
                            title: DuploText(
                              text: accountType[i].title,
                              style: textStyles.textMd,
                              fontWeight: DuploFontWeight.semiBold,
                              color: theme.text.textSecondary,
                            ),
                            subTitle: DuploText(
                              text: accountType[i].subTitle,
                              style: textStyles.textXs,
                              color: theme.text.textSecondary,
                            ),
                            body: Column(
                              children: [
                                for (
                                  int j = 0;
                                  j < accountType[i].features.length;
                                  j++
                                ) ...[
                                  Row(
                                    children: [
                                      Assets.images.outlinedRadioBase.svg(),
                                      const SizedBox(width: 8),
                                      DuploText(
                                        text: accountType[i].features[j],
                                        style: textStyles.textXs,
                                        color: theme.text.textTertiary,
                                        fontWeight: DuploFontWeight.medium,
                                      ),
                                    ],
                                  ),
                                  if (j != accountType[i].features.length - 1)
                                    const SizedBox(height: 16),
                                ],
                              ],
                            ),
                          ),
                        ),
                        if (i != accountType.length - 1)
                          const SizedBox(height: 12),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
