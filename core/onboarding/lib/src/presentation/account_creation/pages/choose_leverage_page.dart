import 'package:broker_settings/broker_settings.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:prelude/prelude.dart';

class ChooseLeveragePage extends StatelessWidget {
  const ChooseLeveragePage({super.key, this.pageIndex});

  final int? pageIndex;

  /// Gets the current account variant based on the state
  AccountVariant? _getCurrentVariant(CreateAccountState state) {
    final request = state.accountCreationRequestModel;
    ;

    final platform = state.tradingPlatforms!.firstOrNullWhere(
      (element) => element.code == request.platform,
    );

    final accountType = platform!.accountTypes.firstOrNullWhere(
      (element) => element.name == request.platformAccountType,
    );

    return accountType!.variants.firstOrNullWhere(
      (variant) => variant.swapFree == request.swapFreeAccount,
    );
  }

  /// Updates the leverage in the bloc
  void _updateLeverage(
    BuildContext context,
    CreateAccountState state,
    String leverage,
  ) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CreateAccountBloc>().add(
        CreateAccountEvent.updateAccountCreationData(
          accountCreationRequestModel: state.accountCreationRequestModel
              .copyWith(leverage: leverage),
        ),
      );
    });
  }

  /// Gets leverage options and handles auto-selection of max leverage
  List<String> _getLeverageOptions(
    BuildContext context,
    CreateAccountState state,
  ) {
    final currentVariant = _getCurrentVariant(state);

    //TODO(Shubham) remove default values after BE fix
    final leverages =
        currentVariant!.leverages.isEmpty
            ? [500, 400, 300, 200, 100, 50, 30, 15, 1]
            : currentVariant.leverages;

    final leverageOptions =
        leverages
            // ignore: prefer-number-format
            .map((leverage) => '1:$leverage')
            .toList();

    // Auto-select max leverage if no leverage is currently selected
    final isLeverageEmpty = state.accountCreationRequestModel.leverage?.isEmpty;
    if (isLeverageEmpty ?? true) {
      _updateLeverage(
        context,
        state,
        // ignore: prefer-number-format
        currentVariant.maxLeverage.toString(),
      );
    }

    return leverageOptions;
  }

  /// Gets the selected index and handles invalid selections
  int _getSelectedIndex(
    List<String> leverageOptions,
    String? selectedLeverage,
    BuildContext context,
    CreateAccountState state,
  ) {
    final index = leverageOptions.indexWhere(
      (item) => item.endsWith(selectedLeverage ?? ''),
    );

    // If selected leverage is not found, auto-select max leverage
    if (index == -1) {
      final currentVariant = _getCurrentVariant(state);
      _updateLeverage(
        context,
        state,
        // ignore: prefer-number-format
        currentVariant!.maxLeverage.toString(),
      );
      return leverageOptions.indexWhere(
        // ignore: prefer-number-format
        (item) => item.endsWith(currentVariant.maxLeverage.toString()),
      );
    }

    return index;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);

    return BlocBuilder<CreateAccountBloc, CreateAccountState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final leverageOptions = _getLeverageOptions(context, state);
        final selectedIndex = _getSelectedIndex(
          leverageOptions,
          state.accountCreationRequestModel.leverage,
          context,
          state,
        );

        return Column(
          children: [
            Expanded(
              child: Semantics(
                identifier: "leverage_options_scroll_view",
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploText(
                        text: localization.onboarding_maxLeverageNeeded,
                        style: textStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      const SizedBox(height: 8),
                      DuploText(
                        text:
                            localization
                                .onboarding_maxLeverageNeededDescription,
                        style: textStyles.textSm,
                        color: theme.text.textSecondary,
                      ),
                      const SizedBox(height: 24),
                      Semantics(
                        identifier: "leverage_options_radio_selector",
                        child: DuploRadioSelector(
                          selectedIndex: selectedIndex,
                          onSelected: (index) {
                            final selectedLeverage =
                                leverageOptions
                                    .elementAtOrNull(index)
                                    ?.split(':')
                                    .lastOrNull ??
                                '';

                            _updateLeverage(context, state, selectedLeverage);
                          },
                          children:
                              leverageOptions.map((leverage) {
                                return DuploText(
                                  text: leverage,
                                  style: textStyles.textSm,
                                  color: theme.text.textSecondary,
                                  fontWeight: DuploFontWeight.medium,
                                );
                              }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
