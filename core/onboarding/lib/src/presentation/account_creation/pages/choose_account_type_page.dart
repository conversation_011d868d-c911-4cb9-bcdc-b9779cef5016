// ignore_for_file: avoid-unsafe-collection-methods

import 'package:broker_settings/broker_settings.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';

class ChooseAccountTypePage extends StatelessWidget {
  const ChooseAccountTypePage({super.key, this.pageIndex});

  final int? pageIndex;

  void updateSwapAccount(BuildContext context) {
    final bloc = context.read<CreateAccountBloc>();
    final state = bloc.state;
    final variants = getVariants(state);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      bloc.add(
        CreateAccountEvent.updateAccountCreationData(
          accountCreationRequestModel: state.accountCreationRequestModel
              .copyWith(swapFreeAccount: variants.first.swapFree),
        ),
      );
    });
  }

  List<AccountVariant> getVariants(CreateAccountState state) {
    final AccountCreationPlatform selectedPlatform =
        state.accountCreationRequestModel.platform;
    final List<AccountType> accountTypes =
        state.tradingPlatforms!
            .firstWhere((element) => element.code == selectedPlatform)
            .accountTypes;
    final List<AccountVariant> variants =
        accountTypes
            .firstWhere(
              (element) =>
                  element.name ==
                  state.accountCreationRequestModel.platformAccountType,
            )
            .variants;
    return variants;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    if (context
            .read<CreateAccountBloc>()
            .state
            .accountCreationRequestModel
            .swapFreeAccount ==
        null) {
      updateSwapAccount(context);
    }

    return BlocBuilder<CreateAccountBloc, CreateAccountState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final variants = getVariants(state);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_chooseAccountType,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: localization.onboarding_enableSwapFreeFeature,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
            ),
            const SizedBox(height: 24),
            for (int i = 0; i < variants.length; i++) ...[
              DuploTap(
                onTap: () {
                  context.read<CreateAccountBloc>().add(
                    CreateAccountEvent.updateAccountCreationData(
                      accountCreationRequestModel: state
                          .accountCreationRequestModel
                          .copyWith(swapFreeAccount: variants[i].swapFree),
                    ),
                  );
                },
                child: DuploSelectionContainer(
                  isSelected:
                      state.accountCreationRequestModel.swapFreeAccount ==
                      variants[i].swapFree,
                  backgroundColor: Colors.transparent,
                  title: DuploText(
                    text: variants[i].title,
                    style: textStyles.textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textSecondary,
                  ),
                  subTitle: DuploText(
                    text: variants[i].subtitle,
                    style: textStyles.textXs,
                    color: theme.text.textSecondary,
                  ),
                ),
              ),
              if (i < variants.length - 1) const SizedBox(height: 24),
            ],
            Spacer(),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 16),
            DuploButton.link(
              semanticsIdentifier: "terms_and_conditions_button",
              useFullWidth: true,
              title: localization.onboarding_termsAndConditions,
              onTap: () {
                //TODO: open terms and conditions
              },
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
