import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding/src/domain/exceptions/post_account_creation_data_exception/post_account_creation_data_exception.dart';
import 'package:onboarding/src/domain/model/account_creation/account_creation_request_model.dart';
import 'package:onboarding/src/domain/usecase/submit_account_creation_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

part 'create_account_bloc.freezed.dart';
part 'create_account_event.dart';
part 'create_account_state.dart';

class CreateAccountBloc extends Bloc<CreateAccountEvent, CreateAccountState> {
  CreateAccountBloc(
    this._navigation,
    this._getCreateAccountDataUseCase,
    this._submitAccountCreationUseCase,
  ) : super(CreateAccountState()) {
    on<NavigateToCreateAccountMainScreen>(_navigateToCreateAccountMainScreen);
    on<FetchAccountCreationData>(_fetchAccountCreationData);
    on<NavigateToNext>(_navigateToNext);
    on<NavigateToPrevious>(_navigateToBack);
    on<UpdateAccountCreationData>(_updateAccountCreationData);
    on<SubmitAccountCreationData>(_submitAccountCreationData);
  }

  final OnboardingNavigation _navigation;
  final GetCreateAccountDataUseCase _getCreateAccountDataUseCase;
  final SubmitAccountCreationUseCase _submitAccountCreationUseCase;

  void _navigateToCreateAccountMainScreen(
    NavigateToCreateAccountMainScreen event,
    Emitter<CreateAccountState> emit,
  ) {
    _navigation.navigateToCreateAccountMain(
      createAccountFlow: event.createAccountFlow,
    );
  }

  void _fetchAccountCreationData(
    FetchAccountCreationData event,
    Emitter<CreateAccountState> emit,
  ) async {
    emit(
      state.copyWith(progressState: CreateAccountProgressState.dataLoading()),
    );

    final accountCreationFetchResult =
        await _getCreateAccountDataUseCase().run();

    if (isClosed) return;

    accountCreationFetchResult.fold(
      (exception) {
        emit(
          state.copyWith(
            progressState: CreateAccountProgressState.dataLoadingError(),
          ),
        );
      },
      (response) {
        emit(
          state.copyWith(
            tradingPlatforms: response,
            progressState: CreateAccountProgressState.dataLoaded(),
          ),
        );
      },
    );
  }

  void _navigateToNext(NavigateToNext event, Emitter<CreateAccountState> emit) {
    emit(
      state.copyWith(
        pageIndex: event.pageIndex! + 1,
        progressState: CreateAccountProgressState.navigatedNext(),
      ),
    );
  }

  void _navigateToBack(
    NavigateToPrevious event,
    Emitter<CreateAccountState> emit,
  ) {
    emit(
      state.copyWith(
        pageIndex: event.pageIndex! - 1,
        progressState: CreateAccountProgressState.navigatedBack(),
      ),
    );
  }

  void _updateAccountCreationData(
    UpdateAccountCreationData event,
    Emitter<CreateAccountState> emit,
  ) {
    emit(
      state.copyWith(
        selectedCurrencyIndex:
            event.currencyIndex ?? state.selectedCurrencyIndex,
        accountCreationRequestModel: event.accountCreationRequestModel,
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _submitAccountCreationData(
    SubmitAccountCreationData event,
    Emitter<CreateAccountState> emit,
  ) async {
    emit(
      state.copyWith(
        progressState: CreateAccountProgressState.dataSubmitting(),
      ),
    );

    final accountCreationSubmitResult =
        await _submitAccountCreationUseCase(
          data: state.accountCreationRequestModel,
        ).run();

    if (isClosed) return;

    accountCreationSubmitResult.fold(
      (err) {
        //todo: handle error properly
        if (err is PostAccountCreationDataException) {
          switch (err) {
            case PostAccountCreationDataUnknownError():
              emit(
                state.copyWith(
                  progressState: CreateAccountProgressState.dataSubmitError(),
                ),
              );
          }
        } else {
          emit(
            state.copyWith(
              progressState: CreateAccountProgressState.dataSubmitError(),
            ),
          );
        }
      },
      (res) {
        emit(
          state.copyWith(
            progressState: CreateAccountProgressState.dataSubmitted(),
          ),
        );
      },
    );
  }
}
