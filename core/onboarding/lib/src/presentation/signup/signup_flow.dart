import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/signup/bloc/signup_flow_bloc.dart';
import 'package:onboarding/src/presentation/signup/widgets/email_page.dart';
import 'package:onboarding/src/presentation/signup/widgets/error_bottom_sheet.dart';

import 'widgets/password_page.dart';
import 'widgets/user_name_page.dart';

// todo (aakash): Delete this
class SignupFlow extends StatefulWidget {
  const SignupFlow({super.key, this.email});
  final String? email;

  @override
  State<SignupFlow> createState() => _SignupFlowState();
}

class _SignupFlowState extends State<SignupFlow> {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return BlocProvider(
      create: (createContext) => diContainer<SignupFlowBloc>(),
      child: BlocConsumer<SignupFlowBloc, SignupFlowState>(
        listenWhen:
            (previous, current) =>
                (current.stepIndex != previous.stepIndex) ||
                (previous.errorBottomSheetCode != current.errorBottomSheetCode),
        listener: (listenerContext, state) {
          if (state.errorBottomSheetCode != null) {
            DuploSheet.showModalSheet<Widget>(
              context: context,
              hideCloseButton: true,
              hasTopBarLayer: false,
              onClose: (isCloseUsingDrag) {
                listenerContext.read<SignupFlowBloc>().add(
                  SignupFlowEvent.clearErrorCode(),
                );
              },
              content: (contentContext) {
                return ErrorBottomSheet(
                  blocContext: listenerContext,
                  errorCode: state.errorBottomSheetCode!,
                );
              },
              title: '',
            );
          }
          if (_pageController.hasClients) {
            _pageController.animateToPage(
              state.stepIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        },
        buildWhen:
            (previous, current) =>
                (previous.stepIndex != current.stepIndex) ||
                (previous.disbaleGoBackButton != current.disbaleGoBackButton),
        builder: (builderContext, state) {
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            appBar: AppBar(
              backgroundColor: theme.background.bgPrimary,
              leading: IconButton(
                onPressed: () {
                  if (state.disbaleGoBackButton) {
                    return;
                  }
                  if (state.stepIndex == 0) {
                    Navigator.pop(builderContext);
                  } else {
                    builderContext.read<SignupFlowBloc>().add(
                      SignupFlowEvent.previousStep(),
                    );
                  }
                },
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.foreground.fgSecondary,
                ),
              ),
            ),
            body: SafeArea(
              child: PageView(
                physics: const NeverScrollableScrollPhysics(),
                controller: _pageController,
                children: [
                  EmailPage(email: widget.email, blocContext: builderContext),
                  PasswordPage(),
                  UserNamePage(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
