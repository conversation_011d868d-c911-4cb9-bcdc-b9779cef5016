import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:domain/domain.dart';
import 'package:onboarding/src/data/check_email_response_model/check_email_response_model.dart';
import 'package:onboarding/src/data/signup_response_model/signup_response_model.dart';
import 'package:onboarding/src/domain/exceptions/check_email_exception/check_email_exception.dart';
import 'package:onboarding/src/domain/usecase/check_email_use_case.dart';
import 'package:onboarding/src/domain/usecase/get_selected_country_usecase.dart';
import 'package:onboarding/src/domain/usecase/signup_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:prelude/prelude.dart';
import 'package:validator/validator.dart' hide ValidationError;

part 'signup_flow_bloc.freezed.dart';
part 'signup_flow_event.dart';
part 'signup_flow_state.dart';

// todo (aakash): Delete this
class SignupFlowBloc extends Bloc<SignupFlowEvent, SignupFlowState> {
  final CheckEmailUseCase _checkEmailUseCase;
  final SignupUseCase _signupUseCase;
  final OnboardingNavigation _onboardingNavigation;
  final GetSelectedCountryUseCase _getSelectedCountryUseCase;

  SignupFlowBloc({
    required CheckEmailUseCase checkEmailUseCase,
    required SignupUseCase signupUseCase,
    required OnboardingNavigation onboardingNavigation,
    required GetSelectedCountryUseCase getSelectedCountryUseCase,
  }) : _checkEmailUseCase = checkEmailUseCase,
       _signupUseCase = signupUseCase,
       _onboardingNavigation = onboardingNavigation,
       _getSelectedCountryUseCase = getSelectedCountryUseCase,
       super(SignupFlowState()) {
    on<_NextStep>(_nextStep);
    on<_PreviousStep>(_previousStep);
    on<_ValidateEmail>(
      _validateEmail,
      transformer: throttleTransformer(Duration(milliseconds: 500)),
    );
    on<_ValidatePassword>(_validatePassword);
    on<_ValidateUserName>(
      _validateUserName,
      transformer: throttleTransformer(Duration(milliseconds: 500)),
    );
    on<_ValidateLastName>(
      _validateLastName,
      transformer: throttleTransformer(Duration(milliseconds: 500)),
    );
    on<_ClearEmailErrorMessage>(_clearEmailErrorMessage);
    on<_ClearFirstNameErrorMessage>(_clearFirstNameErrorMessage);
    on<_ClearLastNameErrorMessage>(_clearLastNameErrorMessage);
    on<_CheckEmailExistance>(_checkEmailExistance);
    on<_SignUp>(_signUp);
    on<_NavigateToLogin>(_navigateToLogin);
    on<_ClearErrorCode>(_clearErrorCode);
    on<_ClearCheckEmailErrorMessage>(_clearCheckEmailErrorMessage);
  }

  // Step Management
  FutureOr<void> _nextStep(_NextStep event, Emitter<SignupFlowState> emit) {
    emit(state.copyWith(stepIndex: state.stepIndex + 1));
  }

  FutureOr<void> _previousStep(
    _PreviousStep event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(state.copyWith(stepIndex: state.stepIndex - 1));
  }

  // Email Validation
  FutureOr<void> _validateEmail(
    _ValidateEmail event,
    Emitter<SignupFlowState> emit,
  ) {
    if (event.email.isEmpty) {
      return null;
    }

    final validator = InputValidator();
    final isEmailValid = validator.validate(event.email).email().run().isEmpty;
    emit(
      state.copyWith(
        emailErrorMessage: isEmailValid ? null : 'email_invalid',
        isLoginButtonEnabled: isEmailValid,
        email: event.email,
      ),
    );
  }

  FutureOr<void> _clearEmailErrorMessage(
    _ClearEmailErrorMessage event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(state.copyWith(emailErrorMessage: null));
  }

  // Password Validation
  FutureOr<void> _validatePassword(
    _ValidatePassword event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(
      state.copyWith(
        isPasswordButtonEnabled: event.isValidPassword,
        password: event.password,
      ),
    );
  }

  FutureOr<void> _validateUserName(
    _ValidateUserName event,
    Emitter<SignupFlowState> emit,
  ) {
    final error = _validateName(event.name);
    emit(
      state.copyWith(
        firstNameErrorMessage: error,
        firstName: event.name,
        isFirstNameValid: error == null,
      ),
    );
  }

  FutureOr<void> _validateLastName(
    _ValidateLastName event,
    Emitter<SignupFlowState> emit,
  ) {
    final error = _validateName(event.name);
    emit(
      state.copyWith(
        lastNameErrorMessage: error,
        lastName: event.name,
        isLastNameValid: error == null,
      ),
    );
  }

  FutureOr<void> _clearFirstNameErrorMessage(
    _ClearFirstNameErrorMessage event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(state.copyWith(firstNameErrorMessage: null));
  }

  FutureOr<void> _clearLastNameErrorMessage(
    _ClearLastNameErrorMessage event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(state.copyWith(lastNameErrorMessage: null));
  }

  String? _validateName(String name) {
    if (name.isEmpty) {
      return "enterFirstNameAndLastNameError";
    }

    if (name != name.trim()) {
      return 'consecutiveSpacesError';
    }

    final trimmedName = name.trim();

    if (!RegExp(r"^[a-zA-Z'\- ]+$").hasMatch(trimmedName)) {
      return 'invalidCharactersError';
    }

    if (RegExp(r"[-]{2,}").hasMatch(trimmedName)) {
      return 'consecutiveHyphensError';
    }

    if (RegExp(r"[']{2,}").hasMatch(trimmedName)) {
      return 'consecutiveApostrophesError';
    }

    if (RegExp(r"[ ]{2,}").hasMatch(trimmedName)) {
      return 'consecutiveSpacesError';
    }

    if (trimmedName.length < 2) {
      return 'nameTooShortError';
    }

    if (trimmedName.length > 50) {
      return 'nameTooLongError';
    }

    return null;
  }

  FutureOr<void> _checkEmailExistance(
    _CheckEmailExistance event,
    Emitter<SignupFlowState> emit,
  ) async {
    emit(state.copyWith(isLoginButtonLoading: true, disbaleGoBackButton: true));
    final checkEmailRequest =
        await _checkEmailUseCase(email: event.email).run();
    emit(
      state.copyWith(isLoginButtonLoading: false, disbaleGoBackButton: false),
    );
    checkEmailRequest.fold(
      (l) {
        if (l is CheckEmailException) {
          switch (l) {
            case CheckEmailUnknownError():
              //todo handle error case
              break;
          }
        } else {
          //todo handle error case
          log(l.toString());
        }

        addError(l);
      },
      (r) {
        if (r.data.exists) {
          emit(state.copyWith(emailErrorMessage: 'email_exists'));
        } else {
          add(SignupFlowEvent.nextStep());
        }
      },
    );
  }

  FutureOr<void> _signUp(_SignUp event, Emitter<SignupFlowState> emit) async {
    emit(
      state.copyWith(isUserNameButtonLoading: true, disbaleGoBackButton: true),
    );

    await _getSelectedCountryUseCase().run().then((result) {
      result.fold(
        (l) {
          log('error while getting selected country: $l');
          emit(state.copyWith(errorBottomSheetCode: 'UNKNOWN_ERROR'));
        },
        (r) {
          log('previous Selected country: ${r?.name ?? 'its null'}');

          if (r?.brokerId == null || r?.name == null) {
            emit(state.copyWith(errorBottomSheetCode: 'UNKNOWN_ERROR'));
            return;
          }
          emit(state.copyWith(selectCountryData: r));
        },
      );
    });

    final signupRequest =
        await _signupUseCase(
          email: state.email ?? "",
          password: state.password ?? "",
          firstName: state.firstName ?? "",
          lastName: state.lastName ?? "",
          userType: "equiti",
          businessUnitId: "CFD",
          brokerId: state.selectCountryData!.brokerId,
          countryCode: state.selectCountryData!.code,
          country: state.selectCountryData!.name,
        ).run();

    await signupRequest.fold(
      (l) {
        emit(
          state.copyWith(
            isUserNameButtonLoading: false,
            disbaleGoBackButton: false,
          ),
        );
        print("error");
        addError(l);
        emit(state.copyWith(errorBottomSheetCode: 'UNKNOWN_ERROR'));
      },
      (r) async {
        switch (r) {
          case SignupEmailExists value:
            emit(state.copyWith(errorBottomSheetCode: value.code));
            break;
          case SignupInternalServerError value:
            emit(state.copyWith(errorBottomSheetCode: value.code));
            break;
          case SignupPasswordPolicyViolation value:
            emit(state.copyWith(errorBottomSheetCode: value.code));
            break;
          case SignupValidationError value:
            emit(state.copyWith(errorBottomSheetCode: value.code));
            break;
          case SignupSuccess value:
            _onboardingNavigation.navigateToMobileNumberInput();
            break;
        }
        emit(
          state.copyWith(
            isUserNameButtonLoading: false,
            disbaleGoBackButton: false,
          ),
        );
      },
    );
  }

  FutureOr<void> _navigateToLogin(
    _NavigateToLogin event,
    Emitter<SignupFlowState> emit,
  ) {
    _onboardingNavigation.goToLogin();
  }

  FutureOr<void> _clearErrorCode(
    _ClearErrorCode event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(state.copyWith(errorBottomSheetCode: null));
  }

  FutureOr<void> _clearCheckEmailErrorMessage(
    _ClearCheckEmailErrorMessage event,
    Emitter<SignupFlowState> emit,
  ) {
    emit(state.copyWith(emailErrorMessage: null));
  }
}
