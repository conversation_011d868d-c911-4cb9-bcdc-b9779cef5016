import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as trader;
import 'package:onboarding/src/presentation/signup/bloc/signup_flow_bloc.dart';

class PasswordPage extends StatefulWidget {
  const PasswordPage({super.key});

  @override
  State<PasswordPage> createState() => _PasswordPageState();
}

class _PasswordPageState extends State<PasswordPage>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DuploText(
                  text: localization.onboarding_createPassword,
                  style: DuploTextStyles.of(context).textXl,
                  fontWeight: DuploFontWeight.bold,
                  color: theme.text.textPrimary,
                ),
                SizedBox(height: 20),
                PasswordFieldValidator(
                  semanticsIdentifier: "password_field",
                  needValidationComponent: true,
                  onPasswordValidation: (String password, bool isValid) {
                    context.read<SignupFlowBloc>().add(
                      SignupFlowEvent.validatePassword(
                        isValidPassword: isValid,
                        password: password,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 100),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              color: theme.background.bgPrimary,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  BlocBuilder<SignupFlowBloc, SignupFlowState>(
                    buildWhen:
                        (previous, current) =>
                            previous.isPasswordButtonEnabled !=
                            current.isPasswordButtonEnabled,
                    builder: (builderContext, state) {
                      return Container(
                        width: MediaQuery.sizeOf(context).width,
                        child: DuploButton.defaultPrimary(
                          semanticsIdentifier: 'continue',
                          isDisabled: !state.isPasswordButtonEnabled,
                          title: localization.onboarding_continueText,
                          loadingText: localization.onboarding_loading,
                          trailingIcon: trader.Assets.images.continueIc.keyName,
                          onTap: () {
                            context.read<SignupFlowBloc>().add(
                              const SignupFlowEvent.nextStep(),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
