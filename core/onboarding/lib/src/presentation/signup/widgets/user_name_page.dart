import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/presentation/signup/bloc/signup_flow_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as trader;

class UserNamePage extends StatefulWidget {
  const UserNamePage({super.key});

  @override
  State<UserNamePage> createState() => _UserNamePageState();
}

class _UserNamePageState extends State<UserNamePage>
    with AutomaticKeepAliveClientMixin {
  final firstNameFoucsNode = FocusNode();
  final TextEditingController firstNameController = TextEditingController();
  final lastNameFoucsNode = FocusNode();
  final TextEditingController lastNameController = TextEditingController();
  @override
  void initState() {
    super.initState();
    firstNameFoucsNode.requestFocus();
    firstNameFoucsNode.addListener(firstNameFoucsNodeListener);

    lastNameFoucsNode.addListener(lastNameFoucsNodeListener);
  }

  @override
  void dispose() {
    firstNameFoucsNode.removeListener(firstNameFoucsNodeListener);
    lastNameFoucsNode.removeListener(lastNameFoucsNodeListener);
    firstNameFoucsNode.dispose();
    lastNameFoucsNode.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    super.dispose();
  }

  void firstNameFoucsNodeListener() {
    if (!firstNameFoucsNode.hasFocus) {
      context.read<SignupFlowBloc>().add(
        SignupFlowEvent.validateUserName(firstNameController.text),
      );
    }
  }

  void lastNameFoucsNodeListener() {
    if (!lastNameFoucsNode.hasFocus) {
      context.read<SignupFlowBloc>().add(
        SignupFlowEvent.validateLastName(lastNameController.text),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DuploText(
                  text: localization.onboarding_tellUsYourName,
                  style: DuploTextStyles.of(context).textXl,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                SizedBox(height: 8),
                DuploText(
                  text: localization.onboarding_enterFirstAndLastName,
                  style: DuploTextStyles.of(context).textSm,
                  fontWeight: DuploFontWeight.regular,
                  color: theme.text.textSecondary,
                  textAlign: TextAlign.start,
                ),
                SizedBox(height: 25),
                BlocBuilder<SignupFlowBloc, SignupFlowState>(
                  buildWhen:
                      (previous, current) =>
                          previous.firstNameErrorMessage !=
                          current.firstNameErrorMessage,
                  builder: (builderContext, state) {
                    return DuploTextField(
                      semanticsIdentifier: "first_name_text_field",
                      controller: firstNameController,
                      focusNode: firstNameFoucsNode,
                      errorMessage: mapError(
                        state.firstNameErrorMessage,
                        localization,
                      ),
                      hint: localization.onboarding_enterFirstName,
                      suffixIcon: Tooltip(
                        enableFeedback: true,
                        triggerMode: TooltipTriggerMode.tap,
                        message: localization.onboarding_enterLegalFirstName,
                        child: trader.Assets.images.emailInfoIc.svg(),
                      ),
                      label: localization.onboarding_firstName,
                      onChanged: (value) {
                        context.read<SignupFlowBloc>().add(
                          SignupFlowEvent.clearFirstNameErrorMessage(),
                        );
                        context.read<SignupFlowBloc>().add(
                          SignupFlowEvent.validateUserName(value),
                        );
                      },
                    );
                  },
                ),
                SizedBox(height: 15),
                BlocBuilder<SignupFlowBloc, SignupFlowState>(
                  buildWhen:
                      (previous, current) =>
                          previous.lastNameErrorMessage !=
                          current.lastNameErrorMessage,
                  builder: (builderContext, state) {
                    return DuploTextField(
                      semanticsIdentifier: "last_name_text_field",
                      focusNode: lastNameFoucsNode,
                      controller: lastNameController,
                      hint: localization.onboarding_enterLastName,
                      errorMessage: mapError(
                        state.lastNameErrorMessage,
                        localization,
                      ),
                      suffixIcon: Tooltip(
                        enableFeedback: true,
                        triggerMode: TooltipTriggerMode.tap,
                        message: localization.onboarding_enterLegalLastName,
                        child: trader.Assets.images.emailInfoIc.svg(),
                      ),
                      label: localization.onboarding_lastName,
                      onChanged: (value) {
                        context.read<SignupFlowBloc>().add(
                          SignupFlowEvent.clearLastNameErrorMessage(),
                        );
                        context.read<SignupFlowBloc>().add(
                          SignupFlowEvent.validateLastName(value),
                        );
                      },
                    );
                  },
                ),
                const SizedBox(height: 100),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 20),
              color: theme.background.bgPrimary,
              child: BlocBuilder<SignupFlowBloc, SignupFlowState>(
                buildWhen:
                    (previous, current) =>
                        (previous.isFirstNameValid !=
                                current.isFirstNameValid ||
                            previous.isLastNameValid !=
                                current.isLastNameValid) ||
                        (previous.isUserNameButtonLoading !=
                            current.isUserNameButtonLoading),
                builder: (builderContext, state) {
                  return Container(
                    width: MediaQuery.sizeOf(context).width,
                    child: DuploButton.defaultPrimary(
                      semanticsIdentifier: "continue_button",
                      isDisabled:
                          !(state.isFirstNameValid && state.isLastNameValid),
                      title: localization.onboarding_continueText,
                      trailingIcon: trader.Assets.images.continueIc.keyName,
                      loadingText: localization.onboarding_loading,
                      isLoading: state.isUserNameButtonLoading,
                      onTap: () {
                        context.read<SignupFlowBloc>().add(
                          const SignupFlowEvent.signUp(),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  String? mapError(String? key, EquitiLocalization localization) {
    switch (key) {
      case 'enterFirstNameAndLastNameError':
        return localization.onboarding_enterFirstNameAndLastNameError;
      case 'invalidCharactersError':
        return localization.onboarding_invalidCharactersError;
      case 'consecutiveHyphensError':
        return localization.onboarding_consecutiveHyphensError;
      case 'consecutiveApostrophesError':
        return localization.onboarding_consecutiveApostrophesError;
      case 'consecutiveSpacesError':
        return localization.onboarding_consecutiveSpacesError;
      case 'nameTooShortError':
        return localization.onboarding_nameTooShortError;
      case 'nameTooLongError':
        return localization.onboarding_nameTooLongError;
      default:
        return null;
    }
  }
}
