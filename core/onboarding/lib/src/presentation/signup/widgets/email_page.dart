import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/presentation/signup/bloc/signup_flow_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as trader;
import 'package:onboarding/src/presentation/signup/widgets/email_exists_bottom_sheet.dart';

class EmailPage extends StatefulWidget {
  const EmailPage({super.key, this.email, required this.blocContext});
  final String? email;
  final BuildContext blocContext;

  @override
  State<EmailPage> createState() => _EmailPageState();
}

class _EmailPageState extends State<EmailPage>
    with AutomaticKeepAliveClientMixin {
  late final TextEditingController emailController;
  @override
  void initState() {
    super.initState();
    emailController = TextEditingController();
    if (widget.email != null) {
      emailController.text = widget.email!;
      widget.blocContext.read<SignupFlowBloc>().add(
        SignupFlowEvent.validateEmail(emailController.text),
      );
    }
  }

  @override
  void dispose() {
    emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);

    return BlocListener<SignupFlowBloc, SignupFlowState>(
      listener: (listenerContext, state) {
        if (state.emailErrorMessage == "email_exists") {
          DuploSheet.showModalSheet<Widget>(
            context: context,
            hideCloseButton: true,
            onClose: (isCloseUsingDrag) {
              listenerContext.read<SignupFlowBloc>().add(
                SignupFlowEvent.clearEmailErrorMessage(),
              );
            },
            hasTopBarLayer: false,
            content: (contentContext) {
              return EmailExistsBottomSheet(blocContext: listenerContext);
            },
            title: '',
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Semantics(
              identifier: "email_page_scroll_view",
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploText(
                      text: localization.onboarding_whatIsYourEmail,
                      style: DuploTextStyles.of(context).textXl,
                      color: theme.text.textPrimary,
                      fontWeight: DuploFontWeight.semiBold,
                    ),
                    SizedBox(height: 8),
                    DuploText(
                      text: localization.onboarding_useThisToLogin,
                      style: DuploTextStyles.of(context).textSm,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                    ),
                    SizedBox(height: 25),
                    BlocBuilder<SignupFlowBloc, SignupFlowState>(
                      buildWhen:
                          (previous, current) =>
                              previous.emailErrorMessage !=
                              current.emailErrorMessage,
                      builder: (emailBuilderContext, emailState) {
                        return DuploTextField(
                          semanticsIdentifier: 'enter_email',
                          controller: emailController,
                          prefixIcon: trader.Assets.images.emailIc.svg(),
                          hint: localization.onboarding_enterEmail,
                          errorMessage: emailFieldErrorMessage(
                            emailState.emailErrorMessage,
                            localization,
                          ),
                          suffixIcon: Tooltip(
                            enableFeedback: true,
                            triggerMode: TooltipTriggerMode.tap,
                            message: localization.onboarding_emailTooltip,
                            child: trader.Assets.images.emailInfoIc.svg(),
                          ),
                          label: localization.onboarding_emailAddress,
                          onChanged: (value) {
                            context.read<SignupFlowBloc>().add(
                              SignupFlowEvent.clearEmailErrorMessage(),
                            );
                            context.read<SignupFlowBloc>().add(
                              SignupFlowEvent.validateEmail(value.trim()),
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(height: 5),
                    BlocBuilder<SignupFlowBloc, SignupFlowState>(
                      buildWhen:
                          (previous, current) =>
                              previous.emailErrorMessage !=
                              current.emailErrorMessage,
                      builder: (builderContext, state) {
                        return state.emailErrorMessage == null
                            ? DuploText(
                              text:
                                  localization
                                      .onboarding_privacyPolicyAgreement,
                              style: DuploTextStyles.of(context).textXs,
                              fontWeight: DuploFontWeight.regular,
                              color: theme.text.textSecondary,
                              textAlign: TextAlign.start,
                            )
                            : Container();
                      },
                    ),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10),
                color: theme.background.bgPrimary,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    BlocBuilder<SignupFlowBloc, SignupFlowState>(
                      buildWhen:
                          (previous, current) =>
                              (previous.isLoginButtonEnabled !=
                                  current.isLoginButtonEnabled) ||
                              (previous.isLoginButtonLoading !=
                                  current.isLoginButtonLoading),
                      builder: (builderContext, state) {
                        return Container(
                          width: MediaQuery.sizeOf(context).width,
                          child: DuploButton.defaultPrimary(
                            semanticsIdentifier: 'continue',
                            isDisabled: !state.isLoginButtonEnabled,
                            isLoading: state.isLoginButtonLoading,
                            loadingText: localization.onboarding_loading,
                            title: localization.onboarding_continueText,
                            trailingIcon:
                                trader.Assets.images.continueIc.keyName,
                            onTap: () {
                              context.read<SignupFlowBloc>().add(
                                SignupFlowEvent.checkEmailExistance(
                                  emailController.text,
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                    Center(
                      child: TextButton(
                        onPressed: () {
                          print("Privacy Policy");
                        },
                        child: DuploText(
                          text: localization.onboarding_privacyPolicy,
                          style: DuploTextStyles.of(context).textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.button.buttonTertiaryFg,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  String? emailFieldErrorMessage(String? key, EquitiLocalization localization) {
    switch (key) {
      case 'email_invalid':
        return localization.onboarding_emailInvalid;
      case 'email_exists':
        return localization.onboarding_emailExists;
      default:
        return key;
    }
  }
}
