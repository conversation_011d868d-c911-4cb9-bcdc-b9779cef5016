import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:onboarding/src/data/add_phone_request_model/add_phone_request_model.dart';
import 'package:onboarding/src/domain/exceptions/add_phone_exception/add_phone_exception.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:onboarding/src/domain/usecase/add_phone_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:prelude/prelude.dart';
import 'package:user_account/user_account.dart';

part 'mobile_number_input_bloc.freezed.dart';
part 'mobile_number_input_event.dart';
part 'mobile_number_input_state.dart';

@injectable
class MobileNumberInputBloc
    extends Bloc<MobileNumberInputEvent, MobileNumberInputState> {
  MobileNumberInputBloc(
    this._navigation,
    this._addPhoneUseCase,
    this._getUserIdUseCase,
    this._getCountriesUseCase,
  ) : super(const MobileNumberInputState()) {
    on<Initial>(_initial);
    on<PhoneNumberChanged>(_onPhoneNumberChanged);
    on<CountryCodeSelected>(_onCountryCodeSelected);
    on<ConfirmButtonPressed>(_onConfirmButtonPressed);
    on<ValidatePhoneNumber>(_onValidatePhoneNumber);
    on<OpenHelpDialog>(_onOpenHelpDialog);
    on<SignInPressed>(_onSignInPressed);
    add(const Initial());
  }

  final OnboardingNavigation _navigation;
  final AddPhoneUseCase _addPhoneUseCase;
  final GetUserIdUseCase _getUserIdUseCase;
  final GetCountryUseCase _getCountriesUseCase;

  Future<void> _initial(
    Initial event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    // Set loading state
    emit(
      state.copyWith(
        processState: const MobileNumberInputProcessState.loading(),
      ),
    );
    // Get countries from use case
    final countryResult = await _getCountriesUseCase().run();

    countryResult.fold(
      (failure) {
        // Handle error case
        if (!isClosed) {
          if (failure is GetCountriesException) {
            switch (failure) {
              case GetCountriesUnknownError():
                emit(
                  state.copyWith(
                    processState: const MobileNumberInputProcessState.error(),
                  ),
                );
            }
          }
          emit(
            state.copyWith(
              processState: const MobileNumberInputProcessState.error(),
            ),
          );
        }
      },
      (allCountriesModel) {
        final countries = allCountriesModel.countries;
        if (countries.isNotEmpty && !isClosed) {
          //todo(sambhav): hardcoded for now because of requirement to select UAE as default
          //and we dont want to ask for location for this purpose only
          final areIndex = countries.indexWhere(
            (country) => country.code == 'ARE',
          );
          final selectedIndex = areIndex != -1 ? areIndex : 0;
          final selectedCountry = countries.elementAtOrNull(selectedIndex);

          emit(
            state.copyWith(
              countries: countries,
              selectedCountryCode: selectedCountry?.dialCode ?? "",
              selectedCountryFlag: selectedCountry?.code ?? '',
              selectedCountryIndex: selectedIndex,
              placeholderText: _getExamplePlaceholderText(
                selectedCountry?.dialCode ?? "",
              ),
              processState: const MobileNumberInputProcessState.initial(),
            ),
          );
        }
      },
    );
  }

  void _onPhoneNumberChanged(
    PhoneNumberChanged event,
    Emitter<MobileNumberInputState> emit,
  ) {
    emit(state.copyWith(phoneNumber: event.phoneNumber, errorMessage: null));
    add(const MobileNumberInputEvent.validatePhoneNumber());
  }

  void _onCountryCodeSelected(
    CountryCodeSelected event,
    Emitter<MobileNumberInputState> emit,
  ) {
    final country = state.countries.elementAtOrNull(event.selectedIndex);
    emit(
      state.copyWith(
        selectedCountryCode: country?.dialCode ?? '',
        selectedCountryFlag: country?.code ?? '',
        errorMessage: null,
        selectedCountryIndex: event.selectedIndex,
        placeholderText: _getExamplePlaceholderText(country?.dialCode ?? ''),
      ),
    );
    add(const MobileNumberInputEvent.validatePhoneNumber());
  }

  Future<void> _onConfirmButtonPressed(
    ConfirmButtonPressed event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    if (!state.isConfirmButtonEnabled) return;

    if (!isClosed) {
      emit(state.copyWith(isLoading: true));
    }
    try {
      log(
        'navigate to verify mobile screen ${state.phoneNumber} ${state.selectedCountryCode}',
      );
      final userId = await _getUserIdFromPreferences();
      if (userId == null) {
        if (!isClosed) {
          emit(
            //todo: need localization
            state.copyWith(
              errorMessage: 'Failed to get user data. Please try again.',
              isLoading: false,
            ),
          );
        }
        return;
      }

      final result =
          await _addPhoneUseCase(
            addPhoneRequestModel: AddPhoneRequestModel(
              phoneNumber:
                  "${state.selectedCountryCode}${state.phoneNumber.removeSpaces()}",
            ),
          ).run();

      result.fold(
        (exception) {
          log('exception while adding mobile number : ${exception}');
          if (exception is AddPhoneException) {
            switch (exception) {
              case AddPhoneUnknownError(:final message):
                if (!isClosed) {
                  emit(state.copyWith(errorMessage: message, isLoading: false));
                }
                break;
            }
          } else {
            if (!isClosed) {
              emit(
                state.copyWith(
                  //todo: need localization
                  errorMessage:
                      'Failed to verify phone number. Please try again.',
                  isLoading: false,
                ),
              );
            }
          }
        },
        (verifyMobileModel) {
          // Since we only have success case now, we can directly handle it
          if (!isClosed) {
            emit(state.copyWith(isLoading: false));
            _navigation.navigateToVerifyMobile(
              args: MobileOtpVerificationArgs(
                phoneNumber: state.phoneNumber,
                countryCode: state.selectedCountryCode,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (!isClosed) {
        emit(
          //todo: need localization
          state.copyWith(
            errorMessage: 'Failed to verify phone number. Please try again.',
            isLoading: false,
          ),
        );
      }
    }
  }

  Future<void> _onValidatePhoneNumber(
    ValidatePhoneNumber event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    final phoneNumber = state.phoneNumber;
    final countryCode = state.selectedCountryCode;
    final fullNumber = '$countryCode$phoneNumber';
    String? errorMessage;
    bool isEnabled = false;

    final formattedNumber = formatNumberSync(
      fullNumber,
      phoneNumberFormat: PhoneNumberFormat.international,
    );
    try {
      await parse(formattedNumber);
      isEnabled = true;
      errorMessage = null;
    } catch (e) {
      //todo: need localization
      errorMessage =
          _checkPhoneNumberLength(formattedNumber)
              ? 'Invalid phone number'
              : null;
      isEnabled = false;
    }
    if (!isClosed) {
      emit(
        state.copyWith(
          errorMessage: errorMessage,
          isConfirmButtonEnabled: isEnabled,
        ),
      );
    }
  }

  void _onOpenHelpDialog(
    OpenHelpDialog event,
    Emitter<MobileNumberInputState> emit,
  ) {
    // TODO: [sambhav] Implement help dialog
  }

  void _onSignInPressed(
    SignInPressed event,
    Emitter<MobileNumberInputState> emit,
  ) {
    // TODO: [sambhav] Implement sign in pressed
  }

  void _resetProcessState(Emitter<MobileNumberInputState> emit) {
    emit(
      state.copyWith(
        processState: const MobileNumberInputProcessState.initial(),
        errorMessage: null,
      ),
    );
  }

  String _getExamplePlaceholderText(String countryCode) {
    final formattedNumber = formatNumberSync(
      '$countryCode 790000000000',
      phoneNumberFormat: PhoneNumberFormat.international,
    ).replaceAll(countryCode, '');
    return formattedNumber;
  }

  bool _checkPhoneNumberLength(String formattedNumber) {
    final formattedNumberLength =
        formattedNumber
            .replaceAll(state.selectedCountryCode, '')
            .removeSpaces()
            .length;
    final examplePlaceholderTextLength =
        _getExamplePlaceholderText(
          state.selectedCountryCode,
        ).removeSpaces().length;
    return formattedNumberLength >= examplePlaceholderTextLength;
  }

  String? _getUserIdFromPreferences() {
    final userId = _getUserIdUseCase();
    return userId;
  }
}
