import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/personal_details_section/mobile_number_input_view/widgets/mobile_input_initial_state.dart';

import 'bloc/mobile_number_input_bloc.dart';

class MobileNumberInputScreen extends StatefulWidget {
  const MobileNumberInputScreen({super.key});

  @override
  State<MobileNumberInputScreen> createState() =>
      _MobileNumberInputScreenState();
}

class _MobileNumberInputScreenState extends State<MobileNumberInputScreen> {
  late final TextEditingController _phoneNumberController;
  late final Future<void> _init;

  @override
  void initState() {
    super.initState();
    _init = init();
    _phoneNumberController = TextEditingController();
  }

  @override
  void dispose() {
    _phoneNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return FutureBuilder(
      future: _init,
      builder: (futureContext, snapshot) {
        if (snapshot.hasError) {
          return ErrorWidget(snapshot.error!);
        } else if (snapshot.connectionState == ConnectionState.done) {
          return BlocProvider(
            create: (createContext) => diContainer<MobileNumberInputBloc>(),
            child: BlocConsumer<MobileNumberInputBloc, MobileNumberInputState>(
              buildWhen: (previous, current) => previous != current,
              builder: (blocContext, state) {
                return switch (state.processState) {
                  MobileNumberInputLoadingState() => LoadingView(),
                  MobileNumberInputInitialState() ||
                  MobileNumberInputProcessDuplicateNumberState() =>
                    MobileInputInitialState(
                      phoneNumberController: _phoneNumberController,
                    ),
                  MobileNumberInputProcessErrorState() => SafeArea(
                    child: Scaffold(
                      body: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: DuploSpacing.spacing_xl_16,
                        ),
                        child: EmptyOrErrorStateComponent.error(
                          raiseIssueText: localization.trader_raiseATicket,
                          onRaiseIssue: () {
                            debugPrint('Raise ticket');
                          },
                          svgImage: onboarding.Assets.images.searchError.svg(),
                          title: localization.trader_somethingWentWrong,
                          description:
                              localization.trader_countryLoadingMessage,
                          retryButtonText: localization.trader_tryAgain,
                          onTapRetry: () {
                            blocContext.read<MobileNumberInputBloc>().add(
                              const MobileNumberInputEvent.initial(),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                };
              },
              listener: (
                BuildContext listenerContext,
                MobileNumberInputState state,
              ) {
                if (state.processState
                    is MobileNumberInputProcessDuplicateNumberState) {
                  _showDuplicateNumberErrorBottomSheet(listenerContext);
                }
              },
            ),
          );
        }
        return DuploLoadingIndicator(color: theme.foreground.fgPrimary);
      },
    );
  }

  void _showDuplicateNumberErrorBottomSheet(BuildContext listenerContext) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      semanticsIdentifierPrimary:
          "duplicate_number_bottom_sheet_sign_in_pressed",
      semanticsIdentifierSecondary: "duplicate_number_bottom_sheet_close",
      primaryButtonText: localization.onboarding_signInButton,
      secondaryButtonText: localization.onboarding_useAnotherNumberButton,
      onPrimaryButtonPressed:
          () => listenerContext.read<MobileNumberInputBloc>().add(
            const MobileNumberInputEvent.signInPressed(),
          ),
      onSecondaryButtonPressed: () {
        _phoneNumberController.clear();
        Navigator.of(listenerContext).pop();
      },
      title: localization.onboarding_alreadyRegisteredTitle,
      body: localization.onboarding_alreadyRegisteredMessage,
      asset: onboarding.Assets.images.duplicateNumber.svg(
        height: 160,
        width: 160,
      ),
    );
  }
}
