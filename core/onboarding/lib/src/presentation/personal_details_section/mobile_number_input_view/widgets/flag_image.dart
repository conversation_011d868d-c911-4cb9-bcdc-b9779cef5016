import 'dart:developer';
import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

class FlagImage extends StatelessWidget {
  const FlagImage({super.key, required this.countryFlag});
  final String countryFlag;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final isRunningInTest = Platform.environment.containsKey('FLUTTER_TEST');

    log('countryFlag: $countryFlag');
    return SizedBox(
      height: 16,
      width: 16,
      child:
          countryFlag.isEmpty || isRunningInTest
              ? Icon(Icons.flag, color: theme.foreground.fgQuinary, size: 16)
              : FlagProvider.getFlagFromCountryCode(countryFlag),
    );
  }
}
