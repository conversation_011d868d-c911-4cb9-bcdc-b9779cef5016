import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/presentation/personal_details_section/mobile_number_input_view/bloc/mobile_number_input_bloc.dart';
import 'package:onboarding/src/presentation/personal_details_section/mobile_number_input_view/widgets/flag_image.dart';

class MobileInputInitialState extends StatelessWidget {
  const MobileInputInitialState({
    super.key,
    required this.phoneNumberController,
  });
  final TextEditingController phoneNumberController;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    final textStyles = context.duploTextStyles;

    return BlocBuilder<MobileNumberInputBloc, MobileNumberInputState>(
      buildWhen: (previous, current) {
        return previous != current;
      },
      builder: (blocContext, state) {
        final bloc = blocContext.read<MobileNumberInputBloc>();
        return Scaffold(
          backgroundColor: theme.background.bgPrimary,
          bottomNavigationBar: SafeArea(
            top: false,
            child: Padding(
              padding: const EdgeInsets.all(
                16,
              ).copyWith(bottom: MediaQuery.viewInsetsOf(context).bottom + 16),
              child: DuploButton.defaultPrimary(
                semanticsIdentifier: 'confirm',
                title: localization.onboarding_confirmButton,
                isLoading: state.isLoading,
                loadingText: localization.onboarding_loading,
                onTap:
                    () =>
                        bloc.add(MobileNumberInputEvent.confirmButtonPressed()),
                isDisabled: !state.isConfirmButtonEnabled,
                trailingIcon:
                    Assets.images.chevronRightDirectional(blocContext).keyName,
              ),
            ),
          ),
          appBar: DuploAppBar(
            title: localization.onboarding_personalDetailsTitle,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DuploProgressBar(progressValue: .4, currentBarIndex: 1),
                DuploText(
                  text: localization.onboarding_mobilePhoneNumberTitle,
                  style: textStyles.textXl,
                  color: theme.text.textPrimary,
                  fontWeight: DuploFontWeight.semiBold,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 24),
                  child: DuploText(
                    text: localization.onboarding_mobilePhoneNumberSubtitle,
                    style: textStyles.textSm,
                    color: theme.text.textSecondary,
                    fontWeight: DuploFontWeight.regular,
                  ),
                ),
                Container(
                  child: DuploTextField(
                    key: Key('mobile_input_field'),
                    semanticsIdentifier: 'mobile_input_field',
                    controller: phoneNumberController,
                    label: localization.onboarding_phoneNumber,
                    autoFocus: true,
                    keyboardType: TextInputType.number,
                    hint: state.placeholderText,
                    onChanged: (value) {
                      final rawNumber = '${state.selectedCountryCode}${value}';
                      final formattedNumber = formatNumberSync(
                        rawNumber,
                        phoneNumberFormat: PhoneNumberFormat.international,
                      ).replaceAll(state.selectedCountryCode, '');
                      phoneNumberController.text = formattedNumber;
                      bloc.add(
                        MobileNumberInputEvent.phoneNumberChanged(
                          phoneNumber: formattedNumber,
                        ),
                      );
                    },
                    errorMessage: state.errorMessage,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    prefixIcon: InkWell(
                      onTap:
                          () => _onCountryCodeSelectorPressed(
                            bloc: bloc,
                            context: context,
                          ),
                      child: Container(
                        height: 28,
                        width: 93,
                        decoration: BoxDecoration(
                          color: theme.background.bgTertiary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            FlagImage(countryFlag: state.selectedCountryFlag),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              child: DuploText(
                                text: state.selectedCountryCode,
                                style: textStyles.textMd,
                                color: theme.text.textSecondary,
                                fontWeight: DuploFontWeight.regular,
                              ),
                            ),
                            Assets.images.chevronDown.svg(
                              height: 20,
                              width: 20,
                              colorFilter: ColorFilter.mode(
                                theme.foreground.fgQuinary,
                                BlendMode.srcIn,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    suffixIcon: InkWell(
                      onTap:
                          () => bloc.add(
                            const MobileNumberInputEvent.openHelpDialog(),
                          ),
                      child: onboarding.Assets.images.helpIcon.svg(
                        colorFilter: ColorFilter.mode(
                          theme.foreground.fgQuaternary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 6, bottom: 24),
                  child: DuploText(
                    text: localization.onboarding_consentMarketingText,
                    style: textStyles.textXs,
                    textAlign:
                        Directionality.of(blocContext) == TextDirection.ltr
                            ? TextAlign.left
                            : TextAlign.right,
                    color: theme.text.textTertiary,
                    fontWeight: DuploFontWeight.regular,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onCountryCodeSelectorPressed({
    required MobileNumberInputBloc bloc,
    required BuildContext context,
  }) {
    final localization = EquitiLocalization.of(context);
    DuploDropDown.customBottomSheetSelector(
      context: context,
      bottomSheetTitle: localization.onboarding_selectCountry,
      items:
          bloc.state.countries
              .map(
                (e) => DropDownItemModel(
                  title: "${e.name ?? ''} (${e.dialCode ?? ''})",
                  image: FlagProvider.getFlagFromCountryCode(e.code),
                ),
              )
              .toList(),
      selectedIndex: bloc.state.selectedCountryIndex,
      onChanged: (index) {
        if (index != -1) {
          bloc.add(
            MobileNumberInputEvent.countryCodeSelected(selectedIndex: index),
          );
          phoneNumberController.clear();
        }
      },
    );
  }
}
