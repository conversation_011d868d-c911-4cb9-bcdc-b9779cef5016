import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:onboarding/src/data/send_otp_request_model/send_otp_request_model.dart';
import 'package:onboarding/src/data/verify_otp_model/verify_otp_model.dart';
import 'package:onboarding/src/data/verify_otp_request_model/verify_otp_request_model.dart';
import 'package:onboarding/src/domain/exceptions/send_otp_exception/send_otp_exception.dart';
import 'package:onboarding/src/domain/exceptions/verify_otp_exception/verify_otp_exception.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:onboarding/src/domain/usecase/send_otp_usecase.dart';
import 'package:onboarding/src/domain/usecase/verify_otp_usecase.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:user_account/user_account.dart';

part 'otp_input_view_bloc.freezed.dart';
part 'otp_input_view_event.dart';
part 'otp_input_view_state.dart';

@injectable
class OtpInputViewBloc extends Bloc<OtpInputViewEvent, OtpInputViewState> {
  OtpInputViewBloc(
    this._navigation,
    this._verifyOtpUseCase,
    this._getUserIdUseCase,
    this._sendOtpUseCase,
  ) : super(const OtpInputViewState()) {
    on<Initial>(_initial);
    on<OtpChanged>(_onOtpChanged);
    on<ConfirmButtonPressed>(_onConfirmButtonPressed);
    on<OnEditPhoneNumberPressed>(_onOnEditPhoneNumberPressed);
    on<OnResendCodePressed>(_onOnResendCodePressed);

    // Dispatch Initial event immediately
    add(const Initial());
  }

  final OnboardingNavigation _navigation;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final GetUserIdUseCase _getUserIdUseCase;
  final SendOtpUseCase _sendOtpUseCase;
  // todo (sambhav) : Add and use wrong otp count
  // int _wrongOtpCount = 0;

  Future<void> _initial(Initial event, Emitter<OtpInputViewState> emit) async {
    emit(state.copyWith(isResendButtonEnabled: false, remainingTime: 20));
    await _startTimer(emit);
  }

  Future<void> _startTimer(Emitter<OtpInputViewState> emit) async {
    await emit.forEach<int>(
      Stream.periodic(
        const Duration(seconds: 1),
        (count) => state.remainingTime - 1,
      ).takeWhile((remainingTime) => remainingTime >= 0),
      onData: (remainingTime) {
        if (remainingTime == 0) {
          return state.copyWith(isResendButtonEnabled: true, remainingTime: 0);
        }
        return state.copyWith(remainingTime: remainingTime);
      },
    );
  }

  void _onOtpChanged(OtpChanged event, Emitter<OtpInputViewState> emit) {
    emit(state.copyWith(otp: event.otp, errorMessage: ''));
    if (event.otp.length == 4) {
      emit(state.copyWith(isConfirmButtonEnabled: true));
    } else {
      emit(state.copyWith(isConfirmButtonEnabled: false));
    }
  }

  Future<void> _onConfirmButtonPressed(
    ConfirmButtonPressed event,
    Emitter<OtpInputViewState> emit,
  ) async {
    emit(state.copyWith(isConfirmButtonLoading: true));

    final userId = await _getUserIdFromPreferences();
    if (userId == null) {
      if (!isClosed) {
        emit(
          //todo: need localization
          state.copyWith(
            errorMessage: 'Failed to get user data. Please try again.',
            isConfirmButtonLoading: false,
          ),
        );
      }
      return;
    }
    try {
      final result =
          await _verifyOtpUseCase(
            requestModel: VerifyOtpRequestModel(
              otpReferenceId: event.otpReferenceId,
              otpCode: state.otp,
            ),
          ).run();

      await Future.delayed(const Duration(seconds: 2), () {
        result.fold(
          (exception) {
            if (exception is VerifyOtpException) {
              switch (exception) {
                case VerifyOtpUnknownError():
                  {
                    emit(
                      state.copyWith(
                        errorMessage: exception.message,
                        isConfirmButtonLoading: false,
                      ),
                    );
                  }
              }
            } else {
              emit(
                state.copyWith(
                  isConfirmButtonLoading: false,
                  processState: OtpInputProcessState.error(),
                ),
              );
            }
          },
          (r) {
            switch (r) {
              case VerifyOtpModelSuccess():
                {
                  emit(state.copyWith(isConfirmButtonLoading: false));
                  _navigation.navigateToPhoneNumberVerified();
                }
              case VerifyOtpModelInternalServerError():
                {
                  emit(
                    state.copyWith(
                      isConfirmButtonLoading: false,
                      processState: OtpInputProcessState.error(),
                    ),
                  );
                  _resetOtpProcessState(emit);
                }
            }
            ;

            // saved for future ref
            // if (r.statusCode == 200) {
            //   // success case
            //   _navigation.navigateToPhoneNumberVerified();
            // } else if (r.statusCode == 414) {
            //   //wrong otp case
            //   _wrongOtpCount++;
            //   emit(state.copyWith(
            //     isConfirmButtonEnabled: false,
            //     errorMessage: r.message,
            //   ));
            //   if (_wrongOtpCount >= 3) {
            //     emit(state.copyWith(
            //       processState:
            //           OtpInputProcessState.tooManyUnsuccessfulAttempts(),
            //     ));
            //     _resetOtpProcessState(emit);
            //   }
            // } else {
            //   //trouble with otp case
            //   emit(state.copyWith(
            //     errorMessage: r.message,
            //     isConfirmButtonEnabled: false,
            //     processState: OtpInputProcessState.troubleWithOtp(),
            //   ));
            //   _resetOtpProcessState(emit);
            // }
          },
        );
      });
    } on Exception catch (e) {
      log('error while verifying otp: $e');
    } finally {
      if (!isClosed) {
        emit(state.copyWith(isConfirmButtonLoading: false));
      }
    }
  }

  void _onOnEditPhoneNumberPressed(
    OnEditPhoneNumberPressed event,
    Emitter<OtpInputViewState> emit,
  ) {
    _navigation.removeUntilMobileNumberInput();
  }

  Future<void> _onOnResendCodePressed(
    OnResendCodePressed event,
    Emitter<OtpInputViewState> emit,
  ) async {
    final userId = await _getUserIdFromPreferences();
    if (userId == null) {
      if (!isClosed) {
        //todo: need localization

        emit(state.copyWith(errorMessage: 'User data not found'));
      }
      return;
    }
    if (state.isResendButtonEnabled) {
      if (!isClosed) {
        emit(state.copyWith(isResendButtonEnabled: false, remainingTime: 20));
      }
      try {
        await _sendOtpUseCase(
          requestModel: SendOtpRequestModel(
            action: 'verify',
            channel: (event.args.isWhatsapp ?? false) ? 'whatsapp' : 'sms',
          ),
        ).run().then((value) {
          value.fold(
            (exception) {
              // Handle SendOtpException with switch case
              if (exception is SendOtpException) {
                switch (exception) {
                  case SendOtpUnknownError(:final message):
                    emit(state.copyWith(errorMessage: message));
                    break;
                }
              } else {
                // Handle other exceptions
                //todo: need localization
                emit(
                  state.copyWith(
                    errorMessage: 'Failed to send OTP. Please try again.',
                  ),
                );
              }
            },
            (r) {
              // todo(sambhav): otp sent successfull. should we show toast or something.
              log("otp sent successfull");
            },
          );
        });
      } on Exception catch (e) {
        log('error in send otp: $e');
      }

      await _startTimer(emit);
    }
  }

  void _resetOtpProcessState(Emitter<OtpInputViewState> emit) {
    emit(state.copyWith(processState: OtpInputProcessState.initial()));
  }

  String? _getUserIdFromPreferences() {
    return _getUserIdUseCase();
  }
}
