import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/data/send_otp_response_model/send_otp_response_model.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:prelude/prelude.dart';

import '../../../di/di_container.dart';
import 'bloc/otp_input_view_bloc.dart';

class OtpInputView extends StatefulWidget {
  const OtpInputView({
    super.key,
    required this.args,
    required this.sendOtpModel,
  });
  final MobileOtpVerificationArgs args;
  final SendOtpResponseData sendOtpModel;

  @override
  State<OtpInputView> createState() => _OtpInputViewState();
}

class _OtpInputViewState extends State<OtpInputView> {
  late TextEditingController controller;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider<OtpInputViewBloc>(
      create: (createContext) => diContainer<OtpInputViewBloc>(),
      child: BlocConsumer<OtpInputViewBloc, OtpInputViewState>(
        buildWhen: (previous, current) => previous != current,
        listener: (listenerContext, state) {
          if (state.processState is OtpInputProcessTroubleWithOtpState) {
            _showTroubleWithOtpBottomSheet(listenerContext);
          }
          if (state.processState
              is OtpInputProcessTooManyUnsuccessfulAttemptsState) {
            _showTooManyUnsuccessfulAttemptsBottomSheet(listenerContext);
          }
        },
        builder: (blocContext, state) {
          final bloc = blocContext.read<OtpInputViewBloc>();
          final remainingTime = state.remainingTime;
          final remainingTimeInString =
              remainingTime < 10
                  ? '00:0${EquitiFormatter.formatNumber(value: remainingTime, locale: Localizations.localeOf(context).toString())}'
                  : '00:${EquitiFormatter.formatNumber(value: remainingTime, locale: Localizations.localeOf(context).toString())}';
          return Scaffold(
            resizeToAvoidBottomInset: true,
            bottomNavigationBar: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.all(16).copyWith(
                  bottom: MediaQuery.viewInsetsOf(context).bottom + 16,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // get from the send otp response model
                    (false)
                        // ignore: dead_code
                        ? AnimatedContainer(
                          duration: const Duration(milliseconds: 500),
                          margin: EdgeInsets.symmetric(
                            vertical: DuploSpacing.spacing_xl_16,
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: DuploSpacing.spacing_xs_4,
                            horizontal: DuploSpacing.spacing_lg_12,
                          ),
                          decoration: BoxDecoration(
                            color: theme.background.bgPrimary,
                            borderRadius: BorderRadius.circular(
                              DuploRadius.radius_sm_6,
                            ),
                            border: Border.all(
                              color: theme.border.borderPrimary,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              onboarding.Assets.images.dot.svg(
                                height: 12,
                                width: 12,
                              ),
                              SizedBox(width: 6),
                              DuploText(
                                text: localization.onboarding_otpMightBeDelayed,
                                style: textStyles.textXs,
                                color: theme.text.textSecondary,
                                fontWeight: DuploFontWeight.medium,
                              ),
                            ],
                          ),
                        )
                        : SizedBox.shrink(),
                    DuploButton.defaultPrimary(
                      semanticsIdentifier: 'confirm',
                      title: localization.onboarding_confirmButton,
                      useFullWidth: true,
                      isLoading: state.isConfirmButtonLoading,
                      onTap:
                          () => bloc.add(
                            ConfirmButtonPressed(
                              otpReferenceId:
                                  widget.sendOtpModel.otpReferenceId,
                            ),
                          ),
                      isDisabled: !state.isConfirmButtonEnabled,
                      loadingText: localization.onboarding_loading,
                      trailingIcon:
                          Assets.images
                              .chevronRightDirectional(blocContext)
                              .keyName,
                    ),
                  ],
                ),
              ),
            ),
            backgroundColor: theme.background.bgPrimary,
            appBar: AppBar(
              backgroundColor: theme.background.bgPrimary,
              leading: DuploIconButton.link(
                onTap: () => Navigator.of(context).pop(),
                icon: onboarding.Assets.images.arrowLeft.keyName,
                iconColor: theme.foreground.fgSecondary,
              ),
              title: DuploText(
                text: localization.onboarding_personalDetailsTitle,
                style: textStyles.textSm,
                color: theme.text.textPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
            ),
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploProgressBar(progressValue: .8, currentBarIndex: 1),
                  DuploText(
                    text: localization.onboarding_verifyNumberTitle,
                    style: textStyles.textXl,
                    color: theme.text.textPrimary,
                    fontWeight: DuploFontWeight.semiBold,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 8, bottom: 10),
                    child: DuploText(
                      text:
                          (widget.args.isWhatsapp ?? false)
                              ? localization
                                  .onboarding_verifyNumberMessageWhatsapp(
                                    widget.args.phoneNumber.substring(
                                      widget.args.phoneNumber.length - 4,
                                    ),
                                  )
                              : localization.onboarding_verifyNumberMessageSms(
                                widget.args.phoneNumber.substring(
                                  widget.args.phoneNumber.length - 4,
                                ),
                              ),
                      style: textStyles.textSm,
                      textAlign:
                          Directionality.of(context) == TextDirection.rtl
                              ? TextAlign.right
                              : TextAlign.left,
                      color: theme.text.textSecondary,
                      fontWeight: DuploFontWeight.regular,
                    ),
                  ),
                  DuploTap(
                    onTap: () => bloc.add(const OnEditPhoneNumberPressed()),
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(4),
                          child: onboarding.Assets.images.edit.svg(
                            height: 15,
                            width: 15,
                            colorFilter: ColorFilter.mode(
                              theme.utility.utilityBrand700Alt,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        SizedBox(width: 4),
                        DuploText(
                          text: localization.onboarding_editPhoneNumber,
                          style: textStyles.textSm,
                          color: theme.button.buttonTertiaryFg,
                          fontWeight: DuploFontWeight.semiBold,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: DuploVerificationCodeInput(
                      key: Key('otp_input_field'),
                      controller: controller,
                      centerPinput: true,
                      autoFocus: true,
                      errorText: state.errorMessage,
                      onChanged: (value) {
                        bloc.add(OtpInputViewEvent.otpChanged(otp: value));
                      },
                    ),
                  ),
                  (state.isResendButtonEnabled)
                      ? Center(
                        child: DuploTap(
                          onTap: () {
                            bloc.add(OnResendCodePressed(args: widget.args));
                            controller.clear();
                          },
                          child: DuploText(
                            text: localization.onboarding_resendCode,
                            style: textStyles.textMd,
                            color: theme.button.buttonTertiaryFg,
                            fontWeight: DuploFontWeight.semiBold,
                          ),
                        ),
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          DuploLoadingIndicator(
                            size: Size(16, 16),
                            strokeWidth: 4,
                            color: theme.foreground.fgBrandPrimary,
                          ),
                          SizedBox(width: 8),
                          DuploText.rich(
                            spans: [
                              DuploTextSpan(
                                text: localization.onboarding_generateNewCodeIn,
                                style: textStyles.textSm,
                                color: theme.text.textSecondary,
                                fontWeight: DuploFontWeight.regular,
                              ),
                              DuploTextSpan(
                                text: remainingTimeInString,
                                style: textStyles.textMd,
                                color: theme.text.textPrimary,
                                fontWeight: DuploFontWeight.semiBold,
                              ),
                            ],
                          ),
                        ],
                      ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showTroubleWithOtpBottomSheet(BuildContext listenerContext) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      title: localization.onboarding_otpTroubleTitle,
      body: localization.onboarding_otpTroubleMessage,
      primaryButtonText: localization.onboarding_chooseAlternativeMethod,
      secondaryButtonText: localization.onboarding_editPhoneNumber,
      onPrimaryButtonPressed: () {
        print('primary button pressed');
        Navigator.of(listenerContext).pop();
        Navigator.of(listenerContext).pop();
      },
      onSecondaryButtonPressed: () {
        print('secondary button pressed');
        Navigator.of(listenerContext).pop();
        Navigator.of(listenerContext).pop();
        Navigator.of(listenerContext).pop();
      },
      tertiaryButtonText: localization.onboarding_skipForNow,
      onTertiaryButtonPressed: () {
        print('tertiary button pressed');
      },
      asset: onboarding.Assets.images.troubleWithOtp.svg(
        height: 160,
        width: 160,
      ),
    );
  }

  void _showTooManyUnsuccessfulAttemptsBottomSheet(
    BuildContext listenerContext,
  ) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      title: localization.onboarding_otpLockedTitle,
      body: localization.onboarding_otpLockedMessage,
      primaryButtonText: localization.onboarding_contactCustomerService,
      secondaryButtonText: localization.onboarding_skipForNow,
      onPrimaryButtonPressed: () {
        print('primary button pressed');
      },
      onSecondaryButtonPressed: () {
        print('secondary button pressed');
      },
      asset: onboarding.Assets.images.tooManyAttempts.svg(
        height: 160,
        width: 160,
      ),
    );
  }
}
