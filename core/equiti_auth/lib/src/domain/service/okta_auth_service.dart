import 'package:auth0_flutter/auth0_flutter.dart';
import 'package:equiti_auth/src/data/auth_config.dart';
import 'package:equiti_auth/src/data/auth_flow.dart';
import 'package:equiti_auth/src/data/auth_result.dart';
import 'package:equiti_auth/src/domain/exceptions/auth_exception.dart';
import 'package:equiti_auth/src/domain/service/auth_service.dart';
import 'package:prelude/prelude.dart';
import 'package:monitoring/monitoring.dart';

class OktaAuthService extends AuthService {
  final AuthConfig _authConfig;
  late final Auth0 _auth0;
  final LoggerBase _logger;

  OktaAuthService(this._authConfig, this._logger) {
    _auth0 = Auth0(_authConfig.domain, _authConfig.clientId);
    _logger.logDebug(
      'OktaAuthService: Initialized with domain: ${_authConfig.domain}, clientId: ${_authConfig.clientId}',
    );
  }

  @override
  TaskEither<Exception, AuthResult> login({
    required String email,
    required AuthFlow authFlow,
  }) {
    assert(
      authFlow != AuthFlow.signup,
      'Signup flow is not supported for login. Use `signup` method instead.',
    );
    _logger.logDebug('login: Starting login process for email: $email');

    // Extract configurable parameters from the AuthFlow enum
    final params = <String, String>{'login_hint': email, ...authFlow.params};
    _logger.logDebug('login: Created login parameters: $params');

    final audience = authFlow.audience;
    final scopes = authFlow.scopes;
    _logger.logDebug('login: Using audience: $audience, scopes: $scopes');

    _logger.logDebug('login: Calling _authenticate method');
    return _authenticate(audience: audience, params: params, scopes: scopes);
  }

  @override
  TaskEither<Exception, AuthResult> signup({
    required String country,
    required String countryCode,
    required String brokerId,
    String? city,
  }) {
    _logger.logDebug(
      'signup: Starting signup process for country: $country, countryCode: $countryCode, brokerId: $brokerId, city: ${city ?? 'null'}',
    );

    // Extract configurable parameters from the AuthFlow enum
    final params = <String, String>{
      'country': country,
      'country_code': countryCode,
      'broker_id': brokerId,
      if (city != null) 'city': city,
      ...AuthFlow.signup.params,
    };
    _logger.logDebug('signup: Created signup parameters: $params');

    final audience = AuthFlow.signup.audience;
    final scopes = AuthFlow.signup.scopes;
    _logger.logDebug('signup: Using audience: $audience, scopes: $scopes');

    _logger.logDebug('signup: Calling _authenticate method');
    return _authenticate(audience: audience, params: params, scopes: scopes);
  }

  @override
  TaskEither<Exception, void> logout() {
    _logger.logDebug('logout: Starting logout process');

    return TaskEither.tryCatch(
      () async {
        // todo (aakash): Set useHTTPS to true once associated domains have been set up for ios
        await _auth0
            .webAuthentication(scheme: _authConfig.scheme)
            .logout(useHTTPS: false);
        _logger.logDebug('logout: Logout completed successfully');
      },
      (error, stackTrace) {
        _logger.logError(error, stackTrace: stackTrace);
        _logger.logDebug('logout: Logout failed with error: $error');
        return _handleError(error, stackTrace);
      },
    );
  }

  @override
  TaskEither<Exception, AuthResult> refreshToken() {
    _logger.logDebug('refreshToken: Starting token refresh process');

    return TaskEither.tryCatch(
      () async {
        _logger.logDebug(
          'refreshToken: Retrieving credentials from credentials manager',
        );
        final Credentials credentials =
            await _auth0.credentialsManager.credentials();
        _logger.logDebug('refreshToken: Credentials retrieved successfully');

        _logger.logDebug(
          'refreshToken: Validating user email from credentials',
        );
        if (credentials.user.email == null || credentials.user.email!.isEmpty) {
          _logger.logWarning(
            'refreshToken: User email is null or empty from Okta',
          );
          throw Exception(
            "Failed to refresh token.Email is null or empty from Okta",
          );
        }

        _logger.logDebug(
          'refreshToken: Creating AuthResult with email: ${credentials.user.email ?? 'null'}',
        );
        final authResult = AuthResult(
          accessToken: credentials.accessToken,
          refreshToken: credentials.refreshToken,
          userData: UserData(email: credentials.user.email!),
        );
        _logger.logDebug('refreshToken: Token refresh completed successfully');
        return authResult;
      },
      (error, stackTrace) {
        _logger.logError(error, stackTrace: stackTrace);
        _logger.logDebug(
          'refreshToken: Token refresh failed with error: $error',
        );
        return _handleError(error, stackTrace);
      },
    );
  }

  TaskEither<Exception, AuthResult> _authenticate({
    required String audience,
    required Map<String, String> params,
    required Set<String> scopes,
  }) {
    _logger.logDebug(
      '_authenticate: Starting authentication with audience: $audience, params: $params, scopes: $scopes',
    );
    _logger.logDebug(
      '_authenticate: Using config - scheme: ${_authConfig.scheme}, useHttps: ${_authConfig.useHttps}',
    );

    return TaskEither.tryCatch(
      () async {
        _logger.logDebug('_authenticate: Initiating web authentication login');
        final credentials = await _auth0
            .webAuthentication(scheme: _authConfig.scheme)
            .login(
              audience: audience,
              useHTTPS: _authConfig.useHttps,
              parameters: params,
              scopes: scopes,
            );
        _logger.logDebug('_authenticate: Web authentication login completed');

        _logger.logDebug(
          '_authenticate: Validating user email from credentials',
        );
        if (credentials.user.email == null || credentials.user.email!.isEmpty) {
          _logger.logWarning(
            '_authenticate: User email is null or empty from Okta',
          );
          throw Exception(
            "Failed to authenticate user. Email is null or empty from Okta",
          );
        }

        _logger.logDebug(
          '_authenticate: Creating AuthResult with email: ${credentials.user.email ?? 'null'}',
        );
        final authResult = AuthResult(
          accessToken: credentials.accessToken,
          refreshToken: credentials.refreshToken,
          userData: UserData(email: credentials.user.email!),
        );
        _logger.logDebug(
          '_authenticate: Authentication completed successfully',
        );
        return authResult;
      },
      (error, stackTrace) {
        _logger.logError(error, stackTrace: stackTrace);
        _logger.logDebug(
          '_authenticate: Authentication failed with error: $error',
        );

        return _handleError(error, stackTrace);
      },
    );
  }

  Exception _handleError(Object error, StackTrace stackTrace) {
    if (error is WebAuthenticationException) {
      return AuthException(
        code: error.code,
        message: error.message,
        details: error.details,
        stackTrace: stackTrace,
      );
    }

    return AuthException(
      code: "UNKNOWN_AUTH_ERROR",
      message: error.toString(),
      stackTrace: stackTrace,
    );
  }
}
