import 'dart:async';

import 'package:api_client/src/http/api_client_base.dart';
import 'package:dio/dio.dart';
import 'package:equiti_auth/src/di/di_container.dart';
import 'package:locale_manager/locale_manager.dart';

class AuthInterceptor extends Interceptor {
  final String? Function() onGetToken;
  final FutureOr<void> Function() onTokenRefresh;
  final void Function() onNavigateToLogin;
  final String? dioInstanceName;
  final List<String> _requestPathsNoRequiredAuth = [
    "api/v1/users/refresh-token",
    "api/v1/getAllCountries",
    "api/v1/users",
    // The apis after this do not need the normal auth token. They require the
    // token with elevated priviledges. This token is obtained by calling the
    // login method from AuthService class in the respective repository.
    "api/v1/payment/withdrawal",
  ];
  bool _isRefreshing = false;
  final List<_QueuedRequest> _queuedRequests = [];

  // New fields for rate limiting and retry logic
  DateTime? _lastRefreshAttempt;
  static const Duration _refreshCooldown = Duration(minutes: 2);

  AuthInterceptor({
    required this.onNavigateToLogin,
    required this.onTokenRefresh,
    required this.onGetToken,
    this.dioInstanceName,
  });

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    /// TODO: There should be a Map to only add the token to the requests that need it.
    /// For now, we are adding the token to all requests. Because of this we cannot redirect to Login in case there is no token.
    try {
      final token = onGetToken();
      options.headers['Accept-Language'] =
          diContainer<LocaleManager>().getLanguageLocalCode();
      if (token != null &&
          token.isNotEmpty &&
          !_requestPathsNoRequiredAuth.contains(options.path)) {
        options.headers['Authorization'] = 'Bearer $token';
      } else {
        print('Token is null or empty');
      }
    } catch (e) {
      print('Error fetching token: $e');
    }
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401 &&
        !_requestPathsNoRequiredAuth.contains(err.requestOptions.path)) {
      // Check if we should attempt token refresh
      final now = DateTime.now();
      final canRefresh =
          _lastRefreshAttempt == null ||
          now.difference(_lastRefreshAttempt!) >= _refreshCooldown;
      // If refresh already in progress, queue this request
      if (_isRefreshing) {
        _queuedRequests.add(_QueuedRequest(err.requestOptions, handler));
        return;
      }

      // If we can't refresh due to cooldown, navigate to login
      if (!canRefresh) {
        print('Token refresh attempted too recently. Navigating to login.');
        _navigateToLoginAndFailRequests(err, handler);
        return;
      }

      _isRefreshing = true;

      try {
        _lastRefreshAttempt = now;
        await onTokenRefresh();
        // Retry the original request with new token
        final clonedRequest = await _retry(err.requestOptions);

        // Check if the retry was successful
        if (clonedRequest.statusCode == 401) {
          // New token still doesn't work, navigate to login
          print('Refreshed token still invalid. Navigating to login.');
          _navigateToLoginAndFailRequests(err, handler);
          return;
        }

        handler.resolve(clonedRequest);

        // Process queued requests
        for (final queued in _queuedRequests) {
          try {
            final retryResponse = await _retry(queued.requestOptions);
            if (retryResponse.statusCode == 401) {
              // If queued request also fails with new token, fail it
              queued.handler.reject(
                DioException(
                  requestOptions: queued.requestOptions,
                  response: retryResponse,
                  type: DioExceptionType.badResponse,
                ),
              );
            } else {
              queued.handler.resolve(retryResponse);
            }
          } catch (e) {
            queued.handler.reject(
              DioException(
                requestOptions: queued.requestOptions,
                error: e,
                type: DioExceptionType.unknown,
              ),
            );
          }
        }
        _queuedRequests.clear();
      } catch (e) {
        print('Token refresh failed: $e');
        // If refresh fails, navigate to login and fail all requests
        _navigateToLoginAndFailRequests(err, handler);
      } finally {
        _isRefreshing = false;
      }
    } else {
      handler.next(err);
    }
  }

  void _navigateToLoginAndFailRequests(
    DioException originalError,
    ErrorInterceptorHandler handler,
  ) {
    // Fail all queued requests
    for (final queued in _queuedRequests) {
      queued.handler.reject(originalError);
    }
    _queuedRequests.clear();

    // Fail the original request
    handler.reject(originalError);

    // Navigate to login
    onNavigateToLogin();
  }

  Future<Response<Object>> _retry(RequestOptions requestOptions) async {
    // Update the authorization header with the new token
    final token = onGetToken();
    if (token != null && token.isNotEmpty) {
      requestOptions.headers['Authorization'] = 'Bearer $token';
    }

    final options = Options(
      method: requestOptions.method,
      headers: requestOptions.headers,
      contentType: requestOptions.contentType,
      responseType: requestOptions.responseType,
      followRedirects: requestOptions.followRedirects,
      validateStatus: requestOptions.validateStatus,
      receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
      extra: requestOptions.extra,
    );

    return await diContainer<ApiClientBase>(
      instanceName: dioInstanceName,
    ).request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: options,
    );
  }
}

class _QueuedRequest {
  final RequestOptions requestOptions;
  final ErrorInterceptorHandler handler;

  const _QueuedRequest(this.requestOptions, this.handler);
}
