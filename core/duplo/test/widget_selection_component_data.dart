import 'package:duplo/src/models/widget_selection_model.dart';
import 'package:duplo/src/text_selection_component/widget_selection_screen.dart';
import 'package:flutter/material.dart';

Widget _showOptions(
  List<WidgetSelectionModel> options,
  int? selectedIndex,
  String pageTitle,
  String buttonTitle,
) {
  return Container(
    child: WidgetSelectionScreen(
      pageTitle: pageTitle,
      buttonTitle: buttonTitle,
      options: options,
      selected:
          selectedIndex != null ? options.elementAtOrNull(selectedIndex) : null,
      onSelection: (option) {
        print(option.identifier);
      },
    ),
  );
}

List<WidgetSelectionModel> _getOptions() {
  List<WidgetSelectionModel> options = [
    WidgetSelectionModel(
      displayWidget: Container(color: Colors.red, height: 70),
      identifier: "mid_price",
    ),
    WidgetSelectionModel(
      displayWidget: Container(color: Colors.green, height: 100),
      identifier: "buy_sell_price",
    ),
  ];
  return options;
}

final defaultSenario = _showOptions(
  _getOptions(),
  null,
  "Test Title",
  "Test Button",
);
final alreadySelectedSenario = _showOptions(
  _getOptions(),
  1,
  "Test Title",
  "Test Button",
);
final largeTextSenario = _showOptions(
  _getOptions(),
  null,
  "Test Title",
  "Test Button",
);
