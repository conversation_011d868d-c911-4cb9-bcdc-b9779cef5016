import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:duplo/src/assets/assets.gen.dart' as duplo;

class DuploLogoutTestData extends StatelessWidget {
  const DuploLogoutTestData({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return Scaffold(
      body: Center(
        child: TextButton(
          child: Text('Show logout bottom sheet'),
          onPressed:
              () => DuploSheet.showModalSheetV2<void>(
                context,
                appBar: DuploAppBar(
                  title: '',
                  automaticallyImplyLeading: false,
                ),
                content: Container(
                  height: 450,
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: DuploSpacing.spacing_xl_16,
                      right: DuploSpacing.spacing_xl_16,
                    ),
                    child: Column(
                      children: [
                        duplo.Assets.images.logoutWarning.svg(),
                        SizedBox(height: DuploSpacing.spacing_xl_16),
                        DuploText(
                          text: localization.duplo_signOutConfirmation,
                          style: textStyles.textXl,
                          color: theme.text.textPrimary,
                          fontWeight: DuploFontWeight.semiBold,
                        ),
                        SizedBox(height: DuploSpacing.spacing_md_8),
                        DuploText(
                          text:
                              localization.duplo_signOutConfirmationSubMessage,
                          style: textStyles.textSm,
                          color: theme.text.textSecondary,
                          fontWeight: DuploFontWeight.regular,
                        ),
                        SizedBox(height: DuploSpacing.spacing_4xl_32),
                        DuploButton.defaultPrimary(
                          semanticsIdentifier: "logout_signout_button",
                          title: localization.duplo_signOutTitle,
                          useFullWidth: true,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        ),
                        SizedBox(height: DuploSpacing.spacing_lg_12),
                        DuploButton.secondary(
                          semanticsIdentifier: "logout_stay_signedin_button",
                          title: localization.duplo_staySignedInTitle,
                          useFullWidth: true,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
        ),
      ),
    );
  }
}
