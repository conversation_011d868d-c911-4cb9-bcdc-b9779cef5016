import 'dart:io';

import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toastification/toastification.dart';

/// Usage: The {WithdrawScreen(walletId: '123', isFromAccountScreen: false)} app is rendered {scenarios:[withdrawSuccessScenario]}
Future<void> theAppIsRendered(
  WidgetTester tester,
  Widget app, {
  List<VoidCallback> scenarios = const <VoidCallback>[],
}) async {
  final locale =
      Platform.environment['APP_LOCALE'] == 'ar'
          ? const Locale('ar')
          : const Locale('en');
  final theme =
      Platform.environment['APP_THEME'] == "dark"
          ? DuploThemeData.dark()
          : DuploThemeData.light();
  return await AppTestConfigurator(
    tester: tester,
    app: DuploTheme(
      child: DuploTextStyles(
        locale: locale,
        child: ToastificationWrapper(
          child: MaterialApp(
            home: app,
            localizationsDelegates: EquitiLocalization.localizationsDelegates,
            supportedLocales: EquitiLocalization.supportedLocales,
            locale: locale,
          ),
        ),
      ),
      data: theme,
    ),
    isGoldenTest: true,
    scenarios: scenarios,
    onInit: () => EquitiLocalizationManager.initMock(),
  ).run();
}
