import 'package:duplo/src/sheets/duplo_sheet.dart';
import 'package:flutter/material.dart';

class DuploSheetTestData extends StatelessWidget {
  const DuploSheetTestData({required this.isFullScreen});

  final bool isFullScreen;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: TextButton(
          child: Text('Show Modal Bottom Sheet'),
          onPressed:
              () => DuploSheet.showModalSheet<void>(
                context: context,
                isFullScreen: isFullScreen,
                title: 'Example',
                content:
                    (contentContext) =>
                        Column(children: [Text("This is a modal sheet")]),
              ),
        ),
      ),
    );
  }
}

class _ModalBottomSheetListView extends StatelessWidget {
  const _ModalBottomSheetListView({required this.isFullScreen, this.height});
  final bool isFullScreen;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: TextButton(
          child: Text('Show Modal Bottom Sheet'),
          onPressed:
              () => DuploSheet.showModalSheet<void>(
                context: context,
                isFullScreen: isFullScreen,
                onClose: (isClosedUsingDrag) {
                  print("isClosedUsingDrag: $isClosedUsingDrag");
                },
                title: 'Example',
                content:
                    (contentContext) => Container(
                      height: MediaQuery.sizeOf(context).height * 0.7,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: 50,
                        itemBuilder: (itemBuilderContext, index) {
                          return ListTile(title: Text('Index is $index'));
                        },
                      ),
                    ),
              ),
        ),
      ),
    );
  }
}

final showFullBottomSheet = DuploSheetTestData(isFullScreen: true);
final showDefaultBottomSheet = DuploSheetTestData(isFullScreen: false);
final showModalSheetListViewFull = _ModalBottomSheetListView(
  isFullScreen: true,
  height: 0.7,
);
final showModalSheetListViewSmall = _ModalBottomSheetListView(
  isFullScreen: false,
  height: 0.2,
);
