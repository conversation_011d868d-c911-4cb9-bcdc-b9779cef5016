import 'package:duplo/duplo.dart';
import 'package:duplo/src/components/popup_menu/duplo_popup_menu.dart';
import 'package:flutter/material.dart';

class DuploPopupMenuData extends StatelessWidget {
  const DuploPopupMenuData({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      child: DuploPopupMenu<String>(
        items: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"],
        selectedItem: "B",
        onItemSelected: (_) {},
        itemBuilder: (builderContext, val, isSelected) {
          return Row(
            children: [
              Icon(Icons.add_business),
              const SizedBox(width: 12),
              Expanded(
                child: DuploText(
                  text: val,
                  style: DuploTextStyles.of(context).textSm,
                  fontWeight: DuploFontWeight.medium,
                  color: DuploTheme.of(context).text.textPrimary,
                ),
              ),
              const SizedBox(width: 10),
              isSelected
                  ? Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Colors.teal,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  )
                  : Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      shape: BoxShape.circle,
                    ),
                  ),
            ],
          );
        },
        icon: Row(children: [Text("Menu"), Icon(Icons.more_vert)]),
      ),
    );
  }
}
