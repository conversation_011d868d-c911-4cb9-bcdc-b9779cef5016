// ignore_for_file: prefer-number-format
import 'package:duplo/src/models/selection_option_model.dart';
import 'package:duplo/src/text_selection_component/text_selection_component_screen.dart';
import 'package:flutter/material.dart';

Widget _showOptions(
  List<SelectionOptionModel> options,
  int? selectedIndex,
  String pageTitle,
  String buttonTitle,
) {
  return Container(
    child: TextSelectionComponentScreen(
      pageTitle: pageTitle,
      buttonTitle: buttonTitle,
      options: options,
      selected:
          selectedIndex != null ? options.elementAtOrNull(selectedIndex) : null,
      onSelection: (option) {
        print(option.identifier);
      },
    ),
  );
}

List<SelectionOptionModel> _getOptions(int count, bool large) {
  String prefix = "Option";
  if (large) {
    prefix = "This is a very long option This is a very long option";
  }
  List<SelectionOptionModel> options = [];
  for (int i = 0; i < count; i++) {
    options.add(
      SelectionOptionModel(
        displayText: '$prefix ${i.toString()}',
        identifier: 'indentifier_${i.toString()}',
      ),
    );
  }

  return options;
}

final defaultSenario = _showOptions(
  _getOptions(4, false),
  null,
  "Test Title",
  "Test Button",
);
final alreadySelectedSenario = _showOptions(
  _getOptions(4, false),
  2,
  "Test Title",
  "Test Button",
);
final largeTextSenario = _showOptions(
  _getOptions(4, true),
  null,
  "Test Title",
  "Test Button",
);
