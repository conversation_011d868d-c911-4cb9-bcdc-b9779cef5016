import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

class DuploToastMessagesTestData extends StatelessWidget {
  final Widget child;
  const DuploToastMessagesTestData({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Center(child: IntrinsicHeight(child: child)));
  }
}

final successWithTitleAndDescription = DuploToastMessagesTestData(
  child: DuploToastMessage(
    titleMessage: "Trade Placed Successfully",
    descriptionMessage:
        "Your margin is too low to place this trade, please deposit more funds to your account.",
    messageType: ToastMessageType.success,
    onLeadingAction: () {
      print("onLeadingAction");
    },
    actionButtonTitle: "View Portfolio",
    onTap: () {
      print("actionButtonAction");
    },
  ),
);

final buyTrade = DuploToastMessagesTestData(
  child: DuploToastTrade(
    onLeadingAction: () => null,
    titleMessage: 'Some title',
    trade: TradeToastModel(
      symbolImage:
          "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
      symbolName: "AUDCAD",
      lotSize: "10.01",
      price: "0.1234",
      type: TradeToastType.buy,
    ),
    type: ToastMessageType.success,
    actionButtonTitle: 'View Portfolio',
  ),
);

final sellTrade = DuploToastMessagesTestData(
  child: DuploToastTrade(
    onLeadingAction: () => null,
    titleMessage: 'Some title',
    trade: TradeToastModel(
      symbolImage:
          "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
      symbolName: "AUDCAD",
      lotSize: "10.01",
      price: "0.1234",
      type: TradeToastType.sell,
    ),
    type: ToastMessageType.success,
    actionButtonTitle: 'View Portfolio',
  ),
);

final customContainerSuccess = DuploToastMessagesTestData(
  child: DuploToastDecoratorWidget(
    titleMessage: "Some custom message",
    messageType: ToastMessageType.success,
    statusColor: Colors.orange,
    contentWidget: Container(height: 100, color: Colors.red),
    onLeadingAction: () {
      print("onLeadingAction");
    },
    actionButtonTitle: "Tap to view",
    onTap: () {
      print("actionButtonAction");
    },
  ),
);
