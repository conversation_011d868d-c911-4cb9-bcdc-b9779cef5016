import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

class DuploDialogTestData extends StatelessWidget {
  const DuploDialogTestData({super.key, required this.lotSize});
  final String lotSize;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = context.duploTextStyles;

    return Scaffold(
      body: Center(
        child: TextButton(
          child: Text('Show Quick Trade Dialog'),
          onPressed:
              () => DuploDialog.showDialog(
                context: context,
                contentHeight: 300,
                title: 'Quick trade',
                content:
                    (_) => Padding(
                      padding: const EdgeInsets.only(left: 18, right: 18),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text:
                                'You have quick trade turned on, this will place a ',
                            fontWeight: DuploFontWeight.regular,
                            style: duploTextStyles.textXs,
                            color: context.duploTheme.text.textPrimary,
                          ),
                          DuploText(
                            text:
                                'Market Limit Trade right away at $lotSize Lots.',
                            fontWeight: DuploFontWeight.bold,
                            style: duploTextStyles.textXs,
                            color: context.duploTheme.text.textPrimary,
                          ),
                          SizedBox(height: 20),
                          DuploText(
                            text:
                                'Select place trade to make your trade & this message will not show again.',
                            fontWeight: DuploFontWeight.regular,
                            style: duploTextStyles.textXs,
                            color: context.duploTheme.text.textPrimary,
                          ),
                          SizedBox(height: 20),
                          SizedBox(
                            width: 300,
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Color(0xFF0C111D),
                                foregroundColor: Color(0xFFFFFFFF),
                                padding: EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                'Place Trade',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 10),
                          SizedBox(
                            width: 300,
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Color(0xFFFFFFFF),
                                foregroundColor: Color(0xFF344054),
                                padding: EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                  side: BorderSide(
                                    color: Color(0xFFD0D5DD),
                                    width: 1,
                                  ),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
              ),
        ),
      ),
    );
  }
}

final quickTradeDialogWithLotSize = DuploDialogTestData(lotSize: "1.0");
final quickTradeDialogWithNoLotSize = DuploDialogTestData(lotSize: "");
final quickTradeDialogWithLongLotSize = DuploDialogTestData(
  lotSize: "0.0000001",
);
