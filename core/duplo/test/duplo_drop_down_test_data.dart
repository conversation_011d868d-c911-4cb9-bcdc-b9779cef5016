import 'package:duplo/src/components/drop_down_selector/duplo_drop_down.dart';
import 'package:duplo/src/data/drop_down_item_model/drop_down_item_model.dart';
import 'package:flutter/material.dart';

int selectedIndex = -1;

void resetSelectedIndex() {
  selectedIndex = -1;
}

class DuploDropDownTestData extends StatefulWidget {
  const DuploDropDownTestData({super.key});

  @override
  State<DuploDropDownTestData> createState() => _DuploDropDownTestDataState();
}

class _DuploDropDownTestDataState extends State<DuploDropDownTestData> {
  final countryData = [
    {
      'name': 'United Arab Emirates',
      'image':
          'https://cdn.countryflags.com/thumbs/united-arab-emirates/flag-800.png',
    },
    {
      'name': 'United States',
      'image':
          'https://cdn.countryflags.com/thumbs/united-states-of-america/flag-800.png',
    },
    {
      'name': 'Canada',
      'image': 'https://cdn.countryflags.com/thumbs/canada/flag-800.png',
    },
    {
      'name': 'India',
      'image': 'https://cdn.countryflags.com/thumbs/india/flag-800.png',
    },
  ];

  @override
  void initState() {
    super.initState();
    resetSelectedIndex();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            DuploDropDown.selector(
              context: context,
              bottomSheetTitle: 'Select Country',
              hint: 'Nationality or citizenship',
              isLoading: false,
              dropDownItemModels:
                  countryData
                      .map(
                        (item) => DropDownItemModel(
                          title: item['name'] as String,
                          image: Image.network(
                            item['image'] as String,
                            semanticLabel: 'Country flag',
                          ),
                        ),
                      )
                      .toList(),
              selectedIndex: selectedIndex,
              onChanged: (index) {
                setState(() {
                  selectedIndex = index;
                });
              },
              helperText:
                  "We've pre-filled your country based on your location. "
                  "If this is incorrect, please select your country manually.",
            ),
          ],
        ),
      ),
    );
  }
}
