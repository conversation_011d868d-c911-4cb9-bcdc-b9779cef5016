import 'dart:developer';
import 'dart:io';

import 'package:duplo/src/di/di_container.dart';
import 'package:flutter/material.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:video_player/video_player.dart';

class DuploVideoPlayer extends StatefulWidget {
  const DuploVideoPlayer({
    super.key,
    required this.lightVideo,
    this.darkVideo,
    required this.package,
  });
  final String lightVideo;
  final String? darkVideo;
  final String package;

  @override
  State<DuploVideoPlayer> createState() => _BackgroundVideoState();
}

class _BackgroundVideoState extends State<DuploVideoPlayer> {
  VideoPlayerController? _controller;
  @override
  void didChangeDependencies() {
    if (!Platform.environment.containsKey('FLUTTER_TEST')) {
      try {
        bool isDarkMode = diContainer<ThemeManager>().isDarkMode;
        String asset =
            isDarkMode
                ? (widget.darkVideo ?? widget.lightVideo)
                : widget.lightVideo;
        _controller = VideoPlayerController.asset(
          asset,
          package: widget.package,
        );
        _controller?.setLooping(true);
        // ignore: avoid-empty-setstate, no-empty-block
        _controller?.initialize().then((_) => setState(() {}));
        _controller?.play();
      } catch (e) {
        log('Error in DuploVideoPlayer: $e');
      }
    }
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Platform.environment.containsKey('FLUTTER_TEST')
        ? Container(color: Colors.yellow)
        : VideoPlayer(_controller!);
  }
}
