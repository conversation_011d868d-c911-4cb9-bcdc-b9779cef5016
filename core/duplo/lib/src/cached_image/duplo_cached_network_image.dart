import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class DuploCachedNetworkImage extends StatelessWidget {
  const DuploCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.imageWidth,
    this.imageHeight,
    this.imageFit,
    this.errorWidget,
  });
  final String imageUrl;
  final double? imageWidth;
  final double? imageHeight;
  final BoxFit? imageFit;
  final Widget? errorWidget;

  @override
  Widget build(BuildContext context) {
    return Platform.environment.containsKey('FLUTTER_TEST')
        ? CircleAvatar(radius: 16, backgroundColor: Colors.red)
        : CachedNetworkImage(
          imageUrl: imageUrl,
          width: imageWidth,
          height: imageHeight,
          fit: imageFit,
          errorWidget:
              (errorWidgetContext, url, error) =>
                  errorWidget ?? Icon(Icons.error, size: 16),
        );
  }
}
