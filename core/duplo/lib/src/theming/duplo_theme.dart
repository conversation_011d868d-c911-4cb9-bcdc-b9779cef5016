import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:flutter/material.dart';

/// Provides the [DuploThemeData] for the current [BuildContext].
///
/// This widget is used to pass the theme data down the widget tree, allowing
/// child widgets to access the theme data using the [DuploTheme.of] method.
///
/// The [data] parameter must not be null.
class DuploTheme extends InheritedWidget {
  final DuploThemeData data;

  const DuploTheme({Key? key, required this.data, required Widget child})
    : super(key: key, child: child);

  static DuploThemeData of(BuildContext context) {
    final DuploTheme? inheritedTheme =
        context.dependOnInheritedWidgetOfExactType<DuploTheme>();
    assert(inheritedTheme != null, 'No DuploTheme found in context');
    return inheritedTheme!.data;
  }

  @override
  bool updateShouldNotify(covariant DuploTheme oldWidget) {
    return data != oldWidget.data;
  }
}
