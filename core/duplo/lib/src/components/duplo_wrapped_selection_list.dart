import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploWrappedSelectionList extends StatelessWidget {
  final List<SelectionItem> items;
  final SelectionItem? selectedItem;
  final ValueChanged<SelectionItem?> onItemSelected;
  final String title;

  const DuploWrappedSelectionList({
    Key? key,
    required this.items,
    this.selectedItem,
    required this.onItemSelected,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: title,
          style: style.textMd,
          color: theme.text.textPrimary,
          fontWeight: DuploFontWeight.semiBold,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              items.map((item) {
                final isSelected = item == selectedItem;
                return Semantics(
                  identifier: item.id,
                  child: GestureDetector(
                    onTap: () {
                      onItemSelected(isSelected ? null : item);
                    },
                    child: Container(
                      margin: isSelected ? null : const EdgeInsets.all(1),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12.0,
                        vertical: 8.0,
                      ),
                      constraints: BoxConstraints(minWidth: 16 * 2),
                      decoration: BoxDecoration(
                        color: theme.background.bgPrimary,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color:
                              isSelected
                                  ? theme.border.borderBrand
                                  : theme.border.borderTertiary,
                          width: isSelected ? 2.0 : 1.0,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          DuploText(
                            text: item.displayName,
                            style: style.textMd,
                            color: theme.text.textTertiary,
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color:
                                    isSelected
                                        ? theme.border.borderBrand
                                        : theme.border.borderPrimary,
                                width: 1,
                              ),
                              color:
                                  isSelected
                                      ? theme.text.textSuccessPrimary
                                      : Colors.transparent,
                            ),
                            child:
                                isSelected
                                    ? Icon(
                                      Icons.check,
                                      size: 12,
                                      color: theme.background.bgPrimary,
                                    )
                                    : null,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }
}

class SelectionItem {
  final String id;
  final String displayName;

  const SelectionItem({required this.id, required this.displayName});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SelectionItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
