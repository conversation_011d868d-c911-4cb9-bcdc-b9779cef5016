import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

class ValidationIndicator extends StatelessWidget {
  const ValidationIndicator({
    super.key,
    required this.isValid,
    required this.text,
  });
  final bool isValid;
  final String text;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Column(
      children: [
        Row(
          children: [
            isValid
                ? Assets.images.validPasswordRuleIc.svg()
                : Assets.images.invalidPasswordRuleIc.svg(),
            const SizedBox(width: 8),
            DuploText(
              text: text,
              style: DuploTextStyles.of(context).textXs,
              color:
                  isValid ? theme.text.textSecondary : theme.text.textTertiary,
              fontWeight: DuploFontWeight.medium,
            ),
          ],
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
