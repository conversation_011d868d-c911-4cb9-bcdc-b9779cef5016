import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_text_field/duplo_password_validator/strength_bar.dart';
import 'package:duplo/src/components/duplo_text_field/duplo_password_validator/strength_text.dart';
import 'package:duplo/src/components/duplo_text_field/duplo_password_validator/validation_indicator.dart';
import 'package:duplo/src/components/duplo_text_field/duplo_text_field.dart';
import 'package:duplo/src/models/password_validator.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class PasswordFieldValidator extends StatefulWidget {
  const PasswordFieldValidator({
    required this.onPasswordValidation,
    this.needValidationComponent = false,
    this.label,
    this.hint,
    this.errorMessage,
    this.controller,
    this.backgroundColor,
    this.autoFocus = false,
    this.semanticsIdentifier = 'enter_password',
  });

  final void Function(String password, bool isValid) onPasswordValidation;
  final bool needValidationComponent;
  final String? label;
  final String? hint;
  final String? semanticsIdentifier;

  final String? errorMessage;
  final Color? backgroundColor;
  final TextEditingController? controller;
  final bool autoFocus;

  @override
  _PasswordFieldState createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordFieldValidator> {
  final TextEditingController _passwordController = TextEditingController();
  PasswordValidator _passwordValidator = PasswordValidator();
  bool _isPasswordVisible = false;
  int _strengthLevel = 0;
  bool _isValid = false;

  void _validatePassword(String password) {
    setState(() {
      _passwordValidator.validate(password);
      _strengthLevel = _passwordValidator.strengthLevel;
      _isValid = _passwordValidator.isValid;
      widget.onPasswordValidation(password, _isValid);
    });
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploTextField(
          semanticsIdentifier: widget.semanticsIdentifier,
          errorMessage: widget.errorMessage,
          backgroundColor: widget.backgroundColor,
          controller: widget.controller ?? _passwordController,
          obscureText: !_isPasswordVisible,
          onChanged: _validatePassword,
          autoFocus: widget.autoFocus,
          label: widget.label ?? localization.duplo_passwordLabel,
          hint: widget.hint ?? localization.duplo_passwordHint,
          suffixIcon: InkWell(
            onTap: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
            child: Assets.images.showPasswordIc.svg(),
          ),
        ),
        widget.needValidationComponent
            ? Column(
              children: [
                const SizedBox(height: 10),
                Row(
                  children: [
                    StrengthBar(index: 0, strengthLevel: _strengthLevel),
                    const SizedBox(width: 4),
                    StrengthBar(index: 1, strengthLevel: _strengthLevel),
                    const SizedBox(width: 4),
                    StrengthBar(index: 2, strengthLevel: _strengthLevel),
                    SizedBox(width: 10),
                    StrengthText(strengthLevel: _strengthLevel),
                  ],
                ),
                const SizedBox(height: 10),

                // Password rules
                ValidationIndicator(
                  text: localization.duplo_useMinLength,
                  isValid: _passwordValidator.hasMinLength,
                ),
                ValidationIndicator(
                  text: localization.duplo_useSpecialChar,
                  isValid: _passwordValidator.hasSpecialChar,
                ),
                ValidationIndicator(
                  text: localization.duplo_useUpperCase,
                  isValid: _passwordValidator.hasUpperCase,
                ),
                ValidationIndicator(
                  text: localization.duplo_useLowerCase,
                  isValid: _passwordValidator.hasLowerCase,
                ),
                ValidationIndicator(
                  text: localization.duplo_useNumber,
                  isValid: _passwordValidator.hasNumber,
                ),
                const SizedBox(height: 20),
              ],
            )
            : Container(),
      ],
    );
  }
}
