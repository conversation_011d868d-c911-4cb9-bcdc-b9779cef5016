import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_base_icon_button.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_button_theme.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_button_utils.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_icon_button.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:flutter/material.dart';

/// State class for [DuploIconButton] that handles hover states and rendering of the button.
///
/// This class manages:
/// * Hover state tracking
/// * Button property calculations based on type and state
/// * Rendering of the button with appropriate styling
/// * Loading states
/// * Disabled states
/// * Focus states
/// * Color customization for different states
///
/// The button is composed of:
/// * An [InkWell] for tap and hover handling
/// * A [DuploButtonTheme] for consistent styling
/// * A [DuploBaseIconButton] for the icon display
class DuploIconButtonState extends State<DuploIconButton> {
  /// Tracks whether the button is currently being hovered over
  bool isHovered = false;

  /// Updates the hover state when the pointer enters or exits the button
  void _onHover(bool value) {
    setState(() => isHovered = value);
  }

  Color? _getBackgroundColor(DuploButtonProperties buttonProperties) {
    if (widget.isDisabled)
      return widget.disabledBackgroundColor ??
          buttonProperties.disabledBackgroundColor;
    if (isHovered)
      return widget.hoverBackgroundColor ??
          buttonProperties.hoverBackgroundColor;
    return widget.backgroundColor ?? buttonProperties.backgroundColor;
  }

  Color? _getBorderColor(DuploButtonProperties buttonProperties) {
    if (widget.isDisabled)
      return widget.disabledBorderColor ?? buttonProperties.disabledBorderColor;
    if (isHovered)
      return widget.hoverBorderColor ?? buttonProperties.hoverBorderColor;
    return widget.borderColor ?? buttonProperties.borderColor;
  }

  @override
  Widget build(BuildContext context) {
    final buttonProperties = DuploButtonUtils.getIconButtonProperties(
      type: widget.type,
      context: context,
      isFocused: widget.isFocused,
      isSelected: widget.isSelected,
      colorMode: widget.colorMode,
    );

    return DuploTap(
      onTap: widget.isDisabled ? null : widget.onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onHover: _onHover,
      useMaterial: widget.useMaterial,
      child: DuploButtonTheme(
        height: 48,
        width: 48,
        padding: EdgeInsets.all(14),
        isDisabled: widget.isDisabled,
        isFocused: widget.isFocused,
        isLinkButton: buttonProperties.isLinkButton,
        isShadowEnabled: buttonProperties.isShadowEnabled,
        borderColor: _getBorderColor(buttonProperties),
        backgroundColor: _getBackgroundColor(buttonProperties),
        isHovered: isHovered,
        child: DuploBaseIconButton(
          icon: widget.icon,
          isLoading: widget.isLoading,
          iconColor:
              widget.isDisabled
                  ? (widget.disabledColor ?? buttonProperties.disabledColor)
                  : isHovered
                  ? (widget.hoverIconColor ?? buttonProperties.hoverIconColor)
                  : (widget.iconColor ?? buttonProperties.iconColor),
        ),
      ),
    );
  }
}
