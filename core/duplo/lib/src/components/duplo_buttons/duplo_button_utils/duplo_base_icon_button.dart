import 'dart:math';

import 'package:duplo/src/components/duplo_loading_indicator.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

/// Internal widget used by [DuploIconButton] to handle the display of icons and loading states.
///
/// This widget is not intended to be used directly. Instead, use [DuploIconButton] which provides
/// a complete icon button implementation with proper theming and interaction states.
///
/// Internally handles:
/// * Display of SVG icons with optional color tinting
/// * Loading indicator states
/// * Icon sizing and color customization
///
/// @nodoc
class DuploBaseIconButton extends StatelessWidget {
  /// Creates a base icon button with the specified properties.
  ///
  /// This constructor is not intended to be used directly.
  /// Use [DuploIconButton] instead.
  const DuploBaseIconButton({
    super.key,
    required this.icon,
    required this.isLoading,
    this.iconColor,
  });

  /// The SVG asset path for the icon to display
  final String icon;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// Optional color tint to apply to the icon
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return SizedBox(
      height: 20,
      width: 20,
      child:
          isLoading
              ? DuploLoadingIndicator(
                color: iconColor ?? theme.foreground.fgPrimary,
              )
              : Transform(
                alignment: Alignment.center,
                transform:
                    Directionality.of(context) == TextDirection.rtl
                        ? Matrix4.rotationY(pi)
                        : Matrix4.rotationY(0),
                child: SvgPicture.asset(
                  icon,
                  colorFilter:
                      iconColor == null
                          ? null
                          : ColorFilter.mode(iconColor!, BlendMode.srcIn),
                ),
              ),
    );
  }
}
