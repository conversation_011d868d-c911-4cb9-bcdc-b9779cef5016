/// Utility classes and enums for configuring button styles and properties in the Duplo design system.
///
/// This file contains the core button configuration logic used by the Duplo button components.
/// It provides enums for different button types and utilities for generating button properties
/// based on the button type and state.

import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_buttons_enum.dart';
import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/theming/duplo_colors.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';

/// Utility class for generating button properties based on type and state.
class DuploButtonUtils {
  /// Generates properties for regular buttons based on type and state.
  ///
  /// Parameters:
  /// - [type] The button type that determines the base styling
  /// - [context] BuildContext for accessing theme data
  /// - [isDisabled] Whether the button is disabled
  /// - [isHovered] Whether the button is being hovered
  /// - [isFocused] Whether the button has keyboard focus
  /// - [isError] Whether the button is in an error state
  /// - [isSuccess] Whether the button is in a success state
  static DuploButtonProperties getButtonProperties({
    required DuploButtonType type,
    required BuildContext context,
    required bool isFocused,
    required bool isPayButtonDarkMode,
    required bool applePayBorderEnabled,
  }) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case DuploButtonType.defaultPrimary:
        return DuploButtonProperties(
          textColor: theme.button.buttonPrimaryFg,
          iconColor: theme.button.buttonPrimaryFg,
          hoverBackgroundColor: theme.button.buttonPrimaryBgHover,
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
          hoverIconColor: theme.button.buttonPrimaryFgHover,
          hoverTextColor: theme.button.buttonPrimaryFgHover,
        );
      case DuploButtonType.primaryBold:
        return DuploButtonProperties(
          textColor: theme.text.textPrimaryOnBrand,
          iconColor: theme.text.textPrimaryOnBrand,
          backgroundColor: theme.background.bgPrimarySolid,
          hoverBackgroundColor: theme.button.buttonPrimaryBgHover,
          disabledColor: theme.foreground.fgDisabled,
          borderColor: theme.background.bgPrimarySolid,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabledSubtle,
          hoverBorderColor: Colors.transparent,
          hoverIconColor: theme.button.buttonPrimaryFgHover,
          hoverTextColor: theme.button.buttonPrimaryFgHover,
          //todo: no tokenization for shadow
          shadows: [
            if (isFocused)
              BoxShadow(
                color: theme.background.bgPrimarySolid,
                blurRadius: 0,
                offset: Offset(0, 0),
                spreadRadius: DuploRadius.radius_xs_4,
              ),
            if (isFocused)
              BoxShadow(
                color: DuploColors.baseWhite,
                blurRadius: 0,
                offset: Offset(0, 0),
                spreadRadius: DuploRadius.radius_xxs_2,
              ),
            BoxShadow(
              color: Color(0x0C101828),
              blurRadius: DuploRadius.radius_xxs_2,
              offset: Offset(0, 1),
              spreadRadius: 0,
            ),
          ],
        );
      case DuploButtonType.secondary:
        return DuploButtonProperties(
          textColor: theme.button.buttonSecondaryFg,
          disabledBackgroundColor: theme.background.bgPrimary,
          disabledBorderColor: theme.border.borderDisabled,
          borderColor: theme.button.buttonSecondaryBorder,
          backgroundColor: theme.button.buttonSecondaryBg,
          hoverBackgroundColor: theme.button.buttonSecondaryBgHover,
          hoverBorderColor: theme.button.buttonSecondaryBorderHover,
          disabledColor: theme.foreground.fgDisabled,
          hoverIconColor: theme.button.buttonSecondaryFgHover,
          hoverTextColor: theme.button.buttonSecondaryFgHover,
        );
      case DuploButtonType.tertiary:
        return DuploButtonProperties(
          textColor: theme.text.textTertiary,
          disabledBackgroundColor: Colors.transparent,
          disabledBorderColor: Colors.transparent,
          borderColor: Colors.transparent,
          backgroundColor: Colors.transparent,
          hoverBackgroundColor: theme.button.buttonTertiaryBgHover,
          hoverBorderColor: Colors.transparent,
          isShadowEnabled: false,
          disabledColor: theme.foreground.fgDisabled,
          hoverIconColor: theme.button.buttonTertiaryFgHover,
          hoverTextColor: theme.button.buttonTertiaryFgHover,
        );
      case DuploButtonType.link:
        return DuploButtonProperties(
          textColor: theme.button.buttonTertiaryFg,
          disabledBackgroundColor: Colors.transparent,
          disabledBorderColor: Colors.transparent,
          borderColor: Colors.transparent,
          hoverBackgroundColor: Colors.transparent,
          hoverBorderColor: Colors.transparent,
          isShadowEnabled: false,
          isLinkButton: true,
          padding: EdgeInsets.all(2),
          backgroundColor:
              isFocused ? theme.background.bgPrimary : Colors.transparent,
          disabledColor: theme.foreground.fgDisabled,
          hoverIconColor: theme.button.buttonTertiaryFgHover,
          hoverTextColor: theme.button.buttonTertiaryFgHover,
        );
      case DuploButtonType.sellPrimary:
        return DuploButtonProperties(
          backgroundColor: theme.foreground.fgErrorPrimary,
          errorBackgroundColor: theme.utility.utilityWarning400,
          successBackgroundColor: theme.utility.utilityBrand300Alt,
          textColor: theme.text.textPrimaryOnBrand,
          errorTextColor: theme.foreground.fgPrimary,
          errorIconColor: theme.foreground.fgPrimary,
          successTextColor: theme.foreground.fgPrimary,
          successIconColor: theme.foreground.fgPrimary,
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
          isShadowEnabled: false,
          borderColor: theme.foreground.fgErrorPrimary,
          errorBorderColor: theme.utility.utilityWarning400,
          successBorderColor: theme.utility.utilityBrand300Alt,
          iconSize: 24,
          loadingSize: 16,
        );
      case DuploButtonType.buyPrimary:
        return DuploButtonProperties(
          backgroundColor: theme.foreground.fgSuccessPrimary,
          textColor: theme.text.textPrimaryOnBrand,
          errorBackgroundColor: theme.utility.utilityWarning400,
          successBackgroundColor: theme.utility.utilityBrand300Alt,
          errorTextColor: theme.foreground.fgPrimary,
          errorIconColor: theme.foreground.fgPrimary,
          successTextColor: theme.foreground.fgPrimary,
          successIconColor: theme.foreground.fgPrimary,
          isShadowEnabled: false,
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
          borderColor: theme.foreground.fgSuccessPrimary,
          errorBorderColor: theme.utility.utilityWarning400,
          successBorderColor: theme.utility.utilityBrand300Alt,
          iconSize: 24,
          loadingSize: 16,
        );
      case DuploButtonType.applePay:
        return DuploButtonProperties(
          title: localization.duplo_apple_pay,
          backgroundColor: isPayButtonDarkMode ? Colors.black : Colors.white,
          borderColor: applePayBorderEnabled ? Colors.black : Colors.white,
          textColor: isPayButtonDarkMode ? Colors.white : Colors.black,
          borderStrokeWidth: 1,
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
        );
      case DuploButtonType.applePayWithText:
        return DuploButtonProperties(
          title: localization.duplo_pay_with_apple_pay,
          backgroundColor: isPayButtonDarkMode ? Colors.black : Colors.white,
          borderColor: applePayBorderEnabled ? Colors.black : Colors.white,
          textColor: isPayButtonDarkMode ? Colors.white : Colors.black,
          borderStrokeWidth: 1,
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
        );

      case DuploButtonType.googlePay:
        return DuploButtonProperties(
          title: localization.duplo_pay,
          backgroundColor: isPayButtonDarkMode ? Colors.black : Colors.white,
          borderColor: isPayButtonDarkMode ? Colors.black : Colors.white,
          isShadowEnabled: !isPayButtonDarkMode,
          textColor: isPayButtonDarkMode ? Colors.white : Colors.black,
          shadows: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.33),
              blurRadius: 5,
              offset: Offset(0, 0),
              spreadRadius: 0,
            ),
          ],
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
        );

      case DuploButtonType.googlePayWithText:
        return DuploButtonProperties(
          title: localization.duplo_pay,
          backgroundColor: isPayButtonDarkMode ? Colors.black : Colors.white,
          borderColor: isPayButtonDarkMode ? Colors.black : Colors.white,
          textColor: isPayButtonDarkMode ? Colors.white : Colors.black,
          leadingText: localization.duplo_buy_with,
          isShadowEnabled: !isPayButtonDarkMode,
          shadows: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.33),
              blurRadius: 5,
              offset: Offset(0, 0),
              spreadRadius: 0,
            ),
          ],
          disabledColor: theme.foreground.fgDisabled,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
        );
    }
  }

  /// Generates properties for icon buttons based on type and state.
  ///
  /// Parameters:
  /// - [type] The icon button type that determines the base styling
  /// - [context] BuildContext for accessing theme data
  /// - [isDisabled] Whether the button is disabled
  /// - [isHovered] Whether the button is being hovered
  /// - [isFocused] Whether the button has keyboard focus
  /// - [isSelected] Whether the button is selected
  /// - [colorMode] The color mode of the button (dynamic or static)
  static DuploButtonProperties getIconButtonProperties({
    required DuploIconButtonType type,
    required BuildContext context,
    required bool isFocused,
    required bool isSelected,
    ColorMode? colorMode,
  }) {
    final theme = DuploTheme.of(context);
    switch (type) {
      case DuploIconButtonType.primary:
        return DuploButtonProperties(
          disabledColor: theme.foreground.fgDisabled,
          hoverIconColor: theme.button.buttonPrimaryFgHover,
          disabledBackgroundColor: theme.background.bgDisabled,
          disabledBorderColor: theme.border.borderDisabled,
        );
      case DuploIconButtonType.secondary:
        return DuploButtonProperties(
          disabledBackgroundColor: theme.background.bgPrimary,
          disabledBorderColor: theme.border.borderDisabled,
          borderColor: theme.button.buttonSecondaryBorder,
          backgroundColor: theme.button.buttonSecondaryBg,
          hoverBackgroundColor: theme.button.buttonSecondaryBgHover,
          hoverBorderColor: theme.button.buttonSecondaryBorderHover,
          disabledColor: theme.foreground.fgDisabled,
          hoverIconColor: theme.button.buttonSecondaryFgHover,
        );
      case DuploIconButtonType.link:
        return DuploButtonProperties(
          isLinkButton: true,
          padding: EdgeInsets.all(14),
          isShadowEnabled: false,
          disabledBackgroundColor: Colors.transparent,
          disabledBorderColor: Colors.transparent,
          borderColor: Colors.transparent,
          backgroundColor:
              isFocused ? theme.background.bgPrimary : Colors.transparent,
          hoverBackgroundColor: theme.button.buttonTertiaryBgHover,
          hoverBorderColor: Colors.transparent,
          disabledColor: theme.foreground.fgDisabled,
          hoverIconColor: theme.button.buttonTertiaryFgHover,
        );
      case DuploIconButtonType.favorite || DuploIconButtonType.customColorMode:
        assert(
          colorMode == ColorMode.dynamicMode ||
              colorMode == ColorMode.staticDarkMode,
        );
        return DuploButtonProperties(
          backgroundColor:
              colorMode == ColorMode.dynamicMode
                  ? theme.button.buttonSecondaryBg
                  : theme.chart.borderSecondaryDark,
          iconColor:
              isSelected
                  ? theme.foreground.fgBrandSecondary
                  : colorMode == ColorMode.dynamicMode
                  ? theme.button.buttonSecondaryFg
                  : theme.chart.fgSecondaryDark,
          borderColor:
              isSelected
                  ? theme.border.borderBrand
                  : colorMode == ColorMode.dynamicMode
                  ? theme.button.buttonSecondaryBorder
                  : theme.chart.borderPrimaryDark,
          shadows: [],
        );
    }
  }
}

/// Class that holds all configurable properties for Duplo buttons.
///
/// This class encapsulates all the visual properties that can be customized for buttons,
/// including colors for different states, text styling, padding, and size configurations.
class DuploButtonProperties {
  /// Colors for different button states and elements
  final Color? textColor,
      iconColor,
      disabledColor,
      disabledBackgroundColor,
      backgroundColor,
      disabledBorderColor,
      borderColor,
      hoverBackgroundColor,
      hoverBorderColor,
      hoverTextColor,
      hoverIconColor,
      successBackgroundColor,
      successIconColor,
      successBorderColor,
      successTextColor,
      errorBackgroundColor,
      errorIconColor,
      errorBorderColor,
      errorTextColor;

  /// Text styling properties
  final DuploFontWeight? textFontWeight;
  final DuploTextStyle? textStyle;

  final String? title;

  /// Button behavior flags
  final bool isShadowEnabled, isLinkButton;

  /// Layout properties
  final EdgeInsets? padding;
  final double? iconSize, loadingSize;

  /// Shadows for different button states
  final List<BoxShadow>? shadows;

  /// Border stroke width for different button states
  final double? borderStrokeWidth;

  /// Text to display before the button text and icon
  final String? leadingText;

  /// Creates a new instance of button properties with the specified configurations.
  const DuploButtonProperties({
    this.title,
    this.textColor,
    this.iconColor,
    this.disabledColor,
    this.disabledBackgroundColor,
    this.backgroundColor,
    this.disabledBorderColor,
    this.borderColor,
    this.hoverBackgroundColor,
    this.hoverBorderColor,
    this.hoverTextColor,
    this.hoverIconColor,
    this.textFontWeight,
    this.textStyle,
    this.isShadowEnabled = true,
    this.isLinkButton = false,
    this.padding,
    this.successBackgroundColor,
    this.errorBackgroundColor,
    this.iconSize,
    this.loadingSize = 20,
    this.shadows,
    this.successIconColor,
    this.successBorderColor,
    this.successTextColor,
    this.errorIconColor,
    this.errorBorderColor,
    this.errorTextColor,
    this.borderStrokeWidth,
    this.leadingText,
  });
}
