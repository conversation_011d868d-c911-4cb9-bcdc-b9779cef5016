import 'dart:math';

import 'package:duplo/src/components/duplo_loading_indicator.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Internal widget used by [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] to handle the layout and styling of button content.
///
/// This widget is not intended to be used directly. Instead, use [DuploButton] which provides
/// a complete button implementation with proper theming and interaction states.
///
/// Internally handles:
/// * Layout of button content (icons, text, loading indicator)
/// * Text and icon styling
/// * Loading states
/// * Full width options
///
/// @nodoc
class DuploBaseButton extends StatelessWidget {
  /// Creates a base button with the specified properties.
  ///
  /// This constructor is not intended to be used directly.
  /// Use [DuploButton] instead.
  const DuploBaseButton({
    this.title,
    super.key,
    this.textColor,
    this.textFontWeight,
    this.textStyle,
    this.useFullWidth = false,
    this.leadingIcon,
    this.trailingIcon,
    this.iconColor,
    this.isLoading = false,
    required this.loadingText,
    this.iconSize = 20,
    this.loadingSize = 20,
    this.useAssetColor = false,
    this.leadingText,
  });

  /// The SVG asset paths for optional leading and trailing icons
  final String? leadingIcon, trailingIcon;

  /// The button text and optional loading text
  final String? title, loadingText, leadingText;

  /// Colors for the text and icons
  final Color? textColor, iconColor;

  /// Text styling options
  final DuploFontWeight? textFontWeight;
  final DuploTextStyle? textStyle;

  /// Whether the button should take full width of its container
  final bool useFullWidth;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// Sizes for the icons and loading indicator
  final double? iconSize, loadingSize;

  final bool useAssetColor;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: useFullWidth ? MainAxisSize.max : MainAxisSize.min,
      children: [
        if (leadingText != null && !isLoading)
          Padding(
            padding: const EdgeInsets.only(right: 4),
            child: DuploText(
              text: leadingText!,
              style: textStyle ?? context.duploTextStyles.textMd,
              fontWeight: textFontWeight ?? DuploFontWeight.semiBold,
              color: textColor ?? theme.button.buttonPrimaryFg,
            ),
          ),
        if (!isLoading && leadingIcon != null)
          SizedBox(
            height: iconSize,
            width: iconSize,
            child: Transform(
              alignment: Alignment.center,
              transform:
                  Directionality.of(context) == TextDirection.rtl
                      ? Matrix4.rotationY(pi)
                      : Matrix4.rotationY(0),
              child: SvgPicture.asset(
                leadingIcon!,
                colorFilter:
                    iconColor == null || useAssetColor
                        ? null
                        : ColorFilter.mode(iconColor!, BlendMode.srcIn),
              ),
            ),
          ),
        if (isLoading)
          SizedBox(
            height: loadingSize,
            width: loadingSize,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: DuploLoadingIndicator(
                color: textColor ?? theme.button.buttonPrimaryFg,
              ),
            ),
          ),
        Flexible(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: DuploSpacing.spacing_sm_6,
            ),
            child: DuploText(
              text: isLoading ? (loadingText ?? "") : title,
              overflow: TextOverflow.ellipsis,
              style: textStyle ?? context.duploTextStyles.textMd,
              fontWeight: textFontWeight ?? DuploFontWeight.semiBold,
              color: textColor ?? theme.button.buttonPrimaryFg,
            ),
          ),
        ),
        if (!isLoading && trailingIcon != null)
          SizedBox(
            height: iconSize,
            width: iconSize,
            child: Transform(
              alignment: Alignment.center,
              transform:
                  Directionality.of(context) == TextDirection.rtl
                      ? Matrix4.rotationY(pi)
                      : Matrix4.rotationY(0),
              child: SvgPicture.asset(
                trailingIcon!,
                colorFilter:
                    iconColor == null || useAssetColor
                        ? null
                        : ColorFilter.mode(iconColor!, BlendMode.srcIn),
              ),
            ),
          ),
      ],
    );
  }
}
