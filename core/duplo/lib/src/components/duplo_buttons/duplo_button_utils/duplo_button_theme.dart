import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/theming/duplo_colors.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';

/// Internal widget used by Duplo button components to provide consistent theming.
///
/// This widget handles the visual styling of buttons in the Duplo design system,
/// including background colors, borders, shadows, and focus states.
///
/// This widget is not intended to be used directly. Instead, use the public button
/// components like [DuploButton] or [DuploIconButton].
///
/// @nodoc
class DuploButtonTheme extends StatelessWidget {
  /// Creates a themed button container.
  ///
  /// This constructor is not intended to be used directly.
  const DuploButtonTheme({
    super.key,
    this.backgroundColor,
    this.borderColor,
    this.isDisabled = false,
    this.isFocused = false,
    this.isShadowEnabled = true,
    required this.child,
    this.padding,
    this.isLinkButton = false,
    this.height,
    this.width,
    this.isHovered = false,
    this.shadows,
    this.borderStrokeWidth,
  });

  final Color? backgroundColor, borderColor;
  final double? height, width, borderStrokeWidth;
  final bool isDisabled, isFocused, isShadowEnabled, isLinkButton, isHovered;
  final EdgeInsets? padding;
  final Widget child;
  final List<BoxShadow>? shadows;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Container(
      height: height,
      width: width,
      padding:
          padding ??
          EdgeInsets.symmetric(
            horizontal: DuploSpacing.spacing_xxl_18,
            vertical: DuploSpacing.spacing_lg_12,
          ),
      decoration: ShapeDecoration(
        color: (backgroundColor ?? theme.button.buttonPrimaryBg),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: borderStrokeWidth ?? (isDisabled ? 1 : 2),
            color: (borderColor ?? theme.border.borderPrimary),
          ),
          borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
        ),
        //todo: no tokenization for shadow
        shadows:
            !isShadowEnabled
                ? []
                : (shadows ??
                    (isLinkButton && isFocused && !isHovered
                        ? [
                          BoxShadow(
                            color: Color(0x7F66D3CF),
                            blurRadius: 0,
                            offset: Offset(0, 0),
                            spreadRadius: DuploRadius.radius_xs_4,
                          ),
                          BoxShadow(
                            color: Color(0x3F000000),
                            blurRadius: DuploRadius.radius_xs_4,
                            offset: Offset(0, 4),
                            spreadRadius: 0,
                          ),
                        ]
                        : [
                          if (isFocused)
                            BoxShadow(
                              color: Color(0xFF00AFAB),
                              blurRadius: 0,
                              offset: Offset(0, 0),
                              spreadRadius: DuploRadius.radius_xs_4,
                            ),
                          if (isFocused)
                            BoxShadow(
                              color: DuploColors.baseWhite,
                              blurRadius: 0,
                              offset: Offset(0, 0),
                              spreadRadius: DuploRadius.radius_xxs_2,
                            ),
                          BoxShadow(
                            color: Color(0x0C0A0C12),
                            blurRadius: DuploRadius.radius_xxs_2,
                            offset: Offset(0, 1),
                            spreadRadius: 0,
                          ),
                        ])),
      ),
      child: child,
    );
  }
}
