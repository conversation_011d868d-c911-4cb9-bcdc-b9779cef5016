// ignore_for_file: unused_element

import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_buttons_enum.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_icon_button_state.dart';
import 'package:flutter/material.dart';

/// A customizable icon button widget that supports different styles and states.
///
/// The DuploIconButton can be configured with various visual styles through factory
/// constructors like [DuploIconButton.primary], [DuploIconButton.secondary], and
/// [DuploIconButton.link].
///
/// Features include:
/// * Different button styles (primary, secondary, link)
/// * Loading states
/// * Disabled states
/// * Hover effects
/// * Custom colors for various states
/// * Focus states
///
/// Example usage:
/// ```dart
/// DuploIconButton.primary(
///   icon: 'assets/icons/some_icon.svg',
///   onTap: () => print('Button tapped'),
///   isLoading: false,
/// )
/// ```
class DuploIconButton extends StatefulWidget {
  /// Creates a primary icon button with default styling.
  ///
  /// Use this for the main icon-based call-to-action buttons in your interface.
  factory DuploIconButton.primary({
    bool isDisabled = false,
    bool isFocused = false,
    bool isLoading = false,
    bool useMaterial = false,
    required VoidCallback onTap,
    required String icon,
    Color? iconColor,
  }) {
    return DuploIconButton._(
      type: DuploIconButtonType.primary,
      icon: icon,
      isDisabled: isDisabled,
      isFocused: isFocused,
      isLoading: isLoading,
      iconColor: iconColor,
      onTap: onTap,
      useMaterial: useMaterial,
    );
  }

  /// Creates a secondary icon button with alternative styling.
  ///
  /// Use this for less prominent actions or when you need to create visual hierarchy.
  factory DuploIconButton.secondary({
    bool isDisabled = false,
    bool isFocused = false,
    bool isLoading = false,
    bool useMaterial = false,
    required VoidCallback onTap,
    required String icon,
    Color? iconColor,
  }) {
    return DuploIconButton._(
      type: DuploIconButtonType.secondary,
      icon: icon,
      isDisabled: isDisabled,
      isFocused: isFocused,
      isLoading: isLoading,
      iconColor: iconColor,
      onTap: onTap,
      useMaterial: useMaterial,
    );
  }

  /// Creates a link-style icon button.
  ///
  /// Use this for navigation actions or when you want a minimal button appearance.
  factory DuploIconButton.link({
    bool isDisabled = false,
    bool isFocused = false,
    bool isLoading = false,
    bool useMaterial = false,
    required VoidCallback onTap,
    required String icon,
    Color? iconColor,
  }) {
    return DuploIconButton._(
      type: DuploIconButtonType.link,
      icon: icon,
      isDisabled: isDisabled,
      isFocused: isFocused,
      isLoading: isLoading,
      useMaterial: useMaterial,
      onTap: onTap,
      iconColor: iconColor,
    );
  }
  factory DuploIconButton.favorite({
    bool isDisabled = false,
    bool isFocused = false,
    bool isLoading = false,
    bool useMaterial = false,
    bool isSelected = false,
    required VoidCallback onTap,
    required ColorMode colorMode,
  }) {
    return DuploIconButton._(
      type: DuploIconButtonType.favorite,
      icon:
          isSelected
              ? Assets.images.starFilled.keyName
              : Assets.images.starOutline.keyName,
      isDisabled: isDisabled,
      isFocused: isFocused,
      isSelected: isSelected,
      isLoading: isLoading,
      useMaterial: useMaterial,
      onTap: onTap,
      colorMode: colorMode,
    );
  }

  factory DuploIconButton.customColorMode({
    bool isDisabled = false,
    bool isFocused = false,
    bool isLoading = false,
    bool useMaterial = false,
    bool isSelected = false,
    required String icon,
    String? selectedIcon,
    required VoidCallback onTap,
    required ColorMode colorMode,
  }) {
    return DuploIconButton._(
      type: DuploIconButtonType.customColorMode,
      icon: isSelected ? (selectedIcon ?? icon) : icon,
      isDisabled: isDisabled,
      isFocused: isFocused,
      isSelected: isSelected,
      isLoading: isLoading,
      useMaterial: useMaterial,
      onTap: onTap,
      colorMode: colorMode,
    );
  }

  /// Private constructor used by the factory constructors.
  const DuploIconButton._({
    super.key,
    required this.icon,
    this.isDisabled = false,
    this.isFocused = false,
    required this.onTap,
    this.iconColor,
    this.disabledColor,
    this.disabledBackgroundColor,
    this.backgroundColor,
    this.disabledBorderColor,
    this.borderColor,
    this.hoverBackgroundColor,
    this.hoverBorderColor,
    this.hoverIconColor,
    this.isLoading = false,
    required this.type,
    this.useMaterial = false,
    this.isSelected = false,
    this.colorMode,
  });

  /// The icon asset path to display in the button
  final String icon;

  /// State flags for the button
  final bool isDisabled, isFocused, isLoading, useMaterial, isSelected;

  /// Callback function when the button is tapped
  final VoidCallback onTap;

  /// Color customization options for different button states
  final Color? iconColor,
      backgroundColor,
      borderColor,
      disabledColor,
      disabledBackgroundColor,
      disabledBorderColor,
      hoverBackgroundColor,
      hoverBorderColor,
      hoverIconColor;

  /// The type of icon button (primary, secondary, or link)
  final DuploIconButtonType type;

  /// The color mode of the button
  final ColorMode? colorMode;
  @override
  State<DuploIconButton> createState() => DuploIconButtonState();
}
