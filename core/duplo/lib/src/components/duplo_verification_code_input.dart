import 'package:custom_action_keyboard/custom_action_keyboard.dart';
import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import 'package:theme_manager/theme_manager.dart';

class DuploVerificationCodeInput extends StatefulWidget {
  const DuploVerificationCodeInput({
    super.key,
    this.label,
    this.hintText,
    required this.controller,
    this.onChanged,
    this.onCompleted,
    this.onSubmitted,
    this.autoFocus,
    this.centerPinput = false,
    this.errorText,
  });
  final String? label, hintText;
  final TextEditingController controller;
  final void Function(String)? onChanged, onCompleted, onSubmitted;
  final bool? autoFocus;
  final bool centerPinput;
  final String? errorText;

  @override
  State<DuploVerificationCodeInput> createState() =>
      _DuploVerificationCodeInputState();
}

class _DuploVerificationCodeInputState
    extends State<DuploVerificationCodeInput> {
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    focusNode.removeListener(_onFocusChange);
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final pinTheme = PinTheme(
      width: 40,
      height: 40,
      textStyle: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w500,
        //todo [sambhav] somehow the textBrandPrimary is looking black so using border brand
        // color: theme.text.textBrandPrimary,
        color: theme.border.borderBrand,
      ),
      decoration: BoxDecoration(
        color: theme.background.bgPrimary,
        borderRadius: BorderRadius.circular(DuploRadius.radius_sm_6),
        border: Border.all(color: theme.border.borderPrimary),
        boxShadow: [
          BoxShadow(
            color: Color(0x0C0A0C12),
            blurRadius: 2,
            offset: Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
    );

    final preFilledWidget = DuploText(
      text: '-',
      style: textStyles.displaySm,
      fontWeight: DuploFontWeight.semiBold,
      color: theme.text.textPlaceholder,
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null)
          DuploText(
            text: widget.label!,
            style: textStyles.textSm,
            fontWeight: DuploFontWeight.medium,
            color: theme.text.textSecondary,
          ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 18),
          child: Row(
            mainAxisAlignment:
                (widget.centerPinput)
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
            children: [
              Pinput(
                key: Key('verification_code_input'),
                controller: widget.controller,
                onChanged: widget.onChanged,
                focusNode: focusNode,
                onCompleted: widget.onCompleted,
                onSubmitted: widget.onSubmitted,
                autofocus: widget.autoFocus ?? false,
                length: 4,
                forceErrorState:
                    widget.errorText != null && widget.errorText!.isNotEmpty,
                errorText: widget.errorText,
                errorTextStyle: TextStyle(
                  color: theme.text.textErrorPrimary,
                  fontSize: textStyles.textSm.fontSize,
                  height: textStyles.textSm.lineHeight,
                  fontFamily: textStyles.textSm.fontFamily,
                ),
                keyboardAppearance:
                    diContainer<ThemeManager>().isDarkMode
                        ? Brightness.dark
                        : Brightness.light,
                crossAxisAlignment: CrossAxisAlignment.center,
                preFilledWidget: preFilledWidget,
                defaultPinTheme: pinTheme,
                focusedPinTheme: pinTheme.copyWith(
                  decoration: pinTheme.decoration?.copyWith(
                    border: Border.all(
                      color: theme.border.borderBrand,
                      width: 2,
                    ),
                  ),
                ),
                submittedPinTheme: pinTheme.copyWith(
                  decoration: pinTheme.decoration?.copyWith(
                    border: Border.all(
                      color: theme.border.borderBrand,
                      width: 2,
                    ),
                  ),
                ),
                errorPinTheme: pinTheme.copyWith(
                  decoration: pinTheme.decoration?.copyWith(
                    border: Border.all(
                      color: theme.border.borderError,
                      width: 2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (widget.hintText != null)
          DuploText(
            text: widget.hintText!,
            style: textStyles.textSm,
            fontWeight: DuploFontWeight.regular,
            color: theme.text.textTertiary,
          ),
      ],
    );
  }

  void _onFocusChange() {
    diContainer<CustomActionKeyboard>().setKeyboardVisibility(
      focusNode.hasFocus,
    );
  }
}
