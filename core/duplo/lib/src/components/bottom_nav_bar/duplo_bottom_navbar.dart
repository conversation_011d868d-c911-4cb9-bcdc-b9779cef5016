import 'package:duplo/src/components/bottom_nav_bar/bottom_navbar.dart';
import 'package:duplo/src/components/bottom_nav_bar/bottom_navbar_items.dart';
import 'package:duplo/src/components/bottom_nav_bar/dublo_bottom_navbar_items.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';

class DuploBottomNavbar extends StatelessWidget {
  const DuploBottomNavbar({
    super.key,
    required this.navItems,
    this.selectedIndex = 0,
    this.onNavBarCallBack,
  });
  final List<DubloBottomNavBarItems> navItems;
  final int selectedIndex;
  final void Function(int)? onNavBarCallBack;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return Container(
      decoration: BoxDecoration(color: theme.background.bgPrimary),
      child: BottomNavbar(
        onNavBarCallBack: onNavBarCallBack,
        initialIndex: selectedIndex,
        navItems:
            navItems
                .map(
                  (e) => BottomNavbarItems(
                    label: e.title,
                    selectedIcon: e.selectedIcon,
                    unselectedIcon: e.unselectedIcon,
                    isSelected: false,
                  ),
                )
                .toList(),
      ),
    );
  }
}
