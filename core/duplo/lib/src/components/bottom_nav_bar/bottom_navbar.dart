import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/components/bottom_nav_bar/bottom_navbar_items.dart';
import 'package:flutter/material.dart';

class BottomNavbar extends StatefulWidget {
  BottomNavbar({
    Key? key,
    required this.navItems,
    this.onNavBarCallBack,
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.initialIndex = 0,
  }) : super(key: key);

  final List<BottomNavbarItems> navItems;
  final void Function(int index)? onNavBarCallBack;
  final Color selectedColor;
  final Color unselectedColor;
  final int initialIndex;

  @override
  State<BottomNavbar> createState() => _BottomNavbarState();
}

class _BottomNavbarState extends State<BottomNavbar> {
  int pageIndex = 0;
  int currentIndex = 0;
  @override
  void didUpdateWidget(covariant BottomNavbar oldWidget) {
    super.didUpdateWidget(oldWidget);

    currentIndex = widget.initialIndex;
    pageIndex = currentIndex;
  }

  void _onItemTap(int index) {
    if (pageIndex == index) return;

    setState(() {
      pageIndex = index;
      currentIndex = index;
    });

    widget.onNavBarCallBack?.call(index);
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    pageIndex = currentIndex;

    return Container(
      height: 62,
      margin: const EdgeInsetsDirectional.only(
        start: 16,
        end: 16,
        top: 8,
        bottom: 27,
      ),
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      decoration: BoxDecoration(
        color: theme.background.bgOverlay,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(widget.navItems.length, (index) {
          final isSelected = pageIndex == index;

          return Flexible(
            fit: FlexFit.tight,
            child: InkWell(
              borderRadius: BorderRadius.circular(6),
              onTap: () => _onItemTap(index),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: EdgeInsets.symmetric(
                  horizontal: MediaQuery.sizeOf(context).width < 400 ? 12 : 0,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color:
                      isSelected
                          ? theme.foreground.fgBrandSecondary
                          : Colors.transparent,
                ),
                child: widget.navItems[index].copyWith(
                  isSelected: isSelected,
                  selectedColor: widget.selectedColor,
                  unselectedColor: widget.unselectedColor,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
