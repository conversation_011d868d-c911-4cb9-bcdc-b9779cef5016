import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class BottomNavbarItems extends StatelessWidget {
  const BottomNavbarItems({
    Key? key,
    this.label,
    required this.selectedIcon,
    required this.unselectedIcon,
    this.isSelected = false,
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
  }) : super(key: key);

  final String? label;
  final Widget selectedIcon;
  final Widget unselectedIcon;
  final bool isSelected;
  final Color selectedColor;
  final Color unselectedColor;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        isSelected ? selectedIcon : unselectedIcon,
        SizedBox(height: 4),
        if (label != null)
          FittedBox(
            child: DuploText(
              text: label!,
              style: context.duploTextStyles.textXxs,
              color:
                  isSelected
                      ? theme.foreground.fgWhite
                      : theme.foreground.fgDisabled,
            ),
          ),
      ],
    );
  }

  BottomNavbarItems copyWith({
    Key? key,
    String? label,
    Widget? selectedIcon,
    Widget? unselectedIcon,
    bool? isSelected,
    Color? selectedColor,
    Color? unselectedColor,
  }) {
    return BottomNavbarItems(
      key: key ?? this.key,
      label: label ?? this.label,
      selectedIcon: selectedIcon ?? this.selectedIcon,
      unselectedIcon: unselectedIcon ?? this.unselectedIcon,
      isSelected: isSelected ?? this.isSelected,
      selectedColor: selectedColor ?? this.selectedColor,
      unselectedColor: unselectedColor ?? this.unselectedColor,
    );
  }
}
