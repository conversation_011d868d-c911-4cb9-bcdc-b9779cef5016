import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/popup_menu/duplo_popup_menu.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/models/language_model.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:locale_manager/locale_manager.dart';

class DuploLanguageSelector extends StatefulWidget {
  const DuploLanguageSelector({super.key});

  @override
  State<DuploLanguageSelector> createState() => _DuploLanguageSelectorState();
}

class _DuploLanguageSelectorState extends State<DuploLanguageSelector> {
  late String _selectedLanguage;

  @override
  void initState() {
    super.initState();
    _selectedLanguage = diContainer<LocaleManager>().locale.languageCode;
  }

  final List<LanguageModel> _languages =
      LanguageModelOptions.supportedLanguages;

  //
  @override
  Widget build(BuildContext context) {
    return DuploPopupMenu<LanguageModel>(
      items: _languages,
      selectedItem: _languages.firstWhere(
        (lang) => lang.code == _selectedLanguage,
        orElse: () => _languages.firstOrNull!,
      ),
      onItemSelected: (LanguageModel language) {
        diContainer<LocaleManager>()
            .setLocale(Locale(language.code))
            .then((value) => setState(() => _selectedLanguage = language.code));
      },
      itemBuilder:
          (builderContext, language, isSelected) => Row(
            children: [
              language.flag,
              const SizedBox(width: 12),
              Expanded(
                child: DuploText(
                  text: language.name,
                  style: DuploTextStyles.of(context).textSm,
                  fontWeight: DuploFontWeight.medium,
                  color: DuploTheme.of(context).text.textPrimary,
                ),
              ),
              const SizedBox(width: 10),
              isSelected
                  ? Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Colors.teal,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  )
                  : Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      shape: BoxShape.circle,
                    ),
                  ),
            ],
          ),
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Assets.images.languageGlobe.svg(),
          const SizedBox(width: 4),
          DuploText(
            text:
                _languages
                    .firstWhere(
                      (element) => element.code == _selectedLanguage,
                      orElse: () => _languages.firstOrNull!,
                    )
                    .displayCode,
            style: DuploTextStyles.of(context).textMd,
            fontWeight: DuploFontWeight.semiBold,
            color: DuploTheme.of(context).text.textQuaternary,
          ),
          const SizedBox(width: 8),
          Assets.images.dropDownIc.svg(),
        ],
      ),
    );
  }
}
