import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';

class DuploPopupMenu<T> extends StatefulWidget {
  const DuploPopupMenu({
    super.key,
    required this.items,
    required this.selectedItem,
    required this.onItemSelected,
    required this.itemBuilder,
    required this.icon,
  });

  final List<T> items;
  final T selectedItem;
  final ValueChanged<T> onItemSelected;
  final Widget Function(BuildContext, T, bool) itemBuilder;
  final Widget icon;

  @override
  State<DuploPopupMenu<T>> createState() => _DuploPopupMenuState<T>();
}

class _DuploPopupMenuState<T> extends State<DuploPopupMenu<T>> {
  bool _isOpen = false;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return PopupMenuButton<T>(
      onCanceled: () => setState(() => _isOpen = false),
      onOpened: () => setState(() => _isOpen = true),
      offset: const Offset(0, 45),
      color: theme.background.bgPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.border.borderSecondary),
      ),
      onSelected: (T value) {
        widget.onItemSelected(value);
        setState(() => _isOpen = false);
      },
      itemBuilder: (BuildContext builderContext) {
        return widget.items.map((item) {
          bool isSelected = item == widget.selectedItem;
          return PopupMenuItem<T>(
            value: item,
            child: widget.itemBuilder(context, item, isSelected),
          );
        }).toList();
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border:
              _isOpen
                  ? Border.all(
                    color: theme.button.buttonPrimaryBg.withOpacity(0.4),
                    width: 5,
                  )
                  : null,
          color: _isOpen ? theme.background.bgActive : Colors.transparent,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: widget.icon,
      ),
    );
  }
}
