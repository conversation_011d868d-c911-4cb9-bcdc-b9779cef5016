import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';

/// A customizable radio selector widget that displays a list of selectable options,
/// handles selection state and notifies with an index when an option is selected.
/// ```dart
/// DuploRadioSelector(
///    children: [
///      DuploText(text: 'Option 1'),
///      DuploText(text: 'Option 2'),
///      DuploText(text: 'Option 3'),
///    ],
///    contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
///    radioheight: 16,
///    onSelected: (index) {
///      // Handle selection
///    },
///    selectedIndex: 0,
/// )
class DuploRadioSelector extends StatefulWidget {
  const DuploRadioSelector({
    super.key,
    required this.children,
    this.contentPadding,
    this.radioheight,
    required this.onSelected,
    this.selectedIndex,
  });

  final List<Widget> children;
  final EdgeInsetsGeometry? contentPadding;
  final double? radioheight;
  final void Function(int) onSelected;
  final int? selectedIndex;

  @override
  State<DuploRadioSelector> createState() => _DuploRadioSelectorState();
}

class _DuploRadioSelectorState extends State<DuploRadioSelector> {
  late int selectedIndex = widget.selectedIndex ?? -1;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Column(
      children: [
        for (int i = 0; i < widget.children.length; i++) ...[
          DuploTap(
            onTap: () {
              setState(() {
                selectedIndex = i;
              });
              widget.onSelected(i);
            },
            child: Padding(
              padding:
                  widget.contentPadding ??
                  const EdgeInsets.symmetric(horizontal: 18.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  widget.children.elementAtOrNull(i) ?? const SizedBox.shrink(),
                  selectedIndex == i
                      ? Assets.images.radioBase.svg(
                        height: widget.radioheight ?? 16,
                      )
                      : Assets.images.emptyRadioBase.svg(
                        height: widget.radioheight ?? 16,
                      ),
                ],
              ),
            ),
          ),
          if (i != widget.children.length - 1)
            Divider(color: theme.border.borderSecondary),
        ],
      ],
    );
  }
}
