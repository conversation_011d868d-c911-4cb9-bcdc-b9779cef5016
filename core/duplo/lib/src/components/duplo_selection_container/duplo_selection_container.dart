import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';
import 'package:duplo/src/assets/assets.gen.dart';

/// A customizable selection container widget with optional
///leading icon, title, subtitle, and body.
/// It displays a icon to indicate selection state.
/// ```dart
/// DuploSelectionContainer(
///    isSelected: true
///    title: DuploText(
///      leading: Icon(Icons.check_circle),
///      leadingGap: 8,
///      text: title,
///      style: textStyles.textMd,
///      fontWeight: DuploFontWeight.semiBold,
///      color: theme.text.textSecondary,
///    ),
///    subTitle: DuploText(
///      text: accountSubType[i].subTitle,
///      style: textStyles.textXs,
///      color: theme.text.textSecondary,
///    ),
class DuploSelectionContainer extends StatelessWidget {
  const DuploSelectionContainer({
    super.key,
    required this.isSelected,
    this.borderRadius = 16,
    this.borderWidth = 1,
    required this.title,
    this.subTitle,
    this.body,
    this.contentPadding = const EdgeInsets.symmetric(
      horizontal: 16,
      vertical: 24,
    ),
    this.selectedIconAlignment = CrossAxisAlignment.start,
    this.gapAboveSubtitle,
    this.gapAboveBody,
    this.leading,
    this.leadingGap,
    this.backgroundColor,
  });

  final bool isSelected;
  final double borderRadius;
  final double borderWidth;
  final Widget title;
  final Widget? subTitle;
  final double? gapAboveSubtitle;
  final double? gapAboveBody;
  final Widget? body;
  final EdgeInsetsGeometry contentPadding;
  final CrossAxisAlignment selectedIconAlignment;
  final Widget? leading;
  final double? leadingGap;

  /// Default is foreground.fgWhite,
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Container(
      padding: contentPadding,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.foreground.fgWhite,
        border: Border.all(
          color:
              isSelected
                  ? theme.border.borderBrand
                  : theme.border.borderSecondary,
          width: borderWidth,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: selectedIconAlignment,
        children: [
          if (leading != null) ...[leading!, SizedBox(width: leadingGap ?? 16)],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                title,
                if (subTitle != null) ...[
                  SizedBox(height: gapAboveSubtitle ?? 8),
                  subTitle!,
                ],
                if (body != null) ...[
                  SizedBox(height: gapAboveBody ?? 24),
                  body!,
                ],
              ],
            ),
          ),
          isSelected
              ? Assets.images.radioBase.svg()
              : Assets.images.emptyRadioBase.svg(),
        ],
      ),
    );
  }
}
