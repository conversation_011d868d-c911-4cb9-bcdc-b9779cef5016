import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

/// A customizable checkbox widget that displays a title and descriptive body text.
///
/// The [DuploCheckBox] provides a checkbox with associated text content, styled according
/// to the Duplo design system. It handles its own tap interactions and state management.
///
/// Parameters:
/// * [title] - The main text displayed next to the checkbox
/// * [body] - The descriptive text shown below the title
/// * [onChanged] - Callback function that is called when the checkbox state changes
/// * [currentValue] - The current state of the checkbox (checked/unchecked). Defaults to true
/// * [padding] - Optional padding around the entire checkbox widget
/// * [decoration] - Optional BoxDecoration for styling the container around the checkbox
///
/// Example usage:
/// ```dart
/// DuploCheckBox(
///   title: 'Accept Terms',
///   body: 'I agree to the terms and conditions of the service',
///   onChanged: (bool value) {
///     // Handle checkbox state change
///     print('Checkbox value changed to: $value');
///   },
///   currentValue: true, // Initially checked
///   padding: EdgeInsets.all(16), // Optional padding
///   decoration: BoxDecoration( // Optional decoration
///     color: Colors.grey[100],
///     borderRadius: BorderRadius.circular(8),
///   ),
/// )
/// ```
class DuploCheckBox extends StatelessWidget {
  const DuploCheckBox({
    super.key,
    this.title,
    required this.body,
    required this.onChanged,
    this.currentValue = true,
    this.padding,
    this.decoration,
    this.titleStyle,
    this.bodyStyle,
    this.titleFontWeight,
    this.bodyFontWeight,
    this.titleColor,
    this.bodyColor,
  });
  final String? title;
  final String body;
  final void Function(bool value) onChanged;
  final bool currentValue;
  final EdgeInsets? padding;
  final BoxDecoration? decoration;
  final DuploTextStyle? titleStyle, bodyStyle;
  final DuploFontWeight? titleFontWeight, bodyFontWeight;
  final Color? titleColor, bodyColor;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    return Container(
      padding: padding,
      decoration: decoration,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            key: const Key("duplo_check_box"),
            onTap: () {
              onChanged(!currentValue);
            },
            child: Container(
              height: 20,
              width: 20,
              margin: EdgeInsets.only(top: 2),
              decoration: BoxDecoration(
                color:
                    currentValue
                        ? theme.background.bgBrandSolid
                        : Colors.transparent,
                border: Border.all(
                  color:
                      currentValue
                          ? Colors.transparent
                          : theme.border.borderPrimary,
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child:
                  currentValue
                      ? Assets.images.checkboxBase.svg(height: 20, width: 20)
                      : null,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null) ...[
                  DuploText(
                    text: title,
                    style: titleStyle ?? duploTextStyles.textSm,
                    fontWeight: titleFontWeight ?? DuploFontWeight.medium,
                    color: titleColor ?? theme.text.textSecondary,
                  ),
                  SizedBox(height: 2),
                ],
                DuploText(
                  text: body,
                  style: bodyStyle ?? duploTextStyles.textXs,
                  fontWeight: bodyFontWeight ?? DuploFontWeight.regular,
                  color: bodyColor ?? theme.text.textTertiary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
