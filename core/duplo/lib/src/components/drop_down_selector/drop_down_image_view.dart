import 'dart:io';

import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';

class DropDownImageView extends StatelessWidget {
  const DropDownImageView({
    super.key,
    this.selectedItemImage,
    this.height = 20,
    this.width = 20,
    this.isDisabled = false,
  });

  final Widget? selectedItemImage;
  final double height;
  final double width;
  final bool isDisabled;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return SizedBox(
      height: height,
      width: width,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(width / 2),
        child: ColorFiltered(
          colorFilter:
              isDisabled
                  ? ColorFilter.mode(theme.text.textDisabled, BlendMode.color)
                  : ColorFilter.mode(theme.text.textPrimary, BlendMode.dst),
          child:
              Platform.environment.containsKey('FLUTTER_TEST')
                  ? Icon(
                    Icons.flag,
                    color: theme.text.textPrimary,
                    size: width / 2,
                  )
                  : selectedItemImage,
        ),
      ),
    );
  }
}
