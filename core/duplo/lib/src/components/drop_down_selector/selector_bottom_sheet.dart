import 'package:duplo/src/components/drop_down_selector/drop_down_list_item.dart';
import 'package:duplo/src/components/duplo_search_input_field.dart';
import 'package:duplo/src/data/drop_down_item_model/drop_down_item_model.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';

/// A bottom sheet widget that displays a searchable list of selectable items.
///
/// This widget creates a bottom sheet with a search field at the top and a scrollable
/// list of items below it. Users can search through the items and select one using
/// radio buttons.
///
/// The items are displayed using [DropDownListItem] widgets and support both LTR
/// and RTL text directions.
class SelectorBottomSheet extends StatefulWidget {
  /// Creates a SelectorBottomSheet.
  ///
  /// [items] is the list of items to display, where each item is a Map containing
  /// at least 'name' and 'image' keys.
  /// [selectedIndex] is the index of the currently selected item.
  /// [onChanged] is called when the user selects a different item.
  const SelectorBottomSheet({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onChanged,
    this.hintText,
  });

  /// The list of items to display in the bottom sheet.
  /// Each item should be a Map containing at least 'name' and 'image' keys.
  final List<DropDownItemModel> items;

  /// The index of the currently selected item.
  final int selectedIndex;

  /// Callback function that is called when the user selects a different item.
  /// The parameter is the index of the newly selected item.
  final void Function(int index) onChanged;

  /// The hint text to display in the search input field.
  final String? hintText;

  @override
  State<SelectorBottomSheet> createState() => _SelectorBottomSheetState();
}

class _SelectorBottomSheetState extends State<SelectorBottomSheet> {
  /// Index of the selected item in the original items list
  int selectedIndex = -1;

  /// Index of the selected item in the filtered/searched items list
  int searchedItemSelectedIndex = -1;

  /// List of items that match the current search query
  List<DropDownItemModel> searchedItems = [];

  /// Controller for the search text field
  final textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.selectedIndex;
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final itemsList = searchedItems.isEmpty ? widget.items : searchedItems;
    final isLTR = Directionality.of(context) == TextDirection.ltr;
    return Container(
      color: theme.background.bgPrimary,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
        child: Column(
          children: [
            DuploSearchInputField(
              key: const Key('search_box_bottom_sheet'),
              controller: textController,
              onChanged: _onTextFieldChanged,
              hintText: widget.hintText,
            ),
            SizedBox(height: 16),
            Expanded(
              child: Scrollbar(
                thumbVisibility: true,
                trackVisibility: true,
                thickness: 8,
                radius: Radius.circular(999),
                child: ListView.separated(
                  itemCount: itemsList.length,
                  shrinkWrap: true,
                  padding: EdgeInsets.only(
                    right: isLTR ? 20 : 0,
                    left: isLTR ? 0 : 20,
                  ),
                  separatorBuilder: (_, index) => SizedBox(height: 8),
                  itemBuilder: (_, index) {
                    final item = itemsList[index];
                    return DropDownListItem(
                      selectedItem: item,
                      isSelected:
                          searchedItems.isEmpty
                              ? index == selectedIndex
                              : index == searchedItemSelectedIndex,
                      onChanged: (bool? value) => _onRadioChanged(index: index),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Handles changes in the search text field.
  ///
  /// When the search field is empty, clears the filtered list.
  /// When there is a search query, filters the items list based on the query
  /// and updates the selected index in the filtered list.
  void _onTextFieldChanged(String value) {
    if (value.isEmpty) {
      setState(() {
        searchedItems = [];
      });
    } else {
      searchedItems =
          widget.items
              .where(
                (element) =>
                    element.title.toLowerCase().contains(value.toLowerCase()),
              )
              .toList();

      final currentSelectedIndex =
          searchedItems.isEmpty
              ? selectedIndex
              : (selectedIndex == -1)
              ? -1
              : searchedItems.indexOf(
                widget.items.elementAtOrNull(selectedIndex) ??
                    DropDownItemModel(title: ''),
              );

      setState(() {
        searchedItemSelectedIndex = currentSelectedIndex;
      });
    }
  }

  /// Handles selection changes when a radio button is toggled.
  ///
  /// Updates both the selected index in the original list and in the filtered list
  /// (if search is active). Calls the onChanged callback with the new selection.
  void _onRadioChanged({required int index}) {
    final localSelectedIndex =
        searchedItems.isEmpty
            ? index
            : widget.items.indexOf(
              searchedItems.elementAtOrNull(index) ??
                  DropDownItemModel(title: ''),
            );
    setState(() {
      selectedIndex = localSelectedIndex;
      searchedItemSelectedIndex = index;
    });
    widget.onChanged(localSelectedIndex);
  }
}
