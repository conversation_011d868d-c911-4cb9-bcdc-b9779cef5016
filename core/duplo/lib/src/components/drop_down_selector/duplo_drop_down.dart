import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/drop_down_selector/drop_down_image_view.dart';
import 'package:duplo/src/components/drop_down_selector/selector_bottom_sheet.dart';
import 'package:duplo/src/components/duplo_app_bar.dart';
import 'package:duplo/src/components/duplo_loading_indicator.dart';
import 'package:duplo/src/data/drop_down_item_model/drop_down_item_model.dart';
import 'package:duplo/src/sheets/duplo_sheet.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

/// A generic dropdown selector used for choosing from a list of items (e.g. country, city, etc).
///
/// Use this factory when you want to show a bottom sheet selector with search and auto-dismiss behavior.
///
/// If you want to implement a **custom onTap behavior** (e.g., navigating to another screen or showing your own modal),
/// use the default `DuploDropDown` constructor instead and pass your own `onTap` handler.
///
/// Example using bottom sheet:
/// ```dart
/// DuploDropDown.selector(
///   hint: 'Select Country',
///   title: 'Select Country',
///   context: context,
///   dropDownItemModels: countryList,
///   selectedIndex: selectedIndex,
///   onChanged: (index) => setState(() => selectedIndex = index),
/// );
/// ```
///
/// Example using custom tap handler:
/// ```dart
/// DuploDropDown(
///   hint: 'Select Language',
///   selectedItem: selectedLanguageItem,
///   onTap: () {
///     // Navigate or show custom sheet
///   },
/// )
/// ```
///
/// Parameters:
/// - [hint]: Placeholder text shown when no item is selected.
/// - [helperText]: Optional text shown below the dropdown.
/// - [isDisabled]: Whether the dropdown is non-interactive (default: false).
/// - [isLoading]: Whether to show a loading spinner (default: false).
/// - [dropDownItemModels]: List of items shown in the selector.
/// - [onChanged]: Callback triggered when an item is selected.
/// - [selectedIndex]: Index of the currently selected item. Use -1 if nothing is selected.
/// - [context]: Build context to open the bottom sheet.
/// - [title]: Title shown in the bottom sheet.
/// - [shouldAutoPop]: Whether to auto-close the sheet after selection (default: true).
/// - [hintText]: Optional search placeholder in the selector sheet.
///
class DuploDropDown extends StatelessWidget {
  factory DuploDropDown.selector({
    required String hint,
    String? helperText,
    bool isDisabled = false,
    bool isLoading = false,
    required List<DropDownItemModel> dropDownItemModels,
    required void Function(int index) onChanged,
    required int selectedIndex,
    required BuildContext context,
    String? bottomSheetTitle,
    bool shouldAutoPop = true,
    String? hintText,
    String? semanticsIdentifier,
  }) {
    return DuploDropDown(
      hint: hint,
      selectedItem:
          selectedIndex == -1
              ? null
              : dropDownItemModels.elementAtOrNull(selectedIndex),
      helperText: helperText,
      isLoading: isLoading,
      onTap: () {
        if (isDisabled || isLoading) return;
        customBottomSheetSelector(
          context: context,
          bottomSheetTitle: bottomSheetTitle ?? 'Select Country',
          items: dropDownItemModels,
          selectedIndex: selectedIndex,
          onChanged: onChanged,
          shouldAutoPop: shouldAutoPop,
          hintText: hintText,
        );
      },

      isDisabled: isDisabled,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  const DuploDropDown({
    super.key,
    required this.hint,
    this.helperText,
    this.onTap,
    this.isDisabled = false,
    this.isLoading = false,
    this.semanticsIdentifier,
    this.selectedItem,
  });

  /// The currently selected item text to display in the dropdown
  final DropDownItemModel? selectedItem;

  /// Helper text shown below the dropdown
  final String? helperText;

  /// Placeholder text shown when no item is selected
  final String hint;

  /// Callback function when dropdown is tapped
  final VoidCallback? onTap;

  /// Whether the dropdown is disabled/non-interactive
  final bool isDisabled;

  /// Whether the dropdown is in loading state
  final bool isLoading;

  /// Semantics identifier for the dropdown
  final String? semanticsIdentifier;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final backgroundColor =
        isDisabled ? theme.background.bgDisabled : theme.background.bgSecondary;
    final borderColor =
        isDisabled ? theme.border.borderDisabled : theme.border.borderSecondary;
    final textColor =
        isDisabled ? theme.text.textDisabled : theme.text.textPrimary;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onTap,
          child: Semantics(
            identifier: semanticsIdentifier,
            child: Container(
              height: 75,
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                color: backgroundColor,
                border: Border.all(color: borderColor),
                borderRadius: BorderRadius.circular(6),
                // todo: token for shadow is pending
                boxShadow: [
                  BoxShadow(
                    color: Color(0x2398A2B3),
                    blurRadius: 0,
                    offset: Offset(0, 0),
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: Color(0x0C101828),
                    blurRadius: 2,
                    offset: Offset(0, 1),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (selectedItem != null)
                    DuploText(
                      text: hint,
                      style: duploTextStyles.textXs,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                    ),
                  if (selectedItem != null) SizedBox(height: 6),
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            if (selectedItem?.image != null)
                              DropDownImageView(
                                selectedItemImage: selectedItem?.image,
                                isDisabled: isDisabled,
                              ),
                            if (selectedItem?.image != null) SizedBox(width: 8),
                            Expanded(
                              child: DuploText(
                                text: selectedItem?.title ?? hint,
                                style: duploTextStyles.textMd,
                                textAlign:
                                    Directionality.of(context) ==
                                            TextDirection.ltr
                                        ? TextAlign.left
                                        : TextAlign.right,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                fontWeight: DuploFontWeight.regular,
                                color: textColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 8),
                      if (isLoading)
                        DuploLoadingIndicator(
                          color: textColor,
                          size: Size(18, 18),
                          strokeWidth: 4,
                        )
                      else
                        Assets.images.chevronDown.svg(
                          height: 24,
                          width: 24,
                          colorFilter: ColorFilter.mode(
                            textColor,
                            BlendMode.srcIn,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (helperText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6, left: 8),
            child: DuploText(
              text: helperText!,
              style: duploTextStyles.textXs,
              fontWeight: DuploFontWeight.regular,
              color: theme.text.textTertiary,
            ),
          ),
      ],
    );
  }

  /// Opens a bottom sheet containing the list of selectable items
  static void customBottomSheetSelector({
    required BuildContext context,
    required String bottomSheetTitle,
    required List<DropDownItemModel> items,
    required int selectedIndex,
    required void Function(int index) onChanged,
    bool shouldAutoPop = true,
    String? hintText,
  }) {
    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        title: bottomSheetTitle,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      content: SelectorBottomSheet(
        hintText: hintText,
        items: items,
        selectedIndex: selectedIndex,
        onChanged: (index) {
          onChanged(index);
          if (shouldAutoPop) {
            Navigator.pop(context);
          }
        },
      ),
    );
  }
}
