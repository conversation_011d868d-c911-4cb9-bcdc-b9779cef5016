import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:duplo/src/typography/text_styles.dart';
import 'package:duplo/src/utilities/widget_calculations.dart';
import 'package:flutter/material.dart';

class DuploTagContainer extends StatelessWidget {
  factory DuploTagContainer.xs({
    required String text,
    Widget? leading,
    Widget? trailing,
    DuploTagType type = DuploTagType.neutral,
  }) {
    return DuploTagContainer._(
      text: text,
      size: DuploTagSize.xs,
      type: type,
      leading: leading,
      trailing: trailing,
      borderRadius: 4,
    );
  }

  factory DuploTagContainer.sm({
    required String text,
    Widget? leading,
    Widget? trailing,
    DuploTagType type = DuploTagType.neutral,
  }) {
    return DuploTagContainer._(
      text: text,
      size: DuploTagSize.sm,
      type: type,
      leading: leading,
      trailing: trailing,
      borderRadius: 6,
    );
  }

  factory DuploTagContainer.md({
    required String text,
    Widget? leading,
    Widget? trailing,
    DuploTagType type = DuploTagType.neutral,
  }) {
    return DuploTagContainer._(
      text: text,
      size: DuploTagSize.md,
      type: type,
      leading: leading,
      trailing: trailing,
      borderRadius: 6,
    );
  }

  factory DuploTagContainer.lg({
    required String text,
    Widget? leading,
    Widget? trailing,
    DuploTagType type = DuploTagType.neutral,
  }) => DuploTagContainer._(
    text: text,
    leading: leading,
    trailing: trailing,
    size: DuploTagSize.lg,
    type: type,
    borderRadius: 6,
  );

  const DuploTagContainer._({
    required this.text,
    this.leading,
    this.trailing,
    required this.size,
    this.type = DuploTagType.neutral,
    this.borderRadius = 4,
  });
  final String text;
  final Widget? leading;
  final Widget? trailing;
  final DuploTagSize size;
  final DuploTagType type;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = context.duploTextStyles;

    return Container(
      padding: _getPadding(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          leading != null
              ? SizedBox(width: 16, height: 16, child: leading!)
              : const SizedBox.shrink(),
          const SizedBox(width: 4),
          DuploText(
            text: text,
            style: _getTextStyle(textStyles),
            fontWeight: DuploFontWeight.medium,
            color: _getTextColor(theme),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 4),
          trailing != null
              ? SizedBox(width: 16, height: 16, child: trailing!)
              : const SizedBox.shrink(),
        ],
      ),
      decoration: BoxDecoration(
        color: _getBackgroundColor(theme),
        border: Border.all(color: _getBorderColor(theme), width: 1),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }

  EdgeInsets _getPadding() => switch (size) {
    DuploTagSize.xs ||
    DuploTagSize.sm => const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
    DuploTagSize.md => const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
    DuploTagSize.lg => const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
  };

  DuploTextStyle _getTextStyle(TextStyles textStyles) => switch (size) {
    DuploTagSize.xs => textStyles.textXxs,
    DuploTagSize.sm => textStyles.textXs,
    DuploTagSize.md || DuploTagSize.lg => textStyles.textSm,
  };

  Color _getBorderColor(DuploThemeData theme) => switch (type) {
    DuploTagType.neutral => theme.utility.utilityGray200,
    DuploTagType.brand => theme.utility.utilityBrand200,
    DuploTagType.error => theme.utility.utilityError200,
    DuploTagType.warning => theme.utility.utilityWarning200,
    DuploTagType.success => theme.utility.utilitySuccess200,
  };

  Color _getBackgroundColor(DuploThemeData theme) => switch (type) {
    DuploTagType.neutral => theme.utility.utilityGray50,
    DuploTagType.brand => theme.utility.utilityBrand50,
    DuploTagType.error => theme.utility.utilityError50,
    DuploTagType.warning => theme.utility.utilityWarning50,
    DuploTagType.success => theme.utility.utilitySuccess50,
  };

  Color _getTextColor(DuploThemeData theme) => switch (type) {
    DuploTagType.neutral => theme.utility.utilityGray700,
    DuploTagType.brand => theme.utility.utilityBrand700,
    DuploTagType.error => theme.utility.utilityError700,
    DuploTagType.warning => theme.utility.utilityWarning700,
    DuploTagType.success => theme.utility.utilitySuccess700,
  };

  double getDuploTagContainerHeight({required BuildContext context}) {
    final textStyles = context.duploTextStyles;
    final textStyle = _getTextStyle(textStyles);
    final textHeight =
        measureText(
          context: context,
          text: text,
          textStyle: textStyle.toTextStyle(),
        ).height;

    final padding = _getPadding();

    return textHeight + padding.vertical;
  }
}

enum DuploTagType { neutral, brand, error, warning, success }

enum DuploTagSize { xs, sm, md, lg }
