import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:duplo/src/components/toast_messages/duplo_toast_container.dart';
import 'package:duplo/src/components/toast_messages/toast_message_type.dart';
import 'package:duplo/src/data/trade_toast_type.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploToastTrade extends StatelessWidget {
  final GestureTapCallback onLeadingAction;
  final String titleMessage;
  final ToastMessageType type;
  final TradeToastModel trade;
  final String? actionButtonTitle;
  final Color? priceColor;
  final bool isPriceCentered;

  const DuploToastTrade({
    super.key,
    required this.onLeadingAction,
    required this.titleMessage,
    required this.trade,
    required this.type,
    this.actionButtonTitle,
    this.priceColor,
    this.isPriceCentered = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    String stateText = switch (trade.type) {
      TradeToastType.buy => "BUY",
      TradeToastType.sell => "SELL",
      TradeToastType.buySellCombined => "SELL|BUY",
    };
    if (trade.lotSize != null) {
      stateText += " ${trade.lotSize} lots";
    }

    final symbolImage =
        Platform.environment.containsKey('FLUTTER_TEST')
            ? CircleAvatar(
              radius: 16,
              backgroundColor: theme.text.textErrorPrimary,
            )
            : CachedNetworkImage(
              width: 32,
              height: 32,
              imageUrl: trade.symbolImage,
              errorWidget:
                  (_, __, ___) => const Icon(Icons.broken_image, size: 32),
            );

    final contentWidget = Container(
      decoration: BoxDecoration(
        color:
            trade.type == TradeToastType.buy
                ? theme.foreground.fgSuccessPrimary
                : theme.foreground.fgErrorPrimary,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.border.borderPrimary, width: 1.0),
        boxShadow: [
          BoxShadow(
            color: theme.shadow.shadowOverlayLg.withOpacity(0.3),
            spreadRadius: -2,
            blurRadius: 4,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              width: 4.0,
              color: switch (trade.type) {
                TradeToastType.buy => theme.foreground.fgSuccessPrimary,
                TradeToastType.sell => theme.foreground.fgErrorPrimary,
                TradeToastType.buySellCombined => theme.border.borderPrimary,
              },
            ),
          ),
          color: theme.background.bgPrimary,
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            symbolImage,
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DuploText(
                    text: trade.symbolName,
                    style: duploTextStyles.textSm,
                    color: theme.text.textPrimary,
                    fontWeight: DuploFontWeight.medium,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      color: switch (trade.type) {
                        TradeToastType.buy => theme.utility.utilitySuccess50,
                        TradeToastType.sell => theme.utility.utilityError50,
                        TradeToastType.buySellCombined =>
                          theme.background.bgTertiary,
                      },
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: const Color.fromRGBO(171, 239, 198, 0.3),
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 4,
                    ),
                    child: DuploText(
                      text: stateText,
                      style: duploTextStyles.textXs,
                      color: switch (trade.type) {
                        TradeToastType.buy => theme.utility.utilitySuccess700,
                        TradeToastType.sell => theme.utility.utilityError700,
                        TradeToastType.buySellCombined =>
                          theme.text.textPrimary,
                      },
                      fontWeight: DuploFontWeight.medium,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              height: 48,
              child: Align(
                alignment:
                    isPriceCentered
                        ? Alignment.centerRight
                        : Alignment.topRight,
                child: Row(
                  children: [
                    DuploText(
                      text: trade.price,
                      style: duploTextStyles.textSm,
                      color: priceColor ?? theme.text.textSecondary,
                      fontWeight: DuploFontWeight.bold,
                    ),
                    if (trade.currency != null)
                      DuploText(
                        text: " " + trade.currency!,
                        style: duploTextStyles.textXs,
                        color: priceColor ?? theme.text.textSecondary,
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );

    return DuploToastContainer(
      onLeadingAction: onLeadingAction,
      titleMessage: titleMessage,
      messageType: type,
      statusIconColor:
          trade.type == TradeToastType.buy
              ? theme.utility.utilitySuccess600
              : theme.utility.utilityError600,
      contentWidget: contentWidget,
    );
  }
}
