import 'package:duplo/src/components/toast_messages/duplo_toast_container.dart';
import 'package:duplo/src/components/toast_messages/toast_message_type.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploToastMessage extends StatelessWidget {
  final GestureTapCallback onLeadingAction;
  final String titleMessage;
  final String descriptionMessage;
  final ToastMessageType messageType;
  final String? actionButtonTitle;
  final GestureTapCallback? onTap;
  final String? primaryButtonTitle;

  const DuploToastMessage({
    super.key,
    required this.onLeadingAction,
    required this.titleMessage,
    required this.descriptionMessage,
    required this.messageType,
    this.actionButtonTitle,
    this.onTap,
    this.primaryButtonTitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    final descriptionWidget = DuploText(
      text: descriptionMessage,
      style: duploTextStyles.textSm,
      color: theme.foreground.fgSecondary,
      maxLines: 3,
    );

    return DuploToastContainer(
      onLeadingAction: onLeadingAction,
      titleMessage: titleMessage,
      messageType: messageType,
      statusIconColor:
          messageType == ToastMessageType.success
              ? theme.utility.utilitySuccess600
              : theme.utility.utilityError600,
      contentWidget: descriptionWidget,
      actionButtonTitle: actionButtonTitle,
      onTap: onTap,
      primaryButtonTitle: primaryButtonTitle,
    );
  }
}
