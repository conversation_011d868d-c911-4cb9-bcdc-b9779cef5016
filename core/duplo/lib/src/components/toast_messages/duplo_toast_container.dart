import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_icon_button.dart';
import 'package:duplo/src/components/toast_messages/ripple_effect_widget.dart';
import 'package:duplo/src/components/toast_messages/toast_message_type.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';

class DuploToastContainer extends StatelessWidget {
  final GestureTapCallback onLeadingAction;
  final String titleMessage;
  final Widget contentWidget;
  final ToastMessageType messageType;
  final Color statusIconColor;
  final String? actionButtonTitle;
  final GestureTapCallback? onTap;
  final String? primaryButtonTitle;

  const DuploToastContainer({
    super.key,
    required this.onLeadingAction,
    required this.titleMessage,
    required this.contentWidget,
    required this.messageType,
    required this.statusIconColor,
    this.actionButtonTitle,
    this.onTap,
    this.primaryButtonTitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = DuploTextStyles.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: theme.border.borderPrimary, width: 1.0),
          color: theme.background.bgPrimaryAlt,
          boxShadow: [
            BoxShadow(
              color: theme.shadow.shadowOverlayLg.withOpacity(0.3),
              spreadRadius: -2,
              blurRadius: 4,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(theme),
            const SizedBox(height: 6),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: DuploText(
                text: titleMessage,
                style: duploTextStyles.textSm,
                color: theme.foreground.fgPrimary,
                fontWeight: DuploFontWeight.medium,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: contentWidget,
            ),

            if (actionButtonTitle != null)
              GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: EdgeInsets.only(left: 16),
                  height: 50,
                  alignment: Alignment(-1, 0),
                  child: DuploText(
                    text: actionButtonTitle,
                    style: duploTextStyles.textSm,
                    color: theme.button.buttonTertiaryFg,
                    fontWeight: DuploFontWeight.semiBold,
                  ),
                ),
              )
            else
              const SizedBox(height: 16),
            if (primaryButtonTitle != null)
              Padding(
                padding: EdgeInsetsGeometry.all(16),
                child: DuploButton.primaryBold(
                  leadingIcon: Assets.images.ticket.keyName,
                  title: primaryButtonTitle!,
                  useAssetColor: true,
                  useMaterial: true,

                  onTap: onTap!,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Builds the top row containing the toast icon and close button
  Widget _buildHeader(DuploThemeData theme) {
    return Row(
      children: [_buildToastIcon(), const Spacer(), _buildCloseButton(theme)],
    );
  }

  /// Returns either the success or error icon based on [type]
  Widget _buildToastIcon() {
    return RippleEffectWidget(
      color: statusIconColor,
      image:
          messageType == ToastMessageType.success
              ? Assets.images.successWithoutRipple
              : Assets.images.toastError,
    );
  }

  /// Close button on the top-right corner of the toast
  Widget _buildCloseButton(DuploThemeData theme) {
    return DuploIconButton.link(
      iconColor: theme.foreground.fgQuinary,
      icon: Assets.images.closeIc.keyName,
      onTap: onLeadingAction,
      useMaterial: true,
    );
  }
}
