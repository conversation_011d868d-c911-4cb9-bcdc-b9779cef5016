import 'package:duplo/src/assets/assets.gen.dart';
import 'package:flutter/material.dart';
import 'dart:math';

class RippleEffectWidget extends StatefulWidget {
  final Color color;
  final SvgGenImage image;

  const RippleEffectWidget({Key? key, required this.color, required this.image})
    : super(key: key);

  @override
  _RippleEffectWidgetState createState() => _RippleEffectWidgetState();
}

class _RippleEffectWidgetState extends State<RippleEffectWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  List<double> _waveStartTimes = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(); // Loop animation
    _waveStartTimes.add(0);
    _waveStartTimes.add(0.5);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          child: Center(
            child: widget.image.svg(
              height: 15,
              width: 15,
              colorFilter: ColorFilter.mode(widget.color, BlendMode.srcIn),
            ),
          ),
        ),
        AnimatedBuilder(
          animation: _controller,
          builder: (_, __) {
            return CustomPaint(
              size: const Size(44, 44),
              painter: RipplePainter(
                _controller.value,
                _waveStartTimes,
                widget.color,
              ),
            );
          },
        ),
      ],
    );
  }
}

class RipplePainter extends CustomPainter {
  final double progress;
  final List<double> waveStartTimes;
  final Color color;
  final strokeWidth = 1.5;
  const RipplePainter(this.progress, this.waveStartTimes, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth;

    final double maxRadius = min(size.width, size.height) / 2;
    final double centerRadius = 10; // Static center circle

    Paint centerPaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth;
    canvas.drawCircle(size.center(Offset.zero), centerRadius, centerPaint);

    // Draw continuously expanding waves
    for (double start in waveStartTimes) {
      double waveProgress = (progress - start) % 1;
      if (waveProgress < 0) waveProgress += 1; // Ensure positive value

      double radius = waveProgress * (maxRadius - centerRadius) + centerRadius;
      double opacity = (1 - waveProgress).clamp(0.0, 1.0);

      paint.color = color.withOpacity(opacity);
      canvas.drawCircle(size.center(Offset.zero), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
