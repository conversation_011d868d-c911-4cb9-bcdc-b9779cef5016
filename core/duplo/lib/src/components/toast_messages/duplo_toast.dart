import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';

class DuploToast {
  static final DuploToast _instance = DuploToast._internal();
  factory DuploToast() => _instance;
  DuploToast._internal();

  ToastificationItem? _notification;

  void showToastMessage({
    required BuildContext? context,
    required Widget widget,
    Duration autoCloseDuration = const Duration(seconds: 5),
  }) {
    _notification = toastification.showCustom(
      context: context,
      autoCloseDuration: autoCloseDuration,
      alignment: Alignment.topRight,
      dismissDirection: DismissDirection.up,
      builder: (BuildContext builderContext, ToastificationItem holder) {
        return widget;
      },
    );
  }

  void hidesToastMessage() {
    if (_notification != null) {
      toastification.dismiss(_notification!);
    }
  }
}
