import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';

import '../constants/duplo_radius.dart';

class DuploFileUpload extends StatelessWidget {
  const DuploFileUpload({super.key, required this.onTap, required this.status});
  final void Function() onTap;
  final DuploFileUploadStatus status;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return DuploTap(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(DuploSpacing.spacing_xl_16),
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          borderRadius: BorderRadius.circular(DuploRadius.radius_xl_12),
          border: Border.all(color: theme.border.borderSecondary),
        ),
        child: Column(
          children: [
            Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                color: theme.background.bgPrimary,
                borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
                border: Border.all(color: theme.icon.iconFeaturedModernBorder),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x0c0A0C12),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Center(child: _getIcon(theme)),
            ),
            SizedBox(height: DuploSpacing.spacing_lg_12),
            DuploText(
              text: _getTitle(localization),
              style: textStyles.textSm,
              fontWeight: DuploFontWeight.semiBold,
              // TODO (sambhav): color token not found for (Component-colors-Components-Buttons-Tertiary-color-button-tertiary-color-fg, #006B67);
              // added which looks similar to that
              color:
                  status == DuploFileUploadStatus.failure
                      ? theme.text.textErrorPrimary
                      : theme.text.textBrandSecondary,
            ),
            SizedBox(height: DuploSpacing.spacing_xs_4),
            DuploText(
              text: localization.payments_upload_doc_file_formats_note,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.regular,
              color: theme.text.textTertiary,
            ),
          ],
        ),
      ),
    );
  }

  String _getTitle(EquitiLocalization localization) {
    switch (status) {
      case DuploFileUploadStatus.initial:
        return localization.payments_upload_doc_tap_to_upload;
      case DuploFileUploadStatus.uploading:
        return localization.payments_upload_doc_uploading;
      case DuploFileUploadStatus.success:
        return localization.payments_upload_doc_success;
      case DuploFileUploadStatus.failure:
        return localization.payments_upload_doc_failure;
    }
  }

  Widget _getIcon(DuploThemeData theme) {
    switch (status) {
      case DuploFileUploadStatus.initial:
      case DuploFileUploadStatus.uploading:
      case DuploFileUploadStatus.success:
        return Assets.images.uploadCloud.svg(
          height: 20,
          width: 20,
          colorFilter: ColorFilter.mode(
            theme.foreground.fgSecondary,
            BlendMode.srcIn,
          ),
        );
      case DuploFileUploadStatus.failure:
        return Assets.images.errorIc.svg(height: 20, width: 20);
    }
  }
}

//upload status enum
enum DuploFileUploadStatus { initial, uploading, success, failure }
