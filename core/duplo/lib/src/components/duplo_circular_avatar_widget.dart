import 'package:duplo/src/cached_image/duplo_cached_network_image.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploCircularAvatarWidget extends StatelessWidget {
  const DuploCircularAvatarWidget({
    super.key,
    this.imageUrl,
    required this.imageWidth,
    required this.imageHeight,
    this.image,
    this.firstName,
    this.lastName,
  });
  final String? imageUrl;
  final Widget? image;
  final String? firstName;
  final String? lastName;
  final double imageWidth;
  final double imageHeight;

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child:
          image != null
              ? image!
              : imageUrl != null
              ? DuploCachedNetworkImage(
                imageUrl: imageUrl!,
                imageWidth: imageWidth,
                imageHeight: imageHeight,
              )
              : Container(
                width: imageWidth,
                height: imageHeight,
                color: context.duploTheme.container.containerBg,
                child: Center(
                  child: DuploText(
                    text:
                        (firstName?.isNotEmpty == true
                            ? firstName!.substring(0, 1)
                            : '') +
                        (lastName?.isNotEmpty == true
                            ? lastName!.substring(0, 1)
                            : ''),
                    style: context.duploTextStyles.displayXs,
                    fontWeight: DuploFontWeight.semiBold,
                    color: context.duploTheme.foreground.fgQuaternary,
                  ),
                ),
              ),
    );
  }
}
