import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:flutter/material.dart';

class DuploOverlay {
  static void removeEntry(OverlayEntry entry) {
    entry.remove();
  }

  static void show(
    BuildContext context,
    String message, {

    /// optional position parameter otherwise positioned centrally
    Offset? position,

    /// offset from the position
    Offset offset = const Offset(12, -42),
    Duration duration = const Duration(seconds: 1),
  }) {
    final overlay = Overlay.of(context);
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);

    final entry = OverlayEntry(
      builder: (_) {
        final overlayWidget = Material(
          color: theme.background.bgPrimarySolid,
          type: MaterialType.circle,
          child: Container(
            constraints: const BoxConstraints(minHeight: 34, minWidth: 66),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.background.bgPrimarySolid,
              borderRadius: BorderRadius.circular(6),
            ),
            child: DuploText(
              text: message,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textWhite,
            ),
          ),
        );
        return position == null
            ? Center(child: overlayWidget)
            : Positioned(
              top: position.dy + offset.dy,
              left: position.dx + offset.dx,
              child: overlayWidget,
            );
      },
    );
    overlay.insert(entry);
    Future.delayed(duration, () => removeEntry(entry));
  }
}
