import 'dart:developer' as dev;
import 'dart:io';

import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:theme_manager/theme_manager.dart';

/// Utility class for date of birth input validation and styling.
///
/// Provides methods for validating date inputs, determining appropriate
/// styling based on input state, and displaying a date picker.
class DuploDateOfBirthUtils {
  /// Creates a date of birth utilities instance.
  const DuploDateOfBirthUtils();

  /// Validates if the entered date string is a valid date.
  ///
  /// Returns an error message string if the date is invalid, or null if valid.
  ///
  /// The expected format is "DD - MM - YYYY".
  ///
  /// @param text The date string to validate
  /// @return Error message if invalid, null if valid
  String? checkIfDateIsValid(String text) {
    // Only validate if the full formatted date is entered
    if (text.length < 14) return null;

    final raw = text.replaceAll(RegExp(r'\D'), '');

    if (raw.length != 8) {
      return 'Please enter the complete date in DD - MM - YYYY format.';
    }

    final day = int.tryParse(raw.substring(0, 2));
    final month = int.tryParse(raw.substring(2, 4));
    final year = int.tryParse(raw.substring(4, 8));

    if (day == null || month == null || year == null) {
      return 'Invalid numeric values in date.';
    }

    try {
      final date = DateTime(year, month, day);

      if (date.day != day || date.month != month || date.year != year) {
        return 'Invalid date. Please check day/month values.';
      }

      if (year < 1900 || year > 2100) {
        return 'Year should be between 1900 and 2100.';
      }

      return null;
    } catch (_) {
      return 'Invalid date format.';
    }
  }

  /// Returns appropriate box shadows based on the input field state.
  ///
  /// Different shadows are applied depending on whether the field has an error,
  /// is focused, or has text entered.
  ///
  /// @param textValue The current text in the input field
  /// @param isFocused Whether the input field is currently focused
  /// @param isError Whether the input field has an error
  /// @return List of BoxShadow objects to apply
  List<BoxShadow> getShadow({
    required String textValue,
    required bool isFocused,
    required bool isError,
  }) {
    //todo: replace with token shadow

    // Common shadow properties
    final _baseShadow = BoxShadow(
      color: Color(0x0C101828),
      blurRadius: 2,
      offset: Offset(0, 1),
      spreadRadius: 0,
    );
    if (isError) {
      return [
        BoxShadow(
          color: Color(0x3DF04438),
          blurRadius: 2,
          offset: Offset(0, 1),
          spreadRadius: 1,
        ),
        _baseShadow,
      ];
    }

    if (isFocused) {
      return [
        BoxShadow(
          color: Color(0x3D77EDEB),
          blurRadius: 2,
          offset: Offset(0, 0),
          spreadRadius: 1,
        ),
        _baseShadow,
      ];
    }

    if (textValue.isNotEmpty) {
      return [
        BoxShadow(
          color: Color(0x2398A2B3),
          blurRadius: 2,
          offset: Offset(0, 0),
          spreadRadius: 1,
        ),
        _baseShadow,
      ];
    }

    return <BoxShadow>[];
  }

  /// Returns the appropriate border color based on the input field state.
  ///
  /// @param isFocused Whether the input field is currently focused
  /// @param isError Whether the input field has an error
  /// @param theme The current theme data
  /// @return The color to use for the input field border
  Color getBorderColor({
    required bool isFocused,
    required bool isError,
    required DuploThemeData theme,
  }) {
    return isError
        ? theme.border.borderError
        : isFocused
        ? theme.border.borderBrand
        : theme.border.borderSecondary;
  }

  /// Opens a date picker and returns the selected date as a formatted string.
  ///
  /// Displays a Cupertino-style date picker in a modal popup.
  /// Returns the selected date in "DD - MM - YYYY" format, or null if no date was selected.
  ///
  /// @param context The build context
  /// @return Future that resolves to the formatted date string or null
  Future<String?> openDatePicker({required BuildContext context}) async {
    // show cupertino if ios
    DateTime? selectedDate;
    String? formattedDate;
    final isAndroid = Platform.isAndroid;
    final isRunningTest = Platform.environment.containsKey('FLUTTER_TEST');
    final currentDateTime = isRunningTest ? DateTime(2025) : DateTime.now();

    isAndroid
        ? selectedDate = await showDatePicker(
          context: context,
          firstDate: currentDateTime.subtract(Duration(days: 365 * 100)),
          lastDate: currentDateTime,
          initialDate: currentDateTime,
          builder:
              (ctx, child) => Theme(
                data: Theme.of(context).copyWith(
                  colorScheme: ColorScheme.light(
                    primary: DuploTheme.of(context).background.bgSecondary,
                    onPrimary: DuploTheme.of(context).text.textPrimary,
                    surface: DuploTheme.of(context).background.bgPrimary,
                    onSurface: DuploTheme.of(context).text.textPrimary,
                  ),
                  textButtonTheme: TextButtonThemeData(
                    style: TextButton.styleFrom(
                      foregroundColor:
                          DuploTheme.of(
                            context,
                          ).text.textBrandPrimary, // button text color
                    ),
                  ),
                ),
                child: child!,
              ),
        )
        : await showCupertinoModalPopup<DateTime>(
          context: context,
          barrierColor: Colors.black.withValues(alpha: .5),
          builder: (_) {
            final theme = DuploTheme.of(context);
            return SafeArea(
              top: false,
              child: Container(
                height: 300,
                padding: EdgeInsets.symmetric(horizontal: 10),
                margin: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.background.bgPrimary,
                  borderRadius: BorderRadius.circular(DuploRadius.radius_xl_12),
                ),
                child: CupertinoTheme(
                  data: CupertinoThemeData(
                    brightness:
                        diContainer<ThemeManager>().isDarkMode
                            ? Brightness.dark
                            : Brightness.light,
                  ),
                  child: CupertinoDatePicker(
                    mode: CupertinoDatePickerMode.date,
                    dateOrder: DatePickerDateOrder.dmy,
                    initialDateTime: currentDateTime,
                    minimumYear: 1900,
                    maximumYear: 2100,
                    onDateTimeChanged: (value) {
                      selectedDate = value;
                      dev.log('Selected date: ${DateFormat().format(value)}');
                    },
                  ),
                ),
              ),
            );
          },
        );

    if (selectedDate != null) {
      formattedDate = DateFormat('dd - MM - yyyy').format(selectedDate!);
    }
    return formattedDate;
  }
}
