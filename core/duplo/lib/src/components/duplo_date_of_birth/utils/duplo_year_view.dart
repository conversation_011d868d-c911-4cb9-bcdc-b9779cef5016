import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

/// A widget that displays the calculated age based on a date of birth input.
///
/// This component shows the age in years in a small container next to the date input field.
/// It handles validation and appropriate styling based on error states.
///
/// Example usage:
/// ```dart
/// DuploYearView(
///   dateOfBirthValue: "12 - 05 - 1990",
///   isError: false,
///   isEnabled: true,
/// )
/// ```
class DuploYearView extends StatelessWidget {
  /// Creates a year view widget.
  ///
  /// The [dateOfBirthValue] is the formatted date string in "DD - MM - YYYY" format.
  /// The [isError] indicates whether the date input has validation errors.
  /// The [isEnabled] determines if the component should be displayed.
  const DuploYearView({
    super.key,
    required this.dateOfBirthValue,
    required this.isError,
    required this.isEnabled,
  });

  /// The date of birth value in "DD - MM - YYYY" format.
  final String dateOfBirthValue;

  /// Whether the date input has validation errors.
  final bool isError;

  /// Whether the component is enabled and should be displayed.
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textTheme = context.duploTextStyles;
    final yearInString = convertDateOfBirthValueToYear(dateOfBirthValue);
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return yearInString != null && isEnabled
        ? Container(
          height: 28,
          width: 34,
          margin: EdgeInsets.only(
            right: isRTL ? 0 : DuploSpacing.spacing_md_8,
            left: isRTL ? DuploSpacing.spacing_md_8 : 0,
          ),
          decoration: BoxDecoration(
            color:
                isError
                    ? theme.utility.utilityError50
                    : theme.utility.utilityGray50,
            borderRadius: BorderRadius.circular(DuploRadius.radius_xs_4),
            border: Border.all(
              color:
                  isError
                      ? theme.utility.utilityError200
                      : theme.utility.utilityGray200,
            ),
          ),
          child: Center(
            child: DuploText(
              text: convertDateOfBirthValueToYear(dateOfBirthValue),
              style: textTheme.textSm,
              color:
                  isError
                      ? theme.utility.utilityError700
                      : theme.utility.utilityGray700,
              fontWeight: DuploFontWeight.medium,
            ),
          ),
        )
        : SizedBox();
  }

  /// Converts a date of birth string to an age in years.
  ///
  /// Parses the input date string, calculates the current age,
  /// and returns it as a string. Returns null if the date is invalid
  /// or incomplete.
  ///
  /// @param value The date string in "DD - MM - YYYY" format
  /// @return The age in years as a string, or null if invalid
  String? convertDateOfBirthValueToYear(String value) {
    // Remove non-digits to get raw format: DDMMYYYY
    final raw = value.replaceAll(RegExp(r'\D'), '');

    if (raw.length != 8) return null; // incomplete

    final day = int.tryParse(raw.substring(0, 2));
    final month = int.tryParse(raw.substring(2, 4));
    final year = int.tryParse(raw.substring(4, 8));

    if (day == null || month == null || year == null) return null;

    try {
      final birthDate = DateTime(year, month, day);
      final today = DateTime.now();

      int age = today.year - birthDate.year;

      // If birthday hasn't occurred yet this year, subtract 1
      if (today.month < birthDate.month ||
          (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }
      // If the age is negative, return null
      if (age < 0 || age > 110) return null;

      return age.toString();
    } catch (_) {
      return null;
    }
  }
}
