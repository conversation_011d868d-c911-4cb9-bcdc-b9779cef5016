import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class StepperControlWidget extends StatelessWidget {
  final Widget? segmentControWidget;
  final Widget inputWidget;
  final Widget? footerWidget;
  final Widget? leadingWidget;
  final String? title;
  final bool bordered;
  final Widget? trailingWidget;
  final double inputSpacing;

  StepperControlWidget({
    super.key,
    this.segmentControWidget,
    required this.inputWidget,
    this.footerWidget,
    this.leadingWidget,
    this.title,
    this.bordered = true,
    this.trailingWidget,
    this.inputSpacing = 8.0,
  }) : assert(
         title != null || leadingWidget != null,
         'Either title or leadingWidget must be provided',
       );

  Widget _footerWidget() {
    if (footerWidget != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [const SizedBox(height: 8), footerWidget!],
      );
    }
    return const SizedBox.shrink();
  }

  Widget _segmentControlWidget() {
    if (segmentControWidget != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          Container(height: 50, child: segmentControWidget),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return Container(
      decoration:
          bordered
              ? BoxDecoration(
                color: theme.background.bgSecondary,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.border.borderSecondary),
              )
              : BoxDecoration(color: theme.background.bgPrimary),
      padding: bordered ? const EdgeInsets.all(16) : EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          leadingWidget ??
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: DuploText(
                      text: title,
                      style: context.duploTextStyles.textMd,
                      textAlign: TextAlign.start,
                      maxLines: 1,
                      fontWeight: DuploFontWeight.semiBold,
                      overflow: TextOverflow.ellipsis,
                      color: theme.text.textPrimary,
                    ),
                  ),
                  Spacer(),
                  trailingWidget ?? SizedBox.shrink(),
                ],
              ),
          _segmentControlWidget(),
          SizedBox(height: inputSpacing),
          inputWidget,
          _footerWidget(),
        ],
      ),
    );
  }
}
