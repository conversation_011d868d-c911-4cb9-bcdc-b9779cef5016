import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class StepperNumberInputWithKeyboardWidget extends StatelessWidget {
  final String hintText;
  final void Function(String) onValueChange;
  final double changeFactor;
  final int prescisionFactor;
  final TextEditingController inputEditingController;
  final TextInputControl? inputControl;
  final List<TextInputFormatter>? formatters;
  final void Function()? onTap;
  final int digits;

  const StepperNumberInputWithKeyboardWidget({
    required this.inputControl,
    required this.prescisionFactor,
    required this.hintText,
    required this.onValueChange,
    required this.changeFactor,
    required this.inputEditingController,
    this.formatters,
    this.onTap,
    required this.digits,
  });

  void _increment(TextEditingController controller, String locale) {
    String text = controller.text;
    try {
      final nowValue = double.parse(text);
      final newDoubleValue = (nowValue + changeFactor);
      controller.text = EquitiFormatter.formatDynamicDigits(
        value: newDoubleValue,
        digits: digits,
        locale: locale,
      );
      onValueChange(controller.text);
    } catch (_) {}
  }

  void _decrement(TextEditingController controller, String locale) {
    String text = controller.text;
    try {
      final nowValue = double.parse(text);
      final newDoubleValue = (nowValue - changeFactor);
      controller.text = EquitiFormatter.formatDynamicDigits(
        value: newDoubleValue,
        digits: digits,
        locale: locale,
      );
      onValueChange(controller.text);
    } catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        border: Border.all(color: context.duploTheme.border.borderSecondary),
        borderRadius: BorderRadius.circular(5),
        color: context.duploTheme.background.bgPrimary,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: context.duploTheme.background.bgTertiary,
              border: Border.all(
                color: context.duploTheme.border.borderSecondary,
              ),
            ),
            child: IconButton(
              key: const Key('decrement_button'),
              icon: Assets.images.minusSquare.svg(
                colorFilter: ColorFilter.mode(
                  context.duploTheme.text.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
              onPressed: () {
                _decrement(
                  inputEditingController,
                  Localizations.localeOf(context).toString(),
                );
              },
            ),
          ),
          Divider(
            color: context.duploTheme.border.borderSecondary,
            height: 48,
            thickness: 1,
          ),
          Expanded(
            child: TextField(
              controller: inputEditingController,
              inputFormatters: formatters,
              keyboardAppearance:
                  diContainer<ThemeManager>().isDarkMode
                      ? Brightness.dark
                      : Brightness.light,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                contentPadding: EdgeInsets.symmetric(horizontal: 8),
              ),
              enableInteractiveSelection: false,
              onChanged: (value) {
                onValueChange(value);
              },
              onTap: () {
                inputControl?.show();
              },
              style: TextStyle(color: context.duploTheme.text.textSecondary),
            ),
          ),
          Divider(
            color: context.duploTheme.border.borderSecondary,
            height: 48,
            thickness: 1,
          ),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: context.duploTheme.background.bgTertiary,
            ),
            child: IconButton(
              key: const Key('increment_button'),
              icon: Assets.images.plusSquare.svg(
                colorFilter: ColorFilter.mode(
                  context.duploTheme.text.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
              onPressed: () {
                _increment(
                  inputEditingController,
                  Localizations.localeOf(context).toString(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
