import 'package:custom_action_keyboard/custom_action_keyboard.dart';
import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/extensions/is_number_keyboard.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:duplo/src/utilities/force_negative_formatter.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class StepperNumberInputWidget extends StatefulWidget {
  final TextEditingController? controller;
  final String hintText;
  final void Function(String) onValueChange;
  final double changeFactor;
  final int prescisionFactor;
  final String? errorText;
  final bool enabled;
  final double? minimumValue;
  final TextInputFormatter? textInputFormatter;
  final bool forceNegative;
  final FocusNode? focusNode;

  const StepperNumberInputWidget({
    this.controller,
    this.enabled = true,
    required this.prescisionFactor,
    required this.hintText,
    required this.onValueChange,
    required this.changeFactor,
    this.errorText,
    this.minimumValue,
    this.textInputFormatter,
    this.forceNegative = false,
    this.focusNode,
  });

  @override
  State<StepperNumberInputWidget> createState() =>
      _StepperNumberInputWidgetState();
}

class _StepperNumberInputWidgetState extends State<StepperNumberInputWidget> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;
  bool _isInternalController = false;
  bool _isInternalFocusNode = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _isInternalController = widget.controller == null;

    _focusNode = widget.focusNode ?? FocusNode();
    _isInternalFocusNode = widget.focusNode == null;

    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    diContainer<CustomActionKeyboard>().setKeyboardVisibility(
      _focusNode.hasFocus,
    );
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);

    if (_isInternalController) {
      _controller.dispose();
    }
    if (_isInternalFocusNode) {
      _focusNode.dispose();
    }
    diContainer<CustomActionKeyboard>().setKeyboardVisibility(false);

    super.dispose();
  }

  void _increment(String locale) {
    String text = _controller.text;
    final nowValue = double.tryParse(text);
    if (nowValue == null) {
      return;
    }
    double newDoubleValue = (nowValue + widget.changeFactor);

    // Force negative if required, and ensure it's not zero
    if (widget.forceNegative) {
      if (newDoubleValue >= 0) {
        newDoubleValue = -widget.changeFactor;
      }
    }

    _controller.text = EquitiFormatter.formatDynamicDigits(
      value: newDoubleValue,
      digits: widget.prescisionFactor,
      locale: locale,
    );
    widget.onValueChange(_controller.text);
  }

  void _decrement(String locale) {
    String text = _controller.text;
    final nowValue = double.tryParse(text);
    if (nowValue == null) {
      return;
    }
    double newDoubleValue = (nowValue - widget.changeFactor);

    if (!widget.forceNegative &&
        widget.minimumValue != null &&
        newDoubleValue < widget.minimumValue!) {
      newDoubleValue = widget.minimumValue!;
    }

    if (widget.forceNegative && newDoubleValue >= 0) {
      return;
    }

    _controller.text = EquitiFormatter.formatDynamicDigits(
      value: newDoubleValue,
      digits: widget.prescisionFactor,
      locale: locale,
    );

    if (!widget.forceNegative &&
        widget.minimumValue != null &&
        nowValue == widget.minimumValue!) {
      return;
    }
    widget.onValueChange(_controller.text);
  }

  List<TextInputFormatter> _getInputFormatters() {
    if (widget.forceNegative) {
      return [
        ForceNegativeFormatter(),
        if (widget.textInputFormatter != null) widget.textInputFormatter!,
      ];
    }

    if (widget.minimumValue != null && widget.minimumValue! >= 0) {
      return [
        FilteringTextInputFormatter.deny(RegExp(r'-')),
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
        if (widget.textInputFormatter != null) widget.textInputFormatter!,
      ];
    }

    return widget.textInputFormatter != null
        ? [
          widget.textInputFormatter!,
          FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
        ]
        : [FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*'))];
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  widget.errorText == null || widget.errorText!.isEmpty
                      ? theme.border.borderSecondary
                      : theme.text.textErrorPrimary,
            ),
            borderRadius: BorderRadius.circular(5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  color:
                      widget.enabled
                          ? theme.background.bgTertiary
                          : theme.background.bgSecondarySubtle,
                  child: IconButton(
                    key: const Key('minus'),
                    icon: Assets.images.minusSquare.svg(
                      colorFilter: ColorFilter.mode(
                        widget.enabled
                            ? theme.button.buttonSecondaryFg
                            : theme.foreground.fgDisabled,
                        BlendMode.srcIn,
                      ),
                    ),
                    onPressed:
                        widget.enabled
                            ? () => _decrement(
                              Localizations.localeOf(context).toString(),
                            )
                            : null,
                  ),
                ),
                Container(
                  height: 48,
                  width: 1,
                  color: theme.border.borderSecondary,
                ),
                Expanded(
                  child: TextField(
                    focusNode: _focusNode,
                    textInputAction: TextInputAction.done,
                    inputFormatters: _getInputFormatters(),
                    style: TextStyle(color: theme.text.textPrimary),
                    controller: _controller,
                    enabled: widget.enabled,
                    keyboardAppearance:
                        diContainer<ThemeManager>().isDarkMode
                            ? Brightness.dark
                            : Brightness.light,
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: widget.hintText,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                    enableInteractiveSelection: false, // Disable paste
                    onChanged: widget.onValueChange,
                  ),
                ),
                Container(
                  height: 48,
                  width: 1,
                  color: theme.border.borderSecondary,
                ),
                Container(
                  width: 48,
                  height: 48,
                  color:
                      widget.enabled
                          ? theme.background.bgTertiary
                          : theme.background.bgSecondarySubtle,
                  child: IconButton(
                    icon: Assets.images.plusSquare.svg(
                      colorFilter: ColorFilter.mode(
                        widget.enabled
                            ? theme.button.buttonSecondaryFg
                            : theme.foreground.fgDisabled,
                        BlendMode.srcIn,
                      ),
                    ),
                    onPressed:
                        widget.enabled
                            ? () => _increment(
                              Localizations.localeOf(context).toString(),
                            )
                            : null,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (widget.errorText != null && widget.errorText!.isNotEmpty) ...[
          const SizedBox(height: 16.0),
          Align(
            alignment: AlignmentDirectional.centerStart,
            child: DuploText(
              text: widget.errorText,
              style: context.duploTextStyles.textSm,
              color: theme.text.textErrorPrimary,
            ),
          ),
        ],
      ],
    );
  }
}
