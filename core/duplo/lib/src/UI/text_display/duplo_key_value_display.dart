import 'package:duplo/src/UI/models/key_value_pair.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploKeyValueDisplay extends StatelessWidget {
  final String? title;
  final DuploTextStyle? titleStyle;
  final DuploTextStyle? keyTextStyle;
  final DuploTextStyle? valueTextStyle;
  final Color? valueColor;
  final Color? keyColor;
  final DuploFontWeight? valueFontWeight;
  final DuploFontWeight? keyFontWeight;
  final List<KeyValuePair> keyValuePairs;
  final double contentSpacing;
  final bool addBorder;
  final bool hideLastDivider;

  const DuploKeyValueDisplay({
    Key? key,
    this.title,
    required this.keyValuePairs,
    this.contentSpacing = 10.0,
    this.addBorder = true,
    this.titleStyle,
    this.keyTextStyle,
    this.valueTextStyle,
    this.valueColor,
    this.keyColor,
    this.valueFontWeight,
    this.keyFontWeight,
    this.hideLastDivider = false,
  }) : super(key: key);

  Widget _titleWidget(BuildContext context) {
    if (title != null && title!.isNotEmpty) {
      final theme = context.duploTheme;
      final duploTextStyles = context.duploTextStyles;
      return Column(
        children: [
          DuploText(
            text: title!,
            style: titleStyle ?? duploTextStyles.textMd,
            fontWeight: DuploFontWeight.semiBold,
            color: theme.text.textPrimary,
          ),
          const SizedBox(height: 8),
        ],
      );
    }
    return Container(height: 0);
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Container(
      color: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _titleWidget(context),
          ...keyValuePairs
              .asMap()
              .entries
              .map(
                (entry) => Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(vertical: contentSpacing),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: DuploText(
                              text: entry.value.label,
                              textAlign: TextAlign.start,
                              overflow: TextOverflow.ellipsis,
                              color:
                                  entry.value.color ??
                                  keyColor ??
                                  theme.text.textSecondary,
                              style: keyTextStyle ?? duploTextStyles.textSm,
                              fontWeight:
                                  keyFontWeight ?? DuploFontWeight.medium,
                              maxLines: 1,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child:
                                entry.value.valueTextDirection != null &&
                                        Localizations.localeOf(
                                              context,
                                            ).toString() !=
                                            'ar'
                                    ? Directionality(
                                      textDirection:
                                          entry.value.valueTextDirection!,
                                      child: DuploText(
                                        text: entry.value.value,
                                        textAlign: TextAlign.end,
                                        overflow: TextOverflow.ellipsis,
                                        color:
                                            entry.value.color ??
                                            valueColor ??
                                            theme.text.textPrimary,
                                        style:
                                            valueTextStyle ??
                                            duploTextStyles.textSm,
                                        fontWeight: DuploFontWeight.medium,
                                        maxLines: 2,
                                      ),
                                    )
                                    : DuploText(
                                      text: entry.value.value,
                                      textAlign: TextAlign.end,
                                      overflow: TextOverflow.ellipsis,
                                      color:
                                          entry.value.color ??
                                          valueColor ??
                                          theme.text.textPrimary,
                                      style:
                                          valueTextStyle ??
                                          duploTextStyles.textSm,
                                      fontWeight: DuploFontWeight.medium,
                                      maxLines: 2,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    if (addBorder &&
                        !(hideLastDivider &&
                            entry.key == keyValuePairs.length - 1))
                      Divider(height: 1, color: theme.border.borderSecondary),
                  ],
                ),
              )
              .toList(),
        ],
      ),
    );
  }
}
