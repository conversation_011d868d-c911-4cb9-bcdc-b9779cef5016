import 'package:duplo/src/UI/text_display/duplo_label_info_widget.dart';
import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/locale_aware_assets_extension.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

class DuploLabelInfoChevronWidget extends StatelessWidget {
  const DuploLabelInfoChevronWidget({
    required this.title,
    this.valueText,
    this.description,
    this.infoText,
    this.iconColor,
  });

  final String title;
  final String? valueText;
  final String? description;
  final String? infoText;
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = DuploTextStyles.of(context);
    double padding = 12;
    if (description != null) {
      padding = 8;
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: padding),
      child: Row(
        children: [
          DuploLabelInfoWidget(
            title: title,
            description: description,
            infoText: infoText,
          ),
          Spacer(),
          if (valueText != null)
            DuploText(
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              text: valueText!,
              color: theme.text.textQuaternary,
              style: duploTextStyles.textXs,
              fontWeight: DuploFontWeight.regular,
              textAlign: TextAlign.start,
            ),
          const SizedBox(width: 4),
          Assets.images
              .chevronRightDirectional(context)
              .svg(
                colorFilter:
                    iconColor != null
                        ? ColorFilter.mode(iconColor!, BlendMode.srcIn)
                        : null,
              ),
        ],
      ),
    );
  }
}
