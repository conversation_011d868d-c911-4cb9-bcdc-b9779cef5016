import 'package:duplo/src/UI/models/key_value_pair.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploStatsWidget extends StatelessWidget {
  final List<KeyValuePair> leftColumnData;
  final List<KeyValuePair> rightColumnData;
  final Color? labelColor;
  final double? verticalPadding;
  final Color? backgroundColor;
  const DuploStatsWidget({
    Key? key,
    required this.leftColumnData,
    required this.rightColumnData,
    this.labelColor,
    this.verticalPadding,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return IntrinsicHeight(
      child: Container(
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          textDirection: Directionality.of(context),
          children: [
            _buildColumn(leftColumnData, context),
            SizedBox(width: 8),
            Container(width: 1, color: theme.border.borderPrimary),
            SizedBox(width: 8),
            _buildColumn(rightColumnData, context),
          ],
        ),
      ),
    );
  }

  Widget _buildColumn(List<KeyValuePair> columnData, BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return Expanded(
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children:
              columnData.map((item) {
                return Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: verticalPadding ?? 8.0,
                    horizontal: 0.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    textDirection: Directionality.of(context),
                    children: [
                      Expanded(
                        child: DuploText(
                          text: item.label,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: duploTextStyles.textXs,
                          textAlign: TextAlign.start,
                          fontWeight: DuploFontWeight.regular,
                          color: labelColor ?? theme.text.textPrimary,
                        ),
                      ),
                      Expanded(
                        child: DuploText(
                          text: item.value,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: duploTextStyles.textXs,
                          textAlign: TextAlign.end,
                          fontWeight: DuploFontWeight.regular,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }
}
