import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class SymbolPriceAndPercentageWidget extends StatelessWidget {
  const SymbolPriceAndPercentageWidget({
    super.key,
    required this.price,
    required this.percentage,
    required this.digits,
  });
  final double price;
  final double percentage;
  final int digits;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return IntrinsicWidth(
      child: IntrinsicHeight(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            DuploText(
              text: EquitiFormatter.formatDynamicDigits(
                value: price,
                digits: digits,
                locale: Localizations.localeOf(context).toString(),
              ),
              style: duploTextStyles.textMd,
              color: theme.text.textTertiary,
              fontWeight: DuploFontWeight.bold,
            ),
            DuploText(
              text: percentage.isNegative ? "$percentage%" : "+$percentage%",
              style: duploTextStyles.textSm,
              color:
                  percentage.isNegative
                      ? theme.text.textErrorPrimary
                      : theme.text.textSuccessPrimary,
              fontWeight: DuploFontWeight.medium,
            ),
          ],
        ),
      ),
    );
  }
}
