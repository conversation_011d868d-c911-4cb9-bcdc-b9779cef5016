import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class GroupedButtonsItem extends StatelessWidget {
  final String title;
  final Widget buttonIcon;
  final bool isSelected;
  final VoidCallback onTap;
  final double horizontalPadding;
  final double verticalPadding;
  final bool isHighlighted;
  final String? semanticsIdentifier;

  const GroupedButtonsItem({
    super.key,
    required this.title,
    required this.buttonIcon,
    required this.onTap,
    this.isSelected = false,
    this.horizontalPadding = 20,
    this.verticalPadding = 10,
    this.isHighlighted = false,
    this.semanticsIdentifier,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      decoration: BoxDecoration(
        color:
            isHighlighted
                ? context.duploTheme.background.bgActive
                : context.duploTheme.background.bgPrimary,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Semantics(
        identifier: semanticsIdentifier ?? "grouped_buttons_item",
        child: DuploTap(
          useMaterial: true,
          onTap: onTap,
          child: Container(
            padding: EdgeInsetsDirectional.symmetric(
              horizontal: horizontalPadding,
              vertical: verticalPadding,
            ),
            decoration:
                isSelected
                    ? BoxDecoration(
                      color: context.duploTheme.background.bgSecondary,
                      borderRadius: BorderRadius.circular(6),
                    )
                    : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                buttonIcon,
                const SizedBox(width: 4),
                DuploText(
                  text: title,
                  style: context.duploTextStyles.textSm,
                  color: context.duploTheme.text.textSecondary,
                  fontWeight: DuploFontWeight.semiBold,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
