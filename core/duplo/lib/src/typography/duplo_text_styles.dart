import 'package:duplo/src/typography/text_styles.dart';
import 'package:flutter/material.dart';

class DuploTextStyles extends InheritedWidget {
  final Locale locale;

  DuploTextStyles({required this.locale, required Widget child})
    : super(child: child);

  static TextStyles of(BuildContext context) {
    final DuploTextStyles? inheritedTextStyles =
        context.dependOnInheritedWidgetOfExactType<DuploTextStyles>();
    assert(inheritedTextStyles != null, 'No DuploTextStyles found in context');
    switch (inheritedTextStyles!.locale.languageCode) {
      case 'ar':
        return TextStyles.ar();
      default:
        return TextStyles.en();
    }
  }

  @override
  bool updateShouldNotify(DuploTextStyles oldWidget) {
    return locale != oldWidget.locale;
  }
}
