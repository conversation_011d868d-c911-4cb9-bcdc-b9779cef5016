import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';

import 'package:flutter/material.dart';

///  - [isFlex]: set as TRUE only when DuploTabBar is placed within a Flex widget (like Column or Row)

class DuploTabBar extends StatefulWidget {
  final List<DuploTabBarTitle> tabTitles;
  final bool isScrollable;
  final List<Widget> tabViews;
  final bool enableSplashEffect;
  final TabController? tabController;
  final WidgetBuilder? bottomViewBuilder;
  final bool isFlex;
  final int initialIndex;
  final bool hideTabbar;
  final DuploTextStyle? tabTextStyle;

  const DuploTabBar({
    super.key,
    required this.tabTitles,
    required this.isScrollable,
    required this.tabViews,
    this.enableSplashEffect = false,
    this.bottomViewBuilder,
    this.tabController,
    required this.isFlex,
    this.hideTabbar = false,
    this.initialIndex = 0,
    this.tabTextStyle,
  });

  @override
  _DuploTabBarState createState() => _DuploTabBarState();
}

class _DuploTabBarState extends State<DuploTabBar> {
  bool _hideTabbar = false;

  @override
  void initState() {
    super.initState();
    _hideTabbar = widget.hideTabbar;
  }

  @override
  void didUpdateWidget(covariant DuploTabBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.hideTabbar != oldWidget.hideTabbar) {
      _hideTabbar = widget.hideTabbar;
    }
    if (widget.initialIndex != oldWidget.initialIndex) {
      widget.tabController?.index = widget.initialIndex;
    }
  }

  @override
  Widget build(BuildContext context) {
    final content = Column(
      children: [
        AnimatedOpacity(
          duration: const Duration(milliseconds: 250),
          opacity: _hideTabbar ? 0 : 1,
          child: Offstage(
            offstage: _hideTabbar,
            child: _TabBar(
              tabController: widget.tabController,
              isScrollable: widget.isScrollable,
              tabTitles: widget.tabTitles,
              enableSplashEffect: widget.enableSplashEffect,
              tabTextStyle: widget.tabTextStyle,
            ),
          ),
        ),
        widget.bottomViewBuilder?.call(context) ?? const SizedBox.shrink(),
        Expanded(
          child: TabBarView(
            controller: widget.tabController,
            children: widget.tabViews,
          ),
        ),
      ],
    );

    return DefaultTabController(
      initialIndex: widget.initialIndex,
      length: widget.tabTitles.length,
      child: widget.isFlex ? Flexible(child: content) : content,
    );
  }
}

class _TabBar extends StatelessWidget {
  const _TabBar({
    required this.tabController,
    required this.isScrollable,
    required this.tabTitles,
    this.enableSplashEffect = false,
    this.tabTextStyle,
  });

  final TabController? tabController;
  final bool isScrollable;
  final List<DuploTabBarTitle> tabTitles;
  final bool enableSplashEffect;
  final DuploTextStyle? tabTextStyle;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return TabBar(
      physics: RangeMaintainingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      dividerColor: theme.border.borderSecondary,
      indicatorColor: theme.foreground.fgBrandPrimaryAlt,
      labelColor: theme.text.textBrandSecondary,
      labelStyle: TextStyle(
        fontSize: duploTextStyles.textSm.fontSize,
        fontWeight: DuploFontWeight.semiBold.value,
      ),
      indicatorSize: TabBarIndicatorSize.tab,
      unselectedLabelColor: theme.text.textQuaternary,
      splashFactory: enableSplashEffect ? null : NoSplash.splashFactory,
      overlayColor:
          enableSplashEffect
              ? null
              : WidgetStateProperty.all(Colors.transparent),
      tabAlignment: isScrollable ? TabAlignment.start : null,
      controller: tabController,
      isScrollable: isScrollable,
      unselectedLabelStyle: TextStyle(
        fontSize: duploTextStyles.textSm.fontSize,
        fontWeight: DuploFontWeight.medium.value,
      ),
      tabs:
          tabTitles
              .map(
                (title) => _TabTitle(title: title, tabTextStyle: tabTextStyle),
              )
              .toList(),
    );
  }
}

class _TabTitle extends StatelessWidget {
  const _TabTitle({required this.title, this.tabTextStyle});
  final DuploTabBarTitle title;
  final DuploTextStyle? tabTextStyle;

  @override
  Widget build(BuildContext context) {
    final child = Tab(
      child: DuploText(
        text: title.text,
        style: tabTextStyle ?? context.duploTextStyles.textSm,
        fontWeight: DuploFontWeight.semiBold,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
    if (title.semanticsIdentifier != null) {
      return Semantics(identifier: title.semanticsIdentifier!, child: child);
    }
    return child;
  }
}

class DuploTabBarTitle {
  final String text;
  final String? semanticsIdentifier;

  const DuploTabBarTitle({required this.text, this.semanticsIdentifier});
}
