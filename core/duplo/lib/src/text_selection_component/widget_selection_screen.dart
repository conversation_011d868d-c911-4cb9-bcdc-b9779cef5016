import 'package:duplo/src/components/duplo_buttons/duplo_button.dart';
import 'package:duplo/src/models/widget_selection_model.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';

class WidgetSelectionScreen extends StatefulWidget {
  final String pageTitle;
  final String buttonTitle;
  final void Function(WidgetSelectionModel) onSelection;
  final List<WidgetSelectionModel> options;
  final WidgetSelectionModel? selected;
  final double topSpacer;

  const WidgetSelectionScreen({
    super.key,
    required this.pageTitle,
    required this.buttonTitle,
    required this.options,
    required this.selected,
    required this.onSelection,
    this.topSpacer = 0,
  });

  @override
  _WidgetSelectionScreenState createState() => _WidgetSelectionScreenState();
}

class _WidgetSelectionScreenState extends State<WidgetSelectionScreen> {
  late WidgetSelectionModel selectedOption;

  @override
  void initState() {
    super.initState();
    selectedOption = (widget.selected ?? widget.options.firstOrNull)!;
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return Container(
      color: theme.background.bgSecondary,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: widget.topSpacer),
          ...widget.options
              .map(
                (option) => GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedOption = option;
                    });
                  },
                  child: Container(
                    color: theme.background.bgSecondary,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                        boxShadow: [
                          BoxShadow(
                            color:
                                selectedOption == option
                                    ? theme.border.borderBrand
                                    : theme.border.borderSecondary,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                        child: Container(
                          color: theme.background.bgPrimary,
                          child: option.displayWidget,
                        ),
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
          const Spacer(),
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: DuploButton.defaultPrimary(
                    title: widget.buttonTitle,
                    onTap: () {
                      widget.onSelection(selectedOption);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
