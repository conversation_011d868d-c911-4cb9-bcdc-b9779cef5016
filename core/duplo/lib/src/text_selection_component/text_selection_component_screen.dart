import 'package:duplo/src/components/duplo_buttons/duplo_button.dart';
import 'package:duplo/src/models/selection_option_model.dart';
import 'package:duplo/src/text_selection_component/text_component_option_item_data.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';

class TextSelectionComponentScreen extends StatefulWidget {
  final String pageTitle;
  final String buttonTitle;
  final void Function(SelectionOptionModel) onSelection;
  final List<SelectionOptionModel> options;
  final SelectionOptionModel? selected;
  final bool isLoading;

  const TextSelectionComponentScreen({
    super.key,
    required this.pageTitle,
    required this.buttonTitle,
    this.isLoading = false,
    required this.options,
    required this.selected,
    required this.onSelection,
  });

  @override
  _TextSelectionComponentScreenState createState() =>
      _TextSelectionComponentScreenState();
}

class _TextSelectionComponentScreenState
    extends State<TextSelectionComponentScreen> {
  late SelectionOptionModel selectedOption;

  @override
  void initState() {
    super.initState();
    selectedOption = (widget.selected ?? widget.options.firstOrNull)!;
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      body: SafeArea(
        top: false,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children:
                      widget.options
                          .map(
                            (option) => GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedOption = option;
                                });
                              },
                              child: TextComponentOptionItemData(
                                displayString: option.displayText,
                                selected: (selectedOption == option),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: DuploButton.defaultPrimary(
                      semanticsIdentifier: 'text_selection_component_button',
                      title: widget.buttonTitle,
                      isLoading: widget.isLoading,
                      onTap: () {
                        widget.onSelection(selectedOption);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
