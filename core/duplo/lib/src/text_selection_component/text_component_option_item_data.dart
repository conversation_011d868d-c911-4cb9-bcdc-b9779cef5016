import 'package:duplo/src/assets/assets.gen.dart' as duploAssets;
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class TextComponentOptionItemData extends StatelessWidget {
  final String displayString;
  final bool selected;

  const TextComponentOptionItemData({
    super.key,
    required this.displayString,
    required this.selected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          border: Border.all(
            color:
                selected
                    ? theme.border.borderBrand
                    : theme.border.borderPrimary,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: DuploText(
                  maxLines: 5,
                  text: displayString,
                  style: duploTextStyles.textMd,
                  color: theme.text.textPrimary,
                  textAlign: TextAlign.start,
                  fontWeight: DuploFontWeight.medium,
                ),
              ),
              SizedBox(
                width: 24, // Fixed width for the icon area
                child:
                    selected
                        ? duploAssets.Assets.images.checkCircle.svg()
                        : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
