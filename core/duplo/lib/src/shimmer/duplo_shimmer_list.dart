import 'package:duplo/src/shimmer/duplo_shimmer_list_item.dart';
import 'package:duplo/src/shimmer/duplo_shimmer_type.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';

class DuploShimmerList extends StatelessWidget {
  final double height;
  final bool hasLeading;
  final bool hasTrailing;
  final double? leadingWidth;
  final double? leadingHeight;
  final int itemCount;
  final ScrollController? scrollController;
  final ScrollPhysics? physics;
  final _DuploShimmerListType type;
  final DuploShimmerType itemShimmerType;
  const DuploShimmerList._({
    super.key,
    this.height = 31,
    this.hasLeading = true,
    this.hasTrailing = true,
    this.itemCount = 10,
    this.leadingWidth,
    this.leadingHeight,
    this.scrollController,
    this.physics,
    required this.type,
    required this.itemShimmerType,
  });

  factory DuploShimmerList({
    Key? key,
    double height = 31,
    bool hasLeading = true,
    bool hasTrailing = true,
    double? leadingWidth,
    double? leadingHeight,
    int itemCount = 10,
    ScrollController? scrollController,
    ScrollPhysics? physics,
    DuploShimmerType itemShimmerType = DuploShimmerType.animated,
  }) => DuploShimmerList._(
    key: key,
    height: height,
    hasLeading: hasLeading,
    hasTrailing: hasTrailing,
    leadingWidth: leadingWidth,
    leadingHeight: leadingHeight,
    itemCount: itemCount,
    scrollController: scrollController,
    physics: physics,
    type: _DuploShimmerListType.widget,
    itemShimmerType: itemShimmerType,
  );

  factory DuploShimmerList.sliver({
    Key? key,
    double height = 31,
    bool hasLeading = true,
    bool hasTrailing = true,
    double? leadingWidth,
    double? leadingHeight,
    int itemCount = 10,
    ScrollController? scrollController,
    ScrollPhysics? physics,
    DuploShimmerType itemShimmerType = DuploShimmerType.animated,
  }) {
    return DuploShimmerList._(
      key: key,
      height: height,
      hasLeading: hasLeading,
      hasTrailing: hasTrailing,
      leadingWidth: leadingWidth,
      leadingHeight: leadingHeight,
      itemCount: itemCount,
      scrollController: scrollController,
      physics: physics,
      type: _DuploShimmerListType.sliver,
      itemShimmerType: itemShimmerType,
    );
  }

  @override
  Widget build(BuildContext context) => switch (type) {
    _DuploShimmerListType.widget => Container(
      height: MediaQuery.sizeOf(context).height * 0.8,
      child: ListView.separated(
        controller: scrollController,
        separatorBuilder:
            (ctx, index) => Container(
              height: 1,
              width: double.infinity,
              color: context.duploTheme.border.borderTertiary,
            ),
        physics: physics ?? const NeverScrollableScrollPhysics(),
        itemCount: itemCount,
        itemBuilder:
            (ctx, index) => switch (itemShimmerType) {
              DuploShimmerType.animated => DuploShimmerListItem(
                hasLeading: hasLeading,
                hasTrailing: hasTrailing,
                leadingWidth: leadingWidth,
                leadingHeight: leadingHeight,
                height: height,
              ),
              DuploShimmerType.static => DuploShimmerListItem.static(
                hasLeading: hasLeading,
                hasTrailing: hasTrailing,
                leadingWidth: leadingWidth,
                leadingHeight: leadingHeight,
                height: height,
              ),
            },
      ),
    ),
    _DuploShimmerListType.sliver => SliverList.separated(
      separatorBuilder:
          (ctx, index) => Container(
            height: 1,
            width: double.infinity,
            color: context.duploTheme.border.borderTertiary,
          ),
      itemBuilder:
          (ctx, index) => switch (itemShimmerType) {
            DuploShimmerType.animated => DuploShimmerListItem(
              hasLeading: hasLeading,
              hasTrailing: hasTrailing,
              leadingWidth: leadingWidth,
              leadingHeight: leadingHeight,
              height: height,
            ),
            DuploShimmerType.static => DuploShimmerListItem.static(
              hasLeading: hasLeading,
              hasTrailing: hasTrailing,
              leadingWidth: leadingWidth,
              leadingHeight: leadingHeight,
              height: height,
            ),
          },
      itemCount: itemCount,
    ),
  };
}

enum _DuploShimmerListType { widget, sliver }
