import 'package:duplo/src/shimmer/duplo_shimmer_list_item.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class DuploMultipleShimmersInRow extends StatelessWidget {
  final int count;
  final double height;
  final double spacing;
  const DuploMultipleShimmersInRow({
    super.key,
    this.height = 31,
    this.spacing = 16,
    required this.count,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children:
          List<Widget>.generate(
            count,
            (index) => Expanded(
              child: DuploShimmerListItem(
                hasLeading: false,
                hasTrailing: false,
                height: height,
              ),
            ),
          ).intersperse(SizedBox(width: spacing)).toList(),
    );
  }
}
