import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class DuploShimmer extends StatelessWidget {
  final Widget child;
  const DuploShimmer({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      direction:
          Directionality.of(context) == TextDirection.ltr
              ? ShimmerDirection.ltr
              : ShimmerDirection.rtl,
      baseColor: context.duploTheme.utility.utilityGray200,
      highlightColor: context.duploTheme.foreground.fgWhite,
      child: child,
    );
  }
}
