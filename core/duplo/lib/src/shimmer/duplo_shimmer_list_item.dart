import 'package:duplo/src/shimmer/duplo_shimmer.dart';
import 'package:duplo/src/shimmer/duplo_shimmer_type.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:flutter/material.dart';

class DuploShimmerListItem extends StatelessWidget {
  final double height;
  final bool hasLeading;
  final bool hasTrailing;
  final double? leadingWidth;
  final double? leadingHeight;
  final DuploShimmerType type;

  const DuploShimmerListItem._({
    super.key,
    this.height = 31,
    this.hasLeading = true,
    this.hasTrailing = true,
    this.leadingWidth,
    this.leadingHeight,
    required this.type,
  });

  factory DuploShimmerListItem({
    Key? key,
    double height = 31,
    bool hasLeading = true,
    bool hasTrailing = true,
    double? leadingWidth,
    double? leadingHeight,
  }) {
    return DuploShimmerListItem._(
      key: key,
      height: height,
      hasLeading: hasLeading,
      hasTrailing: hasTrailing,
      leadingWidth: leadingWidth,
      leadingHeight: leadingHeight,
      type: DuploShimmerType.animated,
    );
  }

  factory DuploShimmerListItem.static({
    Key? key,
    double height = 31,
    bool hasLeading = true,
    bool hasTrailing = true,
    double? leadingWidth,
    double? leadingHeight,
  }) {
    return DuploShimmerListItem._(
      key: key,
      height: height,
      hasLeading: hasLeading,
      hasTrailing: hasTrailing,
      leadingWidth: leadingWidth,
      leadingHeight: leadingHeight,
      type: DuploShimmerType.static,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          (!hasLeading && !hasTrailing)
              ? const EdgeInsetsDirectional.all(0)
              : const EdgeInsetsDirectional.all(16),
      child: SizedBox(
        height: height,
        width: double.infinity,
        child: Row(
          children: [
            if (hasLeading)
              switch (type) {
                DuploShimmerType.animated => DuploShimmer(
                  child: _Content(
                    leadingWidth: leadingWidth,
                    leadingHeight: leadingHeight,
                    borderRadius: 999,
                  ),
                ),
                DuploShimmerType.static => _Content(
                  leadingWidth: leadingWidth,
                  leadingHeight: leadingHeight,
                  borderRadius: 999,
                ),
              },
            if (hasLeading) SizedBox(width: 4),
            Expanded(
              flex: 2,
              child: switch (type) {
                DuploShimmerType.animated => DuploShimmer(
                  child: _Content(borderRadius: 6),
                ),
                DuploShimmerType.static => _Content(borderRadius: 6),
              },
            ),
            if (hasTrailing) Spacer(),
            if (hasTrailing)
              Expanded(
                child: switch (type) {
                  DuploShimmerType.animated => DuploShimmer(
                    child: _Content(borderRadius: 6),
                  ),
                  DuploShimmerType.static => _Content(borderRadius: 6),
                },
              ),
          ],
        ),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content({
    this.leadingWidth,
    this.leadingHeight,
    this.borderRadius = 999,
  });

  final double? leadingWidth;
  final double? leadingHeight;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.duploTheme.utility.utilityGray200,
        borderRadius: BorderRadius.all(Radius.circular(borderRadius)),
      ),
      width: leadingWidth,
      height: leadingHeight,
    );
  }
}
