import 'dart:math';

import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/draggable_handle.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';
import 'package:smooth_sheets/smooth_sheets.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

class DuploSheet {
  static Future<T?> showModalSheetV2<T>(
    BuildContext context, {
    required Widget content,
    RouteSettings? settings,
    PreferredSizeWidget? appBar,
    Widget? bottomBar,
    Widget Function(Widget child)? builder,
    bool barrierDismissible = true,
  }) {
    final theme = context.duploTheme;
    final modalRoute = CupertinoModalSheetRoute<T>(
      overlayColor: const Color(0x33ffffff),
      swipeDismissible: true,
      settings: settings,
      barrierDismissible: barrierDismissible,
      swipeDismissSensitivity: const SwipeDismissSensitivity(
        minFlingVelocityRatio: 2.0,
        minDragDistance: 300.0,
      ),
      builder:
          (_) => SheetKeyboardDismissible(
            dismissBehavior: SheetKeyboardDismissBehavior.onDragDown(
              isContentScrollAware: true,
            ),
            child: Sheet(
              scrollConfiguration: SheetScrollConfiguration(),
              physics: BouncingSheetPhysics(),
              decoration: BoxSheetDecoration(
                size: SheetSize.stretch,
                decoration: ShapeDecoration(
                  color: theme.background.bgPrimary,

                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                ),
              ),
              child: _buildSheetContent(
                theme,
                appBar,
                content,
                bottomBar,
                builder,
              ),
            ),
          ),
    );

    return Navigator.push(context, modalRoute);
  }

  static Widget _buildSheetContent(
    DuploThemeData theme,
    PreferredSizeWidget? appBar,
    Widget content,
    Widget? bottomBar,
    Widget Function(Widget child)? builder,
  ) {
    final sheetContent = ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10),
        topRight: Radius.circular(10),
      ),
      child: SheetContentScaffold(
        backgroundColor: theme.background.bgPrimary,
        topBar: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [DraggableHandle(), appBar ?? const SizedBox.shrink()],
        ),
        bottomBarVisibility: const BottomBarVisibility.always(
          // Make the bottom bar visible when the keyboard is open.
          ignoreBottomInset: true,
        ),
        body: content,
        bottomBar:
            bottomBar != null ? SafeArea(top: false, child: bottomBar) : null,
      ),
    );

    // Apply builder if provided, otherwise return content directly
    return builder != null ? builder(sheetContent) : sheetContent;
  }

  static Future<T?> showModalSheet<T>({
    required BuildContext context,
    bool? resizeToAvoidBottomInset,
    required String title,
    String? subtitle,
    Widget? leadingWidget,
    Widget? topBarTitle,
    void Function(bool isCloseUsingDrag)? onClose,
    Widget Function(Widget)? builder,
    bool isFullScreen = false,
    RouteSettings? routeSettings,
    bool isTopBarLayerAlwaysVisible = true,
    required Widget Function(BuildContext) content,
    bool hasTopBarLayer = true,
    Widget Function(BuildContext)? actionBar,
    bool hideCloseButton = false,
    bool hideTitle = false,
    bool useSafeArea = true,
    double? navBarHeight,
    Color? backgroundColor,
    bool barrierDismissible = true,
  }) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return WoltModalSheet.show<T>(
      context: context,
      modalTypeBuilder: (_) => EquitiSheet(),
      settings: routeSettings,
      onModalDismissedWithDrag: () {
        onClose?.call(true);
        Navigator.of(context).pop(null);
      },
      onModalDismissedWithBarrierTap: () {
        onClose?.call(true);
        Navigator.of(context).pop(null);
      },
      modalDecorator: builder,
      barrierDismissible: barrierDismissible,
      pageListBuilder:
          (bottomSheetContext) => [
            WoltModalSheetPage(
              useSafeArea: useSafeArea,
              surfaceTintColor: theme.background.bgPrimary,
              resizeToAvoidBottomInset: resizeToAvoidBottomInset,
              forceMaxHeight: isFullScreen,
              hasTopBarLayer: hasTopBarLayer,
              isTopBarLayerAlwaysVisible: isTopBarLayerAlwaysVisible,
              navBarHeight: navBarHeight,
              topBarTitle: topBarTitle,
              leadingNavBarWidget:
                  hideTitle
                      ? leadingWidget
                      : Align(
                        alignment:
                            Directionality.of(context) == TextDirection.rtl
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        child: Padding(
                          padding:
                              Directionality.of(context) == TextDirection.rtl
                                  ? const EdgeInsets.only(right: 16.0)
                                  : const EdgeInsets.only(left: 16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(height: 10),
                              DuploText(
                                text: title,
                                fontWeight: DuploFontWeight.bold,
                                style: duploTextStyles.textLg,
                                color: theme.text.textPrimary,
                              ),
                              subtitle != null
                                  ? DuploText(
                                    text: subtitle,
                                    fontWeight: DuploFontWeight.regular,
                                    style: duploTextStyles.textXs,
                                    color: theme.text.textTertiary,
                                  )
                                  : Container(),
                            ],
                          ),
                        ),
                      ),
              backgroundColor: backgroundColor ?? theme.background.bgPrimary,
              trailingNavBarWidget:
                  hideCloseButton
                      ? null
                      : IconButton(
                        onPressed: () {
                          onClose?.call(true);
                          Navigator.pop(bottomSheetContext, null);
                        },
                        icon: Assets.images.closeIc.svg(),
                      ),
              child: content(bottomSheetContext),
              stickyActionBar: actionBar?.call(bottomSheetContext),
            ),
          ],
    );
  }

  static Future<T?> showNonScrollableModalSheet<T>({
    required BuildContext context,
    bool? resizeToAvoidBottomInset,
    String? title,
    double? navBarHeight,
    RouteSettings? routeSettings,
    Widget? leadingNavBarWidget,
    bool? enableDrag,
    Widget Function(Widget)? builder,
    void Function(bool isCloseUsingDrag)? onClose,
    required Widget Function(BuildContext) content,
    AlignmentGeometry alignment = AlignmentDirectional.centerStart,
    DuploTextStyle? titleStyle,
    DuploFontWeight? fontWeight,
    bool hasTopBarLayer = true,
    bool useSafeArea = true,
    bool hasTrailingIc = true,
    Color? backgroundColor,
    bool applyLeadingPadding = true,
    bool barrierDismissible = true,
  }) {
    final theme = context.duploTheme;

    return WoltModalSheet.show<T>(
      context: context,
      settings: routeSettings,
      modalTypeBuilder: (modalTypeBuilderContext) => EquitiSheet(),
      onModalDismissedWithDrag: () {
        onClose?.call(true);
        Navigator.of(context).pop(null);
      },
      onModalDismissedWithBarrierTap: () {
        onClose?.call(true);
        Navigator.of(context).pop(null);
      },
      modalDecorator: builder,
      barrierDismissible: barrierDismissible,
      pageListBuilder:
          (bottomSheetContext) => [
            NonScrollingWoltModalSheetPage(
              useSafeArea: useSafeArea,
              enableDrag: enableDrag,
              resizeToAvoidBottomInset: resizeToAvoidBottomInset,
              hasTopBarLayer: hasTopBarLayer,
              navBarHeight: navBarHeight,
              leadingNavBarWidget: Align(
                alignment: alignment,
                child:
                    applyLeadingPadding
                        ? Padding(
                          padding: const EdgeInsetsDirectional.only(
                            start: 16.0,
                          ),
                          child: _headerWidget(
                            title,
                            leadingNavBarWidget,
                            context,
                            titleStyle,
                            fontWeight,
                          ),
                        )
                        : _headerWidget(
                          title,
                          leadingNavBarWidget,
                          context,
                          titleStyle,
                          fontWeight,
                        ),
              ),
              backgroundColor: backgroundColor ?? theme.background.bgPrimary,
              trailingNavBarWidget:
                  hasTrailingIc
                      ? IconButton(
                        onPressed: () {
                          onClose?.call(false);
                          Navigator.of(bottomSheetContext).pop(null);
                        },
                        icon: Assets.images.closeIc.svg(),
                      )
                      : null,
              child: content(bottomSheetContext),
            ),
          ],
    );
  }

  static Widget _headerWidget(
    String? title,
    Widget? leadingNavBarWidget,
    BuildContext context,
    DuploTextStyle? titleStyle,
    DuploFontWeight? fontWeight,
  ) {
    if (title != null) {
      return DuploText(
        text: title,
        fontWeight: fontWeight ?? DuploFontWeight.bold,
        style: titleStyle ?? context.duploTextStyles.textLg,
        color: context.duploTheme.text.textPrimary,
      );
    }

    return leadingNavBarWidget ?? const SizedBox.shrink();
  }
}

class EquitiSheet extends WoltBottomSheetType {
  const EquitiSheet()
    : super(transitionDuration: const Duration(milliseconds: 600));

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: RubberBandCurve(),
    );

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.0, 1.0),
        end: Offset.zero,
      ).animate(curvedAnimation),
      child: child,
    );
  }
}

class RubberBandCurve extends Curve {
  final double elasticity;
  final int oscillations;

  const RubberBandCurve({this.elasticity = 0.8, this.oscillations = 1});

  @override
  double transform(double t) {
    if (t >= 1.0) return 1.0;
    if (t <= 0.0) return 0.0;

    // Start with a smooth ease-out curve as the base
    double value =
        1.0 -
        pow(
          1.0 - t,
          2.5,
        ).toDouble(); // Changed power from 3 to 2.5 for faster initial movement

    final dampingFactor =
        pow(
          1.0 - t,
          2.2,
        ).toDouble(); // Increased from 1.5 to 2.2 for slower return
    value += elasticity * sin(t * oscillations * pi) * dampingFactor;

    // Apply a stronger overshoot at the beginning for that "snap" feeling
    if (t < 0.25) {
      // Increased from 0.2 to 0.25 for longer initial overshoot
      value +=
          elasticity *
          0.6 *
          t *
          (0.25 - t); // Increased from 0.5 to 0.6 for more pronounced overshoot
    }

    return value.clamp(0.0, 1.0);
  }
}
