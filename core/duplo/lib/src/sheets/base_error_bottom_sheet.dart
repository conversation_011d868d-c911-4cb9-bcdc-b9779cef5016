import 'package:duplo/src/components/duplo_buttons/duplo_button.dart';
import 'package:duplo/src/constants/duplo_spacing.dart';
import 'package:duplo/src/sheets/duplo_sheet.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class BaseErrorBottomSheet extends StatelessWidget {
  const BaseErrorBottomSheet({
    super.key,
    required this.onPrimaryButtonPressed,
    required this.onSecondaryButtonPressed,
    required this.title,
    required this.body,
    required this.primaryButtonText,
    required this.secondaryButtonText,
    required this.asset,
    this.onTertiaryButtonPressed,
    this.tertiaryButtonText,
    this.semanticsIdentifierPrimary,
    this.semanticsIdentifierSecondary,
    this.semanticsIdentifierTertiary,
  });
  final void Function() onPrimaryButtonPressed;
  final void Function() onSecondaryButtonPressed;
  final void Function()? onTertiaryButtonPressed;
  final String title, body, primaryButtonText, secondaryButtonText;
  final String? tertiaryButtonText;
  final Widget asset;
  final String? semanticsIdentifierPrimary;
  final String? semanticsIdentifierSecondary;
  final String? semanticsIdentifierTertiary;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        asset,
        Container(
          margin: EdgeInsets.symmetric(
            vertical: DuploSpacing.spacing_lg_12,
            horizontal: DuploSpacing.spacing_xl_16,
          ),
          child: Column(
            children: [
              DuploText(
                text: title,
                style: textStyles.textXl,
                textAlign: TextAlign.center,
                color: theme.text.textPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
              SizedBox(height: DuploSpacing.spacing_md_8),
              DuploText(
                text: body,
                style: textStyles.textSm,
                textAlign: TextAlign.center,
                color: theme.text.textSecondary,
                fontWeight: DuploFontWeight.regular,
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: Column(
            children: [
              DuploButton.defaultPrimary(
                semanticsIdentifier: semanticsIdentifierPrimary,
                title: primaryButtonText,
                onTap: onPrimaryButtonPressed,
                useFullWidth: true,
              ),
              SizedBox(height: DuploSpacing.spacing_xl_16),
              DuploButton.secondary(
                useFullWidth: true,
                semanticsIdentifier: semanticsIdentifierSecondary,
                title: secondaryButtonText,
                onTap: onSecondaryButtonPressed,
              ),
              if (tertiaryButtonText != null)
                SizedBox(height: DuploSpacing.spacing_xl_16),
              if (onTertiaryButtonPressed != null)
                DuploButton.tertiary(
                  semanticsIdentifier: semanticsIdentifierTertiary,
                  useFullWidth: true,
                  title: tertiaryButtonText ?? '',
                  onTap: onTertiaryButtonPressed!,
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// Shows the error bottom sheet without requiring a BuildContext.
  /// This method can be called from a bloc or any other place without context.
  ///
  /// Uses the navigatorKey registered in the DI container to access current context.
  static void show({
    required void Function() onPrimaryButtonPressed,
    required void Function() onSecondaryButtonPressed,
    required String title,
    required String body,
    required String primaryButtonText,
    required String secondaryButtonText,
    required Widget asset,
    void Function()? onTertiaryButtonPressed,
    String? tertiaryButtonText,
    void Function(bool isCloseUsingDrag)? onClose,
    String? semanticsIdentifierPrimary,
    String? semanticsIdentifierSecondary,
    String? semanticsIdentifierTertiary,
  }) {
    // Get the navigator key from dependency injection
    final navigatorKey = GetIt.instance<GlobalKey<NavigatorState>>();

    // Check if we have a valid context
    if (navigatorKey.currentContext != null) {
      DuploSheet.showModalSheet<void>(
        context: navigatorKey.currentContext!,
        title: '',
        hideTitle: true,
        isTopBarLayerAlwaysVisible: false,
        onClose: onClose,
        content:
            (_) => BaseErrorBottomSheet(
              semanticsIdentifierPrimary: semanticsIdentifierPrimary,

              semanticsIdentifierSecondary: semanticsIdentifierSecondary,
              semanticsIdentifierTertiary: semanticsIdentifierTertiary,
              onPrimaryButtonPressed: onPrimaryButtonPressed,
              onSecondaryButtonPressed: onSecondaryButtonPressed,
              title: title,
              body: body,
              primaryButtonText: primaryButtonText,
              secondaryButtonText: secondaryButtonText,
              asset: asset,
              onTertiaryButtonPressed: onTertiaryButtonPressed,
              tertiaryButtonText: tertiaryButtonText,
            ),
      );
    }
  }
}
