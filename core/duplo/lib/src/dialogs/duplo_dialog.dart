import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';

import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';

import 'package:flutter/material.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

class DuploDialog {
  static void showDialog({
    required BuildContext context,
    required String title,
    DuploTextStyle? titleStyle,
    String? subTitle,
    DuploTextStyle? subTitleStyle,
    required Widget Function(BuildContext) content,
    bool barrierDismissible = true,
    Widget Function(BuildContext)? actionBar,
    bool hasPadding = true,
    double contentHeight = 150,
    void Function(bool isCloseUsingBackground)? onClose,
  }) {
    final theme = DuploTheme.of(context);
    WoltModalSheet.show<Widget>(
      context: context,
      barrierDismissible: barrierDismissible,
      modalTypeBuilder: (modalTypeBuilderContext) => BottomAlertModalSheet(),
      onModalDismissedWithDrag: () {
        onClose?.call(true);
      },
      pageListBuilder:
          (bottomSheetContext) => [
            WoltModalSheetPage(
              enableDrag: false,
              useSafeArea: true,
              hasTopBarLayer: false,
              leadingNavBarWidget: Padding(
                padding: const EdgeInsets.only(
                  left: 16.0,
                  right: 16.0,
                  top: 16,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    DuploText(
                      text: title,
                      style: titleStyle ?? context.duploTextStyles.displaySm,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      color: theme.text.textPrimary,
                      fontWeight: DuploFontWeight.bold,
                    ),
                    if (subTitle != null)
                      DuploText(
                        text: subTitle,
                        style: subTitleStyle ?? context.duploTextStyles.textSm,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        color: theme.text.textTertiary,
                      ),
                  ],
                ),
              ),
              backgroundColor: theme.background.bgPrimary,
              child: Padding(
                padding:
                    hasPadding
                        ? const EdgeInsets.only(
                          left: 16.0,
                          right: 16.0,
                          bottom: 16.0,
                        )
                        : EdgeInsets.zero,
                child: SizedBox(
                  height: contentHeight,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: content(bottomSheetContext),
                        ),
                      ),
                      actionBar?.call(bottomSheetContext) ??
                          const SizedBox.shrink(),
                    ],
                  ),
                ),
              ),
            ),
          ],
    );
  }

  static void showInfoDialog({
    required BuildContext context,
    required String description,
    required String title,
    String? warningDescription,
    String? warningTitle,
    bool barrierDismissible = true,
    void Function(bool isCloseUsingBackground)? onClose,
    Widget Function(BuildContext)? content,
  }) {
    final theme = DuploTheme.of(context);
    WoltModalSheet.show<Widget>(
      context: context,
      barrierDismissible: barrierDismissible,
      modalTypeBuilder: (modalTypeBuilderContext) {
        return BottomAlertModalSheet();
      },
      onModalDismissedWithDrag: () {
        onClose?.call(true);
      },
      pageListBuilder:
          (bottomSheetContext) => [
            WoltModalSheetPage(
              enableDrag: false,
              useSafeArea: true,
              hasTopBarLayer: false,
              backgroundColor: theme.background.bgSecondary,
              resizeToAvoidBottomInset: true,
              child: Container(
                padding: const EdgeInsets.all(16),
                color: context.duploTheme.background.bgSecondary,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: theme.border.borderBrand,
                              size: 24,
                            ),
                            const SizedBox(width: 6),
                            DuploText(
                              text: title,
                              style: context.duploTextStyles.textLg,
                              color: theme.text.textPrimary,
                              fontWeight: DuploFontWeight.bold,
                            ),
                          ],
                        ),
                        InkWell(
                          onTap: () {
                            Navigator.of(bottomSheetContext).pop();
                            onClose?.call(false);
                          },
                          child: Icon(
                            Icons.close,
                            color: theme.foreground.fgSecondary,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                    Divider(color: context.duploTheme.border.borderPrimary),
                    content != null
                        ? content(bottomSheetContext)
                        : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DuploText(
                              text: description,
                              textAlign: TextAlign.start,
                              style: context.duploTextStyles.textSm,
                              color: context.duploTheme.text.textSecondary,
                            ),
                            if (warningDescription != null ||
                                warningTitle != null)
                              Column(
                                children: [
                                  const SizedBox(height: 16),
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color:
                                          context
                                              .duploTheme
                                              .utility
                                              .utilityError50,
                                      border: Border.all(
                                        color:
                                            context
                                                .duploTheme
                                                .utility
                                                .utilityError200,
                                      ),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.warning_amber_rounded,
                                          color:
                                              context
                                                  .duploTheme
                                                  .utility
                                                  .utilityError600,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              DuploText(
                                                text: warningTitle,
                                                style:
                                                    context
                                                        .duploTextStyles
                                                        .textSm,
                                                color:
                                                    context
                                                        .duploTheme
                                                        .text
                                                        .textSecondary,
                                                fontWeight:
                                                    DuploFontWeight.semiBold,
                                                textAlign: TextAlign.start,
                                              ),
                                              Container(
                                                child: DuploText(
                                                  text: warningDescription,
                                                  style:
                                                      context
                                                          .duploTextStyles
                                                          .textSm,
                                                  color:
                                                      context
                                                          .duploTheme
                                                          .text
                                                          .textSecondary,
                                                  textAlign: TextAlign.start,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                  ],
                ),
              ),
            ),
          ],
    );
  }
}

class BottomAlertModalSheet extends WoltModalType {
  const BottomAlertModalSheet()
    : super(
        shapeBorder: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(24)),
        ),
        showDragHandle: false,
        forceMaxHeight: false,
      );
  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: animation.drive(
          Tween(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).chain(CurveTween(curve: Curves.easeOutQuad)),
        ),
        child: child,
      ),
    );
  }

  @override
  BoxConstraints layoutModal(Size availableSize) {
    return BoxConstraints(
      minWidth: availableSize.width - 64,
      maxWidth: availableSize.width - 64,
      minHeight: 0,
      maxHeight: availableSize.height * 0.8,
    );
  }

  @override
  Offset positionModal(
    Size availableSize,
    Size modalContentSize,
    TextDirection textDirection,
  ) {
    double horizontalPosition =
        (availableSize.width - modalContentSize.width) / 2;
    return Offset(
      horizontalPosition,
      availableSize.height - modalContentSize.height - 50,
    );
  }

  @override
  String routeLabel(BuildContext context) {
    final MaterialLocalizations localizations = MaterialLocalizations.of(
      context,
    );
    return localizations.dialogLabel;
  }
}
