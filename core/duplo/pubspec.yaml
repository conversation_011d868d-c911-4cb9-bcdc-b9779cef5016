name: duplo
description: equiti design system package
version: 1.0.0+1
publish_to: none

environment:
  sdk: 3.8.1

dev_dependencies:
  build_runner: 2.5.4
  dependency_validator: 5.0.2
  equiti_lint:
    path: ../../utilities/equiti_lint
  bdd_widget_test: 1.8.1
  bdd_steps:
    path: ../../utilities/bdd_steps
  equiti_test:
    path: ../../utilities/equiti_test
  flutter_test:
    sdk: flutter
  flutter_svg_test: 1.0.3
  flutter_gen_runner: 5.10.0
  injectable_generator: 2.7.0
  dart_code_metrics_presets: 2.22.0
  mocktail: 1.0.4
  build_verify: 3.1.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/animations/
    - assets/flags/
    - assets/gifs/
    - test/assets/
    - assets/

  fonts:
    - family: Inter
      fonts:
        - asset: ../../fonts/Inter.ttf
    - family: NotoNaskhArabic
      fonts:
        - asset: ../../fonts/NotoNaskhArabic.ttf

dependencies:
  flutter:
    sdk: flutter
  very_good_infinite_list: 0.9.0
  flutter_svg: 2.0.10+1
  cached_network_image: 3.4.1
  wolt_modal_sheet: 0.10.0
  toastification: 2.3.0
  shimmer: 3.0.0
  video_player: 2.9.3
  get_it: 8.0.3
  injectable: 2.5.0
  intl: 0.20.2
  lottie: 3.3.1
  prelude:
    path: ../../utilities/prelude
  locale_manager:
    path: ../../utilities/locale_manager
  theme_manager:
    path: ../../utilities/theme_manager
  equiti_localization:
    path: ../equiti_localization
  pinput: 5.0.1
  freezed_annotation: 3.0.0
  custom_action_keyboard:
    path: ../../utilities/custom_action_keyboard
  smooth_sheets: ^0.14.0

dependency_overrides:
  dart_style: ^3.0.0
  analyzer: ^6.3.0  # Also include this to maintain consistency with your other overrides

flutter_gen:
  output: lib/src/assets/
  assets:
    exclude:
      - resources/mocks/**/*
    outputs:
      package_parameter_enabled: true
  integrations:
    flutter_svg: true
