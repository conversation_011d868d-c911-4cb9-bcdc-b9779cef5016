import 'package:broker_settings/src/domain/model/broker_settings_response.dart';
import 'package:broker_settings/src/domain/repository/broker_settings_repository.dart';
import 'package:prelude/prelude.dart';
import 'package:user_account/user_account.dart';

class GetClientPermissionsUseCase {
  final BrokerSettingsRepository repository;
  final GetBrokerIdUseCase getBrokerIdUseCase;

  const GetClientPermissionsUseCase({
    required this.repository,
    required this.getBrokerIdUseCase,
  });
  TaskEither<Exception, ClientPermissions?> call() {
    final brokerId = getBrokerIdUseCase();
    if (brokerId == null) {
      return TaskEither.left(Exception('Broker ID not found'));
    }
    return repository
        .getBrokerSettingsData(brokerId: brokerId)
        .map((response) => response.data.clientPermissions);
  }
}
