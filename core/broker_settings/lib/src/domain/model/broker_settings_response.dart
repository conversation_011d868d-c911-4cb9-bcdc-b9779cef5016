import 'package:broker_settings/src/domain/enums/broker_settings_enum.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'broker_settings_response.freezed.dart';
part 'broker_settings_response.g.dart';

@freezed
sealed class BrokerSettingsResponse with _$BrokerSettingsResponse {
  const factory BrokerSettingsResponse({
    required bool success,
    required BrokerSettingsData data,
  }) = _BrokerSettingsResponse;

  factory BrokerSettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$BrokerSettingsResponseFromJson(json);
}

@freezed
sealed class BrokerSettingsData with _$BrokerSettingsData {
  const factory BrokerSettingsData({
    required List<TradingPlatform> platforms,
    required List<String> currencies,
    ClientPermissions? clientPermissions,
  }) = _BrokerSettingsData;

  factory BrokerSettingsData.from<PERSON>son(Map<String, dynamic> json) =>
      _$BrokerSettingsDataFromJson(json);
}

@Freezed(unionKey: 'code')
sealed class TradingPlatform with _$TradingPlatform {
  const TradingPlatform._();

  const factory TradingPlatform.mt4({
    required AccountCreationPlatform code,
    required String name,
    required AccountCreationAvailability accountCreationAvailability,
    required List<AccountType> accountTypes,
  }) = Mt4Platform;

  const factory TradingPlatform.mt5({
    required AccountCreationPlatform code,
    required String name,
    required AccountCreationAvailability accountCreationAvailability,
    required List<AccountType> accountTypes,
  }) = Mt5Platform;

  const factory TradingPlatform.dulcimer({
    required AccountCreationPlatform code,
    required String name,
    required AccountCreationAvailability accountCreationAvailability,
    required List<AccountType> accountTypes,
  }) = DulcimerPlatform;

  factory TradingPlatform.fromJson(Map<String, dynamic> json) =>
      _$TradingPlatformFromJson(json);

  String get description {
    return switch (this) {
      Mt4Platform() => 'Classic platform for forex trading',
      Mt5Platform() => 'Multi-asset platform for forex, stocks & more',
      DulcimerPlatform() => 'Trade using our all-in-one platform',
    };
  }
}

@freezed
sealed class AccountCreationAvailability with _$AccountCreationAvailability {
  const factory AccountCreationAvailability({@Default(true) bool enabled}) =
      _AccountCreationAvailability;

  factory AccountCreationAvailability.fromJson(Map<String, dynamic> json) =>
      _$AccountCreationAvailabilityFromJson(json);
}

@freezed
sealed class AccountType with _$AccountType {
  const factory AccountType({
    required AccountCreationType name,
    required String title,
    required String subTitle,
    required List<String> features,
    required List<String> currencies,
    required List<AccountVariant> variants,
    @Default(true) bool requirePassword,
    int? minDeposit,
  }) = _AccountType;

  factory AccountType.fromJson(Map<String, dynamic> json) =>
      _$AccountTypeFromJson(json);
}

@freezed
sealed class AccountVariant with _$AccountVariant {
  const AccountVariant._();

  const factory AccountVariant({
    required bool swapFree,
    required int maxLeverage,
    required List<int> leverages,
  }) = _AccountVariant;

  factory AccountVariant.fromJson(Map<String, dynamic> json) =>
      _$AccountVariantFromJson(json);

  String get title {
    return swapFree ? 'Swap-Free' : 'Swap-Enabled';
  }

  String get subtitle {
    return swapFree
        ? 'No overnight fees. Ideal if you don\'t want to pay or earn interest on trades left open overnight.'
        : 'Overnight fees apply. You may pay or earn interest when you keep trades open after the market closes.';
  }
}

@freezed
sealed class ClientPermissions with _$ClientPermissions {
  const factory ClientPermissions({
    required bool depositFunds,
    required bool editProfile,
    required bool transferFunds,
    required bool withdrawFunds,
    required bool changePassword,
  }) = _ClientPermissions;

  factory ClientPermissions.fromJson(Map<String, dynamic> json) =>
      _$ClientPermissionsFromJson(json);
}
