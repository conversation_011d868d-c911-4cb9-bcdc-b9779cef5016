// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'broker_settings_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BrokerSettingsResponse _$BrokerSettingsResponseFromJson(
  Map<String, dynamic> json,
) => _BrokerSettingsResponse(
  success: json['success'] as bool,
  data: BrokerSettingsData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$BrokerSettingsResponseToJson(
  _BrokerSettingsResponse instance,
) => <String, dynamic>{'success': instance.success, 'data': instance.data};

_BrokerSettingsData _$BrokerSettingsDataFromJson(Map<String, dynamic> json) =>
    _BrokerSettingsData(
      platforms:
          (json['platforms'] as List<dynamic>)
              .map((e) => TradingPlatform.fromJson(e as Map<String, dynamic>))
              .toList(),
      currencies:
          (json['currencies'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
      clientPermissions:
          json['clientPermissions'] == null
              ? null
              : ClientPermissions.fromJson(
                json['clientPermissions'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$BrokerSettingsDataToJson(_BrokerSettingsData instance) =>
    <String, dynamic>{
      'platforms': instance.platforms,
      'currencies': instance.currencies,
      'clientPermissions': instance.clientPermissions,
    };

Mt4Platform _$Mt4PlatformFromJson(Map<String, dynamic> json) => Mt4Platform(
  code: $enumDecode(_$AccountCreationPlatformEnumMap, json['code']),
  name: json['name'] as String,
  accountCreationAvailability: AccountCreationAvailability.fromJson(
    json['accountCreationAvailability'] as Map<String, dynamic>,
  ),
  accountTypes:
      (json['accountTypes'] as List<dynamic>)
          .map((e) => AccountType.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$Mt4PlatformToJson(Mt4Platform instance) =>
    <String, dynamic>{
      'code': _$AccountCreationPlatformEnumMap[instance.code]!,
      'name': instance.name,
      'accountCreationAvailability': instance.accountCreationAvailability,
      'accountTypes': instance.accountTypes,
    };

const _$AccountCreationPlatformEnumMap = {
  AccountCreationPlatform.dulcimer: 'dulcimer',
  AccountCreationPlatform.mt4: 'mt4',
  AccountCreationPlatform.mt5: 'mt5',
};

Mt5Platform _$Mt5PlatformFromJson(Map<String, dynamic> json) => Mt5Platform(
  code: $enumDecode(_$AccountCreationPlatformEnumMap, json['code']),
  name: json['name'] as String,
  accountCreationAvailability: AccountCreationAvailability.fromJson(
    json['accountCreationAvailability'] as Map<String, dynamic>,
  ),
  accountTypes:
      (json['accountTypes'] as List<dynamic>)
          .map((e) => AccountType.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$Mt5PlatformToJson(Mt5Platform instance) =>
    <String, dynamic>{
      'code': _$AccountCreationPlatformEnumMap[instance.code]!,
      'name': instance.name,
      'accountCreationAvailability': instance.accountCreationAvailability,
      'accountTypes': instance.accountTypes,
    };

DulcimerPlatform _$DulcimerPlatformFromJson(Map<String, dynamic> json) =>
    DulcimerPlatform(
      code: $enumDecode(_$AccountCreationPlatformEnumMap, json['code']),
      name: json['name'] as String,
      accountCreationAvailability: AccountCreationAvailability.fromJson(
        json['accountCreationAvailability'] as Map<String, dynamic>,
      ),
      accountTypes:
          (json['accountTypes'] as List<dynamic>)
              .map((e) => AccountType.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$DulcimerPlatformToJson(DulcimerPlatform instance) =>
    <String, dynamic>{
      'code': _$AccountCreationPlatformEnumMap[instance.code]!,
      'name': instance.name,
      'accountCreationAvailability': instance.accountCreationAvailability,
      'accountTypes': instance.accountTypes,
    };

_AccountCreationAvailability _$AccountCreationAvailabilityFromJson(
  Map<String, dynamic> json,
) => _AccountCreationAvailability(enabled: json['enabled'] as bool? ?? true);

Map<String, dynamic> _$AccountCreationAvailabilityToJson(
  _AccountCreationAvailability instance,
) => <String, dynamic>{'enabled': instance.enabled};

_AccountType _$AccountTypeFromJson(Map<String, dynamic> json) => _AccountType(
  name: $enumDecode(_$AccountCreationTypeEnumMap, json['name']),
  title: json['title'] as String,
  subTitle: json['subTitle'] as String,
  features:
      (json['features'] as List<dynamic>).map((e) => e as String).toList(),
  currencies:
      (json['currencies'] as List<dynamic>).map((e) => e as String).toList(),
  variants:
      (json['variants'] as List<dynamic>)
          .map((e) => AccountVariant.fromJson(e as Map<String, dynamic>))
          .toList(),
  requirePassword: json['requirePassword'] as bool? ?? true,
  minDeposit: (json['minDeposit'] as num?)?.toInt(),
);

Map<String, dynamic> _$AccountTypeToJson(_AccountType instance) =>
    <String, dynamic>{
      'name': _$AccountCreationTypeEnumMap[instance.name]!,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'features': instance.features,
      'currencies': instance.currencies,
      'variants': instance.variants,
      'requirePassword': instance.requirePassword,
      'minDeposit': instance.minDeposit,
    };

const _$AccountCreationTypeEnumMap = {
  AccountCreationType.standard: 'Standard',
  AccountCreationType.premier: 'Premier',
  AccountCreationType.crypto: 'Crypto',
};

_AccountVariant _$AccountVariantFromJson(Map<String, dynamic> json) =>
    _AccountVariant(
      swapFree: json['swapFree'] as bool,
      maxLeverage: (json['maxLeverage'] as num).toInt(),
      leverages:
          (json['leverages'] as List<dynamic>)
              .map((e) => (e as num).toInt())
              .toList(),
    );

Map<String, dynamic> _$AccountVariantToJson(_AccountVariant instance) =>
    <String, dynamic>{
      'swapFree': instance.swapFree,
      'maxLeverage': instance.maxLeverage,
      'leverages': instance.leverages,
    };

_ClientPermissions _$ClientPermissionsFromJson(Map<String, dynamic> json) =>
    _ClientPermissions(
      depositFunds: json['depositFunds'] as bool,
      editProfile: json['editProfile'] as bool,
      transferFunds: json['transferFunds'] as bool,
      withdrawFunds: json['withdrawFunds'] as bool,
      changePassword: json['changePassword'] as bool,
    );

Map<String, dynamic> _$ClientPermissionsToJson(_ClientPermissions instance) =>
    <String, dynamic>{
      'depositFunds': instance.depositFunds,
      'editProfile': instance.editProfile,
      'transferFunds': instance.transferFunds,
      'withdrawFunds': instance.withdrawFunds,
      'changePassword': instance.changePassword,
    };
