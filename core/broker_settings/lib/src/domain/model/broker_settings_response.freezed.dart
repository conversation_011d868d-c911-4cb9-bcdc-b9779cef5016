// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'broker_settings_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BrokerSettingsResponse {

 bool get success; BrokerSettingsData get data;
/// Create a copy of BrokerSettingsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BrokerSettingsResponseCopyWith<BrokerSettingsResponse> get copyWith => _$BrokerSettingsResponseCopyWithImpl<BrokerSettingsResponse>(this as BrokerSettingsResponse, _$identity);

  /// Serializes this BrokerSettingsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BrokerSettingsResponse&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'BrokerSettingsResponse(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class $BrokerSettingsResponseCopyWith<$Res>  {
  factory $BrokerSettingsResponseCopyWith(BrokerSettingsResponse value, $Res Function(BrokerSettingsResponse) _then) = _$BrokerSettingsResponseCopyWithImpl;
@useResult
$Res call({
 bool success, BrokerSettingsData data
});


$BrokerSettingsDataCopyWith<$Res> get data;

}
/// @nodoc
class _$BrokerSettingsResponseCopyWithImpl<$Res>
    implements $BrokerSettingsResponseCopyWith<$Res> {
  _$BrokerSettingsResponseCopyWithImpl(this._self, this._then);

  final BrokerSettingsResponse _self;
  final $Res Function(BrokerSettingsResponse) _then;

/// Create a copy of BrokerSettingsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? data = null,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as BrokerSettingsData,
  ));
}
/// Create a copy of BrokerSettingsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BrokerSettingsDataCopyWith<$Res> get data {
  
  return $BrokerSettingsDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _BrokerSettingsResponse implements BrokerSettingsResponse {
  const _BrokerSettingsResponse({required this.success, required this.data});
  factory _BrokerSettingsResponse.fromJson(Map<String, dynamic> json) => _$BrokerSettingsResponseFromJson(json);

@override final  bool success;
@override final  BrokerSettingsData data;

/// Create a copy of BrokerSettingsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BrokerSettingsResponseCopyWith<_BrokerSettingsResponse> get copyWith => __$BrokerSettingsResponseCopyWithImpl<_BrokerSettingsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BrokerSettingsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BrokerSettingsResponse&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'BrokerSettingsResponse(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class _$BrokerSettingsResponseCopyWith<$Res> implements $BrokerSettingsResponseCopyWith<$Res> {
  factory _$BrokerSettingsResponseCopyWith(_BrokerSettingsResponse value, $Res Function(_BrokerSettingsResponse) _then) = __$BrokerSettingsResponseCopyWithImpl;
@override @useResult
$Res call({
 bool success, BrokerSettingsData data
});


@override $BrokerSettingsDataCopyWith<$Res> get data;

}
/// @nodoc
class __$BrokerSettingsResponseCopyWithImpl<$Res>
    implements _$BrokerSettingsResponseCopyWith<$Res> {
  __$BrokerSettingsResponseCopyWithImpl(this._self, this._then);

  final _BrokerSettingsResponse _self;
  final $Res Function(_BrokerSettingsResponse) _then;

/// Create a copy of BrokerSettingsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? data = null,}) {
  return _then(_BrokerSettingsResponse(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as BrokerSettingsData,
  ));
}

/// Create a copy of BrokerSettingsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BrokerSettingsDataCopyWith<$Res> get data {
  
  return $BrokerSettingsDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$BrokerSettingsData {

 List<TradingPlatform> get platforms; List<String> get currencies; ClientPermissions? get clientPermissions;
/// Create a copy of BrokerSettingsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BrokerSettingsDataCopyWith<BrokerSettingsData> get copyWith => _$BrokerSettingsDataCopyWithImpl<BrokerSettingsData>(this as BrokerSettingsData, _$identity);

  /// Serializes this BrokerSettingsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BrokerSettingsData&&const DeepCollectionEquality().equals(other.platforms, platforms)&&const DeepCollectionEquality().equals(other.currencies, currencies)&&(identical(other.clientPermissions, clientPermissions) || other.clientPermissions == clientPermissions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(platforms),const DeepCollectionEquality().hash(currencies),clientPermissions);

@override
String toString() {
  return 'BrokerSettingsData(platforms: $platforms, currencies: $currencies, clientPermissions: $clientPermissions)';
}


}

/// @nodoc
abstract mixin class $BrokerSettingsDataCopyWith<$Res>  {
  factory $BrokerSettingsDataCopyWith(BrokerSettingsData value, $Res Function(BrokerSettingsData) _then) = _$BrokerSettingsDataCopyWithImpl;
@useResult
$Res call({
 List<TradingPlatform> platforms, List<String> currencies, ClientPermissions? clientPermissions
});


$ClientPermissionsCopyWith<$Res>? get clientPermissions;

}
/// @nodoc
class _$BrokerSettingsDataCopyWithImpl<$Res>
    implements $BrokerSettingsDataCopyWith<$Res> {
  _$BrokerSettingsDataCopyWithImpl(this._self, this._then);

  final BrokerSettingsData _self;
  final $Res Function(BrokerSettingsData) _then;

/// Create a copy of BrokerSettingsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? platforms = null,Object? currencies = null,Object? clientPermissions = freezed,}) {
  return _then(_self.copyWith(
platforms: null == platforms ? _self.platforms : platforms // ignore: cast_nullable_to_non_nullable
as List<TradingPlatform>,currencies: null == currencies ? _self.currencies : currencies // ignore: cast_nullable_to_non_nullable
as List<String>,clientPermissions: freezed == clientPermissions ? _self.clientPermissions : clientPermissions // ignore: cast_nullable_to_non_nullable
as ClientPermissions?,
  ));
}
/// Create a copy of BrokerSettingsData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientPermissionsCopyWith<$Res>? get clientPermissions {
    if (_self.clientPermissions == null) {
    return null;
  }

  return $ClientPermissionsCopyWith<$Res>(_self.clientPermissions!, (value) {
    return _then(_self.copyWith(clientPermissions: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _BrokerSettingsData implements BrokerSettingsData {
  const _BrokerSettingsData({required final  List<TradingPlatform> platforms, required final  List<String> currencies, this.clientPermissions}): _platforms = platforms,_currencies = currencies;
  factory _BrokerSettingsData.fromJson(Map<String, dynamic> json) => _$BrokerSettingsDataFromJson(json);

 final  List<TradingPlatform> _platforms;
@override List<TradingPlatform> get platforms {
  if (_platforms is EqualUnmodifiableListView) return _platforms;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_platforms);
}

 final  List<String> _currencies;
@override List<String> get currencies {
  if (_currencies is EqualUnmodifiableListView) return _currencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_currencies);
}

@override final  ClientPermissions? clientPermissions;

/// Create a copy of BrokerSettingsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BrokerSettingsDataCopyWith<_BrokerSettingsData> get copyWith => __$BrokerSettingsDataCopyWithImpl<_BrokerSettingsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BrokerSettingsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BrokerSettingsData&&const DeepCollectionEquality().equals(other._platforms, _platforms)&&const DeepCollectionEquality().equals(other._currencies, _currencies)&&(identical(other.clientPermissions, clientPermissions) || other.clientPermissions == clientPermissions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_platforms),const DeepCollectionEquality().hash(_currencies),clientPermissions);

@override
String toString() {
  return 'BrokerSettingsData(platforms: $platforms, currencies: $currencies, clientPermissions: $clientPermissions)';
}


}

/// @nodoc
abstract mixin class _$BrokerSettingsDataCopyWith<$Res> implements $BrokerSettingsDataCopyWith<$Res> {
  factory _$BrokerSettingsDataCopyWith(_BrokerSettingsData value, $Res Function(_BrokerSettingsData) _then) = __$BrokerSettingsDataCopyWithImpl;
@override @useResult
$Res call({
 List<TradingPlatform> platforms, List<String> currencies, ClientPermissions? clientPermissions
});


@override $ClientPermissionsCopyWith<$Res>? get clientPermissions;

}
/// @nodoc
class __$BrokerSettingsDataCopyWithImpl<$Res>
    implements _$BrokerSettingsDataCopyWith<$Res> {
  __$BrokerSettingsDataCopyWithImpl(this._self, this._then);

  final _BrokerSettingsData _self;
  final $Res Function(_BrokerSettingsData) _then;

/// Create a copy of BrokerSettingsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? platforms = null,Object? currencies = null,Object? clientPermissions = freezed,}) {
  return _then(_BrokerSettingsData(
platforms: null == platforms ? _self._platforms : platforms // ignore: cast_nullable_to_non_nullable
as List<TradingPlatform>,currencies: null == currencies ? _self._currencies : currencies // ignore: cast_nullable_to_non_nullable
as List<String>,clientPermissions: freezed == clientPermissions ? _self.clientPermissions : clientPermissions // ignore: cast_nullable_to_non_nullable
as ClientPermissions?,
  ));
}

/// Create a copy of BrokerSettingsData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientPermissionsCopyWith<$Res>? get clientPermissions {
    if (_self.clientPermissions == null) {
    return null;
  }

  return $ClientPermissionsCopyWith<$Res>(_self.clientPermissions!, (value) {
    return _then(_self.copyWith(clientPermissions: value));
  });
}
}

TradingPlatform _$TradingPlatformFromJson(
  Map<String, dynamic> json
) {
        switch (json['code']) {
                  case 'mt4':
          return Mt4Platform.fromJson(
            json
          );
                case 'mt5':
          return Mt5Platform.fromJson(
            json
          );
                case 'dulcimer':
          return DulcimerPlatform.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'code',
  'TradingPlatform',
  'Invalid union type "${json['code']}"!'
);
        }
      
}

/// @nodoc
mixin _$TradingPlatform {

 AccountCreationPlatform get code; String get name; AccountCreationAvailability get accountCreationAvailability; List<AccountType> get accountTypes;
/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TradingPlatformCopyWith<TradingPlatform> get copyWith => _$TradingPlatformCopyWithImpl<TradingPlatform>(this as TradingPlatform, _$identity);

  /// Serializes this TradingPlatform to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TradingPlatform&&(identical(other.code, code) || other.code == code)&&(identical(other.name, name) || other.name == name)&&(identical(other.accountCreationAvailability, accountCreationAvailability) || other.accountCreationAvailability == accountCreationAvailability)&&const DeepCollectionEquality().equals(other.accountTypes, accountTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,name,accountCreationAvailability,const DeepCollectionEquality().hash(accountTypes));

@override
String toString() {
  return 'TradingPlatform(code: $code, name: $name, accountCreationAvailability: $accountCreationAvailability, accountTypes: $accountTypes)';
}


}

/// @nodoc
abstract mixin class $TradingPlatformCopyWith<$Res>  {
  factory $TradingPlatformCopyWith(TradingPlatform value, $Res Function(TradingPlatform) _then) = _$TradingPlatformCopyWithImpl;
@useResult
$Res call({
 AccountCreationPlatform code, String name, AccountCreationAvailability accountCreationAvailability, List<AccountType> accountTypes
});


$AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability;

}
/// @nodoc
class _$TradingPlatformCopyWithImpl<$Res>
    implements $TradingPlatformCopyWith<$Res> {
  _$TradingPlatformCopyWithImpl(this._self, this._then);

  final TradingPlatform _self;
  final $Res Function(TradingPlatform) _then;

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? name = null,Object? accountCreationAvailability = null,Object? accountTypes = null,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,accountCreationAvailability: null == accountCreationAvailability ? _self.accountCreationAvailability : accountCreationAvailability // ignore: cast_nullable_to_non_nullable
as AccountCreationAvailability,accountTypes: null == accountTypes ? _self.accountTypes : accountTypes // ignore: cast_nullable_to_non_nullable
as List<AccountType>,
  ));
}
/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability {
  
  return $AccountCreationAvailabilityCopyWith<$Res>(_self.accountCreationAvailability, (value) {
    return _then(_self.copyWith(accountCreationAvailability: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class Mt4Platform extends TradingPlatform {
  const Mt4Platform({required this.code, required this.name, required this.accountCreationAvailability, required final  List<AccountType> accountTypes}): _accountTypes = accountTypes,super._();
  factory Mt4Platform.fromJson(Map<String, dynamic> json) => _$Mt4PlatformFromJson(json);

@override final  AccountCreationPlatform code;
@override final  String name;
@override final  AccountCreationAvailability accountCreationAvailability;
 final  List<AccountType> _accountTypes;
@override List<AccountType> get accountTypes {
  if (_accountTypes is EqualUnmodifiableListView) return _accountTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accountTypes);
}


/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$Mt4PlatformCopyWith<Mt4Platform> get copyWith => _$Mt4PlatformCopyWithImpl<Mt4Platform>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$Mt4PlatformToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Mt4Platform&&(identical(other.code, code) || other.code == code)&&(identical(other.name, name) || other.name == name)&&(identical(other.accountCreationAvailability, accountCreationAvailability) || other.accountCreationAvailability == accountCreationAvailability)&&const DeepCollectionEquality().equals(other._accountTypes, _accountTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,name,accountCreationAvailability,const DeepCollectionEquality().hash(_accountTypes));

@override
String toString() {
  return 'TradingPlatform.mt4(code: $code, name: $name, accountCreationAvailability: $accountCreationAvailability, accountTypes: $accountTypes)';
}


}

/// @nodoc
abstract mixin class $Mt4PlatformCopyWith<$Res> implements $TradingPlatformCopyWith<$Res> {
  factory $Mt4PlatformCopyWith(Mt4Platform value, $Res Function(Mt4Platform) _then) = _$Mt4PlatformCopyWithImpl;
@override @useResult
$Res call({
 AccountCreationPlatform code, String name, AccountCreationAvailability accountCreationAvailability, List<AccountType> accountTypes
});


@override $AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability;

}
/// @nodoc
class _$Mt4PlatformCopyWithImpl<$Res>
    implements $Mt4PlatformCopyWith<$Res> {
  _$Mt4PlatformCopyWithImpl(this._self, this._then);

  final Mt4Platform _self;
  final $Res Function(Mt4Platform) _then;

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? name = null,Object? accountCreationAvailability = null,Object? accountTypes = null,}) {
  return _then(Mt4Platform(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,accountCreationAvailability: null == accountCreationAvailability ? _self.accountCreationAvailability : accountCreationAvailability // ignore: cast_nullable_to_non_nullable
as AccountCreationAvailability,accountTypes: null == accountTypes ? _self._accountTypes : accountTypes // ignore: cast_nullable_to_non_nullable
as List<AccountType>,
  ));
}

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability {
  
  return $AccountCreationAvailabilityCopyWith<$Res>(_self.accountCreationAvailability, (value) {
    return _then(_self.copyWith(accountCreationAvailability: value));
  });
}
}

/// @nodoc
@JsonSerializable()

class Mt5Platform extends TradingPlatform {
  const Mt5Platform({required this.code, required this.name, required this.accountCreationAvailability, required final  List<AccountType> accountTypes}): _accountTypes = accountTypes,super._();
  factory Mt5Platform.fromJson(Map<String, dynamic> json) => _$Mt5PlatformFromJson(json);

@override final  AccountCreationPlatform code;
@override final  String name;
@override final  AccountCreationAvailability accountCreationAvailability;
 final  List<AccountType> _accountTypes;
@override List<AccountType> get accountTypes {
  if (_accountTypes is EqualUnmodifiableListView) return _accountTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accountTypes);
}


/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$Mt5PlatformCopyWith<Mt5Platform> get copyWith => _$Mt5PlatformCopyWithImpl<Mt5Platform>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$Mt5PlatformToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Mt5Platform&&(identical(other.code, code) || other.code == code)&&(identical(other.name, name) || other.name == name)&&(identical(other.accountCreationAvailability, accountCreationAvailability) || other.accountCreationAvailability == accountCreationAvailability)&&const DeepCollectionEquality().equals(other._accountTypes, _accountTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,name,accountCreationAvailability,const DeepCollectionEquality().hash(_accountTypes));

@override
String toString() {
  return 'TradingPlatform.mt5(code: $code, name: $name, accountCreationAvailability: $accountCreationAvailability, accountTypes: $accountTypes)';
}


}

/// @nodoc
abstract mixin class $Mt5PlatformCopyWith<$Res> implements $TradingPlatformCopyWith<$Res> {
  factory $Mt5PlatformCopyWith(Mt5Platform value, $Res Function(Mt5Platform) _then) = _$Mt5PlatformCopyWithImpl;
@override @useResult
$Res call({
 AccountCreationPlatform code, String name, AccountCreationAvailability accountCreationAvailability, List<AccountType> accountTypes
});


@override $AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability;

}
/// @nodoc
class _$Mt5PlatformCopyWithImpl<$Res>
    implements $Mt5PlatformCopyWith<$Res> {
  _$Mt5PlatformCopyWithImpl(this._self, this._then);

  final Mt5Platform _self;
  final $Res Function(Mt5Platform) _then;

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? name = null,Object? accountCreationAvailability = null,Object? accountTypes = null,}) {
  return _then(Mt5Platform(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,accountCreationAvailability: null == accountCreationAvailability ? _self.accountCreationAvailability : accountCreationAvailability // ignore: cast_nullable_to_non_nullable
as AccountCreationAvailability,accountTypes: null == accountTypes ? _self._accountTypes : accountTypes // ignore: cast_nullable_to_non_nullable
as List<AccountType>,
  ));
}

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability {
  
  return $AccountCreationAvailabilityCopyWith<$Res>(_self.accountCreationAvailability, (value) {
    return _then(_self.copyWith(accountCreationAvailability: value));
  });
}
}

/// @nodoc
@JsonSerializable()

class DulcimerPlatform extends TradingPlatform {
  const DulcimerPlatform({required this.code, required this.name, required this.accountCreationAvailability, required final  List<AccountType> accountTypes}): _accountTypes = accountTypes,super._();
  factory DulcimerPlatform.fromJson(Map<String, dynamic> json) => _$DulcimerPlatformFromJson(json);

@override final  AccountCreationPlatform code;
@override final  String name;
@override final  AccountCreationAvailability accountCreationAvailability;
 final  List<AccountType> _accountTypes;
@override List<AccountType> get accountTypes {
  if (_accountTypes is EqualUnmodifiableListView) return _accountTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accountTypes);
}


/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DulcimerPlatformCopyWith<DulcimerPlatform> get copyWith => _$DulcimerPlatformCopyWithImpl<DulcimerPlatform>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DulcimerPlatformToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DulcimerPlatform&&(identical(other.code, code) || other.code == code)&&(identical(other.name, name) || other.name == name)&&(identical(other.accountCreationAvailability, accountCreationAvailability) || other.accountCreationAvailability == accountCreationAvailability)&&const DeepCollectionEquality().equals(other._accountTypes, _accountTypes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,name,accountCreationAvailability,const DeepCollectionEquality().hash(_accountTypes));

@override
String toString() {
  return 'TradingPlatform.dulcimer(code: $code, name: $name, accountCreationAvailability: $accountCreationAvailability, accountTypes: $accountTypes)';
}


}

/// @nodoc
abstract mixin class $DulcimerPlatformCopyWith<$Res> implements $TradingPlatformCopyWith<$Res> {
  factory $DulcimerPlatformCopyWith(DulcimerPlatform value, $Res Function(DulcimerPlatform) _then) = _$DulcimerPlatformCopyWithImpl;
@override @useResult
$Res call({
 AccountCreationPlatform code, String name, AccountCreationAvailability accountCreationAvailability, List<AccountType> accountTypes
});


@override $AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability;

}
/// @nodoc
class _$DulcimerPlatformCopyWithImpl<$Res>
    implements $DulcimerPlatformCopyWith<$Res> {
  _$DulcimerPlatformCopyWithImpl(this._self, this._then);

  final DulcimerPlatform _self;
  final $Res Function(DulcimerPlatform) _then;

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? name = null,Object? accountCreationAvailability = null,Object? accountTypes = null,}) {
  return _then(DulcimerPlatform(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,accountCreationAvailability: null == accountCreationAvailability ? _self.accountCreationAvailability : accountCreationAvailability // ignore: cast_nullable_to_non_nullable
as AccountCreationAvailability,accountTypes: null == accountTypes ? _self._accountTypes : accountTypes // ignore: cast_nullable_to_non_nullable
as List<AccountType>,
  ));
}

/// Create a copy of TradingPlatform
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationAvailabilityCopyWith<$Res> get accountCreationAvailability {
  
  return $AccountCreationAvailabilityCopyWith<$Res>(_self.accountCreationAvailability, (value) {
    return _then(_self.copyWith(accountCreationAvailability: value));
  });
}
}


/// @nodoc
mixin _$AccountCreationAvailability {

 bool get enabled;
/// Create a copy of AccountCreationAvailability
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountCreationAvailabilityCopyWith<AccountCreationAvailability> get copyWith => _$AccountCreationAvailabilityCopyWithImpl<AccountCreationAvailability>(this as AccountCreationAvailability, _$identity);

  /// Serializes this AccountCreationAvailability to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountCreationAvailability&&(identical(other.enabled, enabled) || other.enabled == enabled));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enabled);

@override
String toString() {
  return 'AccountCreationAvailability(enabled: $enabled)';
}


}

/// @nodoc
abstract mixin class $AccountCreationAvailabilityCopyWith<$Res>  {
  factory $AccountCreationAvailabilityCopyWith(AccountCreationAvailability value, $Res Function(AccountCreationAvailability) _then) = _$AccountCreationAvailabilityCopyWithImpl;
@useResult
$Res call({
 bool enabled
});




}
/// @nodoc
class _$AccountCreationAvailabilityCopyWithImpl<$Res>
    implements $AccountCreationAvailabilityCopyWith<$Res> {
  _$AccountCreationAvailabilityCopyWithImpl(this._self, this._then);

  final AccountCreationAvailability _self;
  final $Res Function(AccountCreationAvailability) _then;

/// Create a copy of AccountCreationAvailability
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? enabled = null,}) {
  return _then(_self.copyWith(
enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountCreationAvailability implements AccountCreationAvailability {
  const _AccountCreationAvailability({this.enabled = true});
  factory _AccountCreationAvailability.fromJson(Map<String, dynamic> json) => _$AccountCreationAvailabilityFromJson(json);

@override@JsonKey() final  bool enabled;

/// Create a copy of AccountCreationAvailability
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountCreationAvailabilityCopyWith<_AccountCreationAvailability> get copyWith => __$AccountCreationAvailabilityCopyWithImpl<_AccountCreationAvailability>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountCreationAvailabilityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountCreationAvailability&&(identical(other.enabled, enabled) || other.enabled == enabled));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enabled);

@override
String toString() {
  return 'AccountCreationAvailability(enabled: $enabled)';
}


}

/// @nodoc
abstract mixin class _$AccountCreationAvailabilityCopyWith<$Res> implements $AccountCreationAvailabilityCopyWith<$Res> {
  factory _$AccountCreationAvailabilityCopyWith(_AccountCreationAvailability value, $Res Function(_AccountCreationAvailability) _then) = __$AccountCreationAvailabilityCopyWithImpl;
@override @useResult
$Res call({
 bool enabled
});




}
/// @nodoc
class __$AccountCreationAvailabilityCopyWithImpl<$Res>
    implements _$AccountCreationAvailabilityCopyWith<$Res> {
  __$AccountCreationAvailabilityCopyWithImpl(this._self, this._then);

  final _AccountCreationAvailability _self;
  final $Res Function(_AccountCreationAvailability) _then;

/// Create a copy of AccountCreationAvailability
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? enabled = null,}) {
  return _then(_AccountCreationAvailability(
enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$AccountType {

 AccountCreationType get name; String get title; String get subTitle; List<String> get features; List<String> get currencies; List<AccountVariant> get variants; bool get requirePassword; int? get minDeposit;
/// Create a copy of AccountType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountTypeCopyWith<AccountType> get copyWith => _$AccountTypeCopyWithImpl<AccountType>(this as AccountType, _$identity);

  /// Serializes this AccountType to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountType&&(identical(other.name, name) || other.name == name)&&(identical(other.title, title) || other.title == title)&&(identical(other.subTitle, subTitle) || other.subTitle == subTitle)&&const DeepCollectionEquality().equals(other.features, features)&&const DeepCollectionEquality().equals(other.currencies, currencies)&&const DeepCollectionEquality().equals(other.variants, variants)&&(identical(other.requirePassword, requirePassword) || other.requirePassword == requirePassword)&&(identical(other.minDeposit, minDeposit) || other.minDeposit == minDeposit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,title,subTitle,const DeepCollectionEquality().hash(features),const DeepCollectionEquality().hash(currencies),const DeepCollectionEquality().hash(variants),requirePassword,minDeposit);

@override
String toString() {
  return 'AccountType(name: $name, title: $title, subTitle: $subTitle, features: $features, currencies: $currencies, variants: $variants, requirePassword: $requirePassword, minDeposit: $minDeposit)';
}


}

/// @nodoc
abstract mixin class $AccountTypeCopyWith<$Res>  {
  factory $AccountTypeCopyWith(AccountType value, $Res Function(AccountType) _then) = _$AccountTypeCopyWithImpl;
@useResult
$Res call({
 AccountCreationType name, String title, String subTitle, List<String> features, List<String> currencies, List<AccountVariant> variants, bool requirePassword, int? minDeposit
});




}
/// @nodoc
class _$AccountTypeCopyWithImpl<$Res>
    implements $AccountTypeCopyWith<$Res> {
  _$AccountTypeCopyWithImpl(this._self, this._then);

  final AccountType _self;
  final $Res Function(AccountType) _then;

/// Create a copy of AccountType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? title = null,Object? subTitle = null,Object? features = null,Object? currencies = null,Object? variants = null,Object? requirePassword = null,Object? minDeposit = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as AccountCreationType,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subTitle: null == subTitle ? _self.subTitle : subTitle // ignore: cast_nullable_to_non_nullable
as String,features: null == features ? _self.features : features // ignore: cast_nullable_to_non_nullable
as List<String>,currencies: null == currencies ? _self.currencies : currencies // ignore: cast_nullable_to_non_nullable
as List<String>,variants: null == variants ? _self.variants : variants // ignore: cast_nullable_to_non_nullable
as List<AccountVariant>,requirePassword: null == requirePassword ? _self.requirePassword : requirePassword // ignore: cast_nullable_to_non_nullable
as bool,minDeposit: freezed == minDeposit ? _self.minDeposit : minDeposit // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountType implements AccountType {
  const _AccountType({required this.name, required this.title, required this.subTitle, required final  List<String> features, required final  List<String> currencies, required final  List<AccountVariant> variants, this.requirePassword = true, this.minDeposit}): _features = features,_currencies = currencies,_variants = variants;
  factory _AccountType.fromJson(Map<String, dynamic> json) => _$AccountTypeFromJson(json);

@override final  AccountCreationType name;
@override final  String title;
@override final  String subTitle;
 final  List<String> _features;
@override List<String> get features {
  if (_features is EqualUnmodifiableListView) return _features;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_features);
}

 final  List<String> _currencies;
@override List<String> get currencies {
  if (_currencies is EqualUnmodifiableListView) return _currencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_currencies);
}

 final  List<AccountVariant> _variants;
@override List<AccountVariant> get variants {
  if (_variants is EqualUnmodifiableListView) return _variants;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_variants);
}

@override@JsonKey() final  bool requirePassword;
@override final  int? minDeposit;

/// Create a copy of AccountType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountTypeCopyWith<_AccountType> get copyWith => __$AccountTypeCopyWithImpl<_AccountType>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountTypeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountType&&(identical(other.name, name) || other.name == name)&&(identical(other.title, title) || other.title == title)&&(identical(other.subTitle, subTitle) || other.subTitle == subTitle)&&const DeepCollectionEquality().equals(other._features, _features)&&const DeepCollectionEquality().equals(other._currencies, _currencies)&&const DeepCollectionEquality().equals(other._variants, _variants)&&(identical(other.requirePassword, requirePassword) || other.requirePassword == requirePassword)&&(identical(other.minDeposit, minDeposit) || other.minDeposit == minDeposit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,title,subTitle,const DeepCollectionEquality().hash(_features),const DeepCollectionEquality().hash(_currencies),const DeepCollectionEquality().hash(_variants),requirePassword,minDeposit);

@override
String toString() {
  return 'AccountType(name: $name, title: $title, subTitle: $subTitle, features: $features, currencies: $currencies, variants: $variants, requirePassword: $requirePassword, minDeposit: $minDeposit)';
}


}

/// @nodoc
abstract mixin class _$AccountTypeCopyWith<$Res> implements $AccountTypeCopyWith<$Res> {
  factory _$AccountTypeCopyWith(_AccountType value, $Res Function(_AccountType) _then) = __$AccountTypeCopyWithImpl;
@override @useResult
$Res call({
 AccountCreationType name, String title, String subTitle, List<String> features, List<String> currencies, List<AccountVariant> variants, bool requirePassword, int? minDeposit
});




}
/// @nodoc
class __$AccountTypeCopyWithImpl<$Res>
    implements _$AccountTypeCopyWith<$Res> {
  __$AccountTypeCopyWithImpl(this._self, this._then);

  final _AccountType _self;
  final $Res Function(_AccountType) _then;

/// Create a copy of AccountType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? title = null,Object? subTitle = null,Object? features = null,Object? currencies = null,Object? variants = null,Object? requirePassword = null,Object? minDeposit = freezed,}) {
  return _then(_AccountType(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as AccountCreationType,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subTitle: null == subTitle ? _self.subTitle : subTitle // ignore: cast_nullable_to_non_nullable
as String,features: null == features ? _self._features : features // ignore: cast_nullable_to_non_nullable
as List<String>,currencies: null == currencies ? _self._currencies : currencies // ignore: cast_nullable_to_non_nullable
as List<String>,variants: null == variants ? _self._variants : variants // ignore: cast_nullable_to_non_nullable
as List<AccountVariant>,requirePassword: null == requirePassword ? _self.requirePassword : requirePassword // ignore: cast_nullable_to_non_nullable
as bool,minDeposit: freezed == minDeposit ? _self.minDeposit : minDeposit // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$AccountVariant {

 bool get swapFree; int get maxLeverage; List<int> get leverages;
/// Create a copy of AccountVariant
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountVariantCopyWith<AccountVariant> get copyWith => _$AccountVariantCopyWithImpl<AccountVariant>(this as AccountVariant, _$identity);

  /// Serializes this AccountVariant to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountVariant&&(identical(other.swapFree, swapFree) || other.swapFree == swapFree)&&(identical(other.maxLeverage, maxLeverage) || other.maxLeverage == maxLeverage)&&const DeepCollectionEquality().equals(other.leverages, leverages));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,swapFree,maxLeverage,const DeepCollectionEquality().hash(leverages));

@override
String toString() {
  return 'AccountVariant(swapFree: $swapFree, maxLeverage: $maxLeverage, leverages: $leverages)';
}


}

/// @nodoc
abstract mixin class $AccountVariantCopyWith<$Res>  {
  factory $AccountVariantCopyWith(AccountVariant value, $Res Function(AccountVariant) _then) = _$AccountVariantCopyWithImpl;
@useResult
$Res call({
 bool swapFree, int maxLeverage, List<int> leverages
});




}
/// @nodoc
class _$AccountVariantCopyWithImpl<$Res>
    implements $AccountVariantCopyWith<$Res> {
  _$AccountVariantCopyWithImpl(this._self, this._then);

  final AccountVariant _self;
  final $Res Function(AccountVariant) _then;

/// Create a copy of AccountVariant
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? swapFree = null,Object? maxLeverage = null,Object? leverages = null,}) {
  return _then(_self.copyWith(
swapFree: null == swapFree ? _self.swapFree : swapFree // ignore: cast_nullable_to_non_nullable
as bool,maxLeverage: null == maxLeverage ? _self.maxLeverage : maxLeverage // ignore: cast_nullable_to_non_nullable
as int,leverages: null == leverages ? _self.leverages : leverages // ignore: cast_nullable_to_non_nullable
as List<int>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountVariant extends AccountVariant {
  const _AccountVariant({required this.swapFree, required this.maxLeverage, required final  List<int> leverages}): _leverages = leverages,super._();
  factory _AccountVariant.fromJson(Map<String, dynamic> json) => _$AccountVariantFromJson(json);

@override final  bool swapFree;
@override final  int maxLeverage;
 final  List<int> _leverages;
@override List<int> get leverages {
  if (_leverages is EqualUnmodifiableListView) return _leverages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_leverages);
}


/// Create a copy of AccountVariant
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountVariantCopyWith<_AccountVariant> get copyWith => __$AccountVariantCopyWithImpl<_AccountVariant>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountVariantToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountVariant&&(identical(other.swapFree, swapFree) || other.swapFree == swapFree)&&(identical(other.maxLeverage, maxLeverage) || other.maxLeverage == maxLeverage)&&const DeepCollectionEquality().equals(other._leverages, _leverages));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,swapFree,maxLeverage,const DeepCollectionEquality().hash(_leverages));

@override
String toString() {
  return 'AccountVariant(swapFree: $swapFree, maxLeverage: $maxLeverage, leverages: $leverages)';
}


}

/// @nodoc
abstract mixin class _$AccountVariantCopyWith<$Res> implements $AccountVariantCopyWith<$Res> {
  factory _$AccountVariantCopyWith(_AccountVariant value, $Res Function(_AccountVariant) _then) = __$AccountVariantCopyWithImpl;
@override @useResult
$Res call({
 bool swapFree, int maxLeverage, List<int> leverages
});




}
/// @nodoc
class __$AccountVariantCopyWithImpl<$Res>
    implements _$AccountVariantCopyWith<$Res> {
  __$AccountVariantCopyWithImpl(this._self, this._then);

  final _AccountVariant _self;
  final $Res Function(_AccountVariant) _then;

/// Create a copy of AccountVariant
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? swapFree = null,Object? maxLeverage = null,Object? leverages = null,}) {
  return _then(_AccountVariant(
swapFree: null == swapFree ? _self.swapFree : swapFree // ignore: cast_nullable_to_non_nullable
as bool,maxLeverage: null == maxLeverage ? _self.maxLeverage : maxLeverage // ignore: cast_nullable_to_non_nullable
as int,leverages: null == leverages ? _self._leverages : leverages // ignore: cast_nullable_to_non_nullable
as List<int>,
  ));
}


}


/// @nodoc
mixin _$ClientPermissions {

 bool get depositFunds; bool get editProfile; bool get transferFunds; bool get withdrawFunds; bool get changePassword;
/// Create a copy of ClientPermissions
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ClientPermissionsCopyWith<ClientPermissions> get copyWith => _$ClientPermissionsCopyWithImpl<ClientPermissions>(this as ClientPermissions, _$identity);

  /// Serializes this ClientPermissions to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ClientPermissions&&(identical(other.depositFunds, depositFunds) || other.depositFunds == depositFunds)&&(identical(other.editProfile, editProfile) || other.editProfile == editProfile)&&(identical(other.transferFunds, transferFunds) || other.transferFunds == transferFunds)&&(identical(other.withdrawFunds, withdrawFunds) || other.withdrawFunds == withdrawFunds)&&(identical(other.changePassword, changePassword) || other.changePassword == changePassword));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,depositFunds,editProfile,transferFunds,withdrawFunds,changePassword);

@override
String toString() {
  return 'ClientPermissions(depositFunds: $depositFunds, editProfile: $editProfile, transferFunds: $transferFunds, withdrawFunds: $withdrawFunds, changePassword: $changePassword)';
}


}

/// @nodoc
abstract mixin class $ClientPermissionsCopyWith<$Res>  {
  factory $ClientPermissionsCopyWith(ClientPermissions value, $Res Function(ClientPermissions) _then) = _$ClientPermissionsCopyWithImpl;
@useResult
$Res call({
 bool depositFunds, bool editProfile, bool transferFunds, bool withdrawFunds, bool changePassword
});




}
/// @nodoc
class _$ClientPermissionsCopyWithImpl<$Res>
    implements $ClientPermissionsCopyWith<$Res> {
  _$ClientPermissionsCopyWithImpl(this._self, this._then);

  final ClientPermissions _self;
  final $Res Function(ClientPermissions) _then;

/// Create a copy of ClientPermissions
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? depositFunds = null,Object? editProfile = null,Object? transferFunds = null,Object? withdrawFunds = null,Object? changePassword = null,}) {
  return _then(_self.copyWith(
depositFunds: null == depositFunds ? _self.depositFunds : depositFunds // ignore: cast_nullable_to_non_nullable
as bool,editProfile: null == editProfile ? _self.editProfile : editProfile // ignore: cast_nullable_to_non_nullable
as bool,transferFunds: null == transferFunds ? _self.transferFunds : transferFunds // ignore: cast_nullable_to_non_nullable
as bool,withdrawFunds: null == withdrawFunds ? _self.withdrawFunds : withdrawFunds // ignore: cast_nullable_to_non_nullable
as bool,changePassword: null == changePassword ? _self.changePassword : changePassword // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ClientPermissions implements ClientPermissions {
  const _ClientPermissions({required this.depositFunds, required this.editProfile, required this.transferFunds, required this.withdrawFunds, required this.changePassword});
  factory _ClientPermissions.fromJson(Map<String, dynamic> json) => _$ClientPermissionsFromJson(json);

@override final  bool depositFunds;
@override final  bool editProfile;
@override final  bool transferFunds;
@override final  bool withdrawFunds;
@override final  bool changePassword;

/// Create a copy of ClientPermissions
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClientPermissionsCopyWith<_ClientPermissions> get copyWith => __$ClientPermissionsCopyWithImpl<_ClientPermissions>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ClientPermissionsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClientPermissions&&(identical(other.depositFunds, depositFunds) || other.depositFunds == depositFunds)&&(identical(other.editProfile, editProfile) || other.editProfile == editProfile)&&(identical(other.transferFunds, transferFunds) || other.transferFunds == transferFunds)&&(identical(other.withdrawFunds, withdrawFunds) || other.withdrawFunds == withdrawFunds)&&(identical(other.changePassword, changePassword) || other.changePassword == changePassword));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,depositFunds,editProfile,transferFunds,withdrawFunds,changePassword);

@override
String toString() {
  return 'ClientPermissions(depositFunds: $depositFunds, editProfile: $editProfile, transferFunds: $transferFunds, withdrawFunds: $withdrawFunds, changePassword: $changePassword)';
}


}

/// @nodoc
abstract mixin class _$ClientPermissionsCopyWith<$Res> implements $ClientPermissionsCopyWith<$Res> {
  factory _$ClientPermissionsCopyWith(_ClientPermissions value, $Res Function(_ClientPermissions) _then) = __$ClientPermissionsCopyWithImpl;
@override @useResult
$Res call({
 bool depositFunds, bool editProfile, bool transferFunds, bool withdrawFunds, bool changePassword
});




}
/// @nodoc
class __$ClientPermissionsCopyWithImpl<$Res>
    implements _$ClientPermissionsCopyWith<$Res> {
  __$ClientPermissionsCopyWithImpl(this._self, this._then);

  final _ClientPermissions _self;
  final $Res Function(_ClientPermissions) _then;

/// Create a copy of ClientPermissions
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? depositFunds = null,Object? editProfile = null,Object? transferFunds = null,Object? withdrawFunds = null,Object? changePassword = null,}) {
  return _then(_ClientPermissions(
depositFunds: null == depositFunds ? _self.depositFunds : depositFunds // ignore: cast_nullable_to_non_nullable
as bool,editProfile: null == editProfile ? _self.editProfile : editProfile // ignore: cast_nullable_to_non_nullable
as bool,transferFunds: null == transferFunds ? _self.transferFunds : transferFunds // ignore: cast_nullable_to_non_nullable
as bool,withdrawFunds: null == withdrawFunds ? _self.withdrawFunds : withdrawFunds // ignore: cast_nullable_to_non_nullable
as bool,changePassword: null == changePassword ? _self.changePassword : changePassword // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
