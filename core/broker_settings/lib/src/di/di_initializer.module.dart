//@GeneratedMicroModule;BrokerSettingsPackageModule;package:broker_settings/src/di/di_initializer.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:api_client/api_client.dart' as _i633;
import 'package:broker_settings/src/di/broker_settings_module.dart' as _i478;
import 'package:broker_settings/src/domain/repository/broker_settings_repository.dart'
    as _i212;
import 'package:broker_settings/src/domain/usecase/get_broker_currencies_use_case.dart'
    as _i358;
import 'package:broker_settings/src/domain/usecase/get_client_permissions_use_case.dart'
    as _i745;
import 'package:broker_settings/src/domain/usecase/get_create_account_data_use_case.dart'
    as _i88;
import 'package:injectable/injectable.dart' as _i526;
import 'package:user_account/user_account.dart' as _i43;

class BrokerSettingsPackageModule extends _i526.MicroPackageModule {
  // initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final brokerSettingsModule = _$BrokerSettingsModule();
    gh.factory<_i212.BrokerSettingsRepository>(
      () => brokerSettingsModule.profileRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i88.GetCreateAccountDataUseCase>(
      () => brokerSettingsModule.getCreateAccountDataUseCase(
        gh<_i212.BrokerSettingsRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i358.GetBrokerCurrenciesUseCase>(
      () => brokerSettingsModule.getBrokerCurrenciesUseCase(
        gh<_i212.BrokerSettingsRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i745.GetClientPermissionsUseCase>(
      () => brokerSettingsModule.getClientPermissionsUseCase(
        gh<_i212.BrokerSettingsRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
  }
}

class _$BrokerSettingsModule extends _i478.BrokerSettingsModule {}
